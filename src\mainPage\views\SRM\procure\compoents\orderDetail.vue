<template>
  <basic-container style="height: 100%">
    <Title
      >采购详情
      <template #foot>
        <el-button type="primary"  icon="view" @click="toInquiry(form)" v-if="!form.auditStatus && !form.auditType">询价</el-button>
        <el-button
          type="primary"
         
          icon="TakeawayBox"
          @click="wareHousing(row)"
          v-if="(form.orderStatus == 1) "
          >入库</el-button
        >
        <el-button
          type="primary"
         
          icon="TakeawayBox"
          @click="outHouse(row)"
          v-if="(form.inStorageStatus == 1 || form.inStorageStatus == 2) &&  form.outStorageStatus !== 2"
          >出库</el-button
        >
        <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >
    <div class="header">
      <div>
        <h3 style="margin-bottom: 10px" class="title">{{ form.name }}</h3>
        <div class="right"></div>
        <el-form inline label-position="top">
          <el-form-item label="采购订单编号:">
            <el-tag effect='plain' size="large">{{ form.orderNo }}</el-tag>
          </el-form-item>
          <el-form-item label="报价名称">
            <el-tag effect='plain' size="large">{{ form.offerName || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="订单状态">
            <el-tag effect='plain' size="large" v-if="form.orderStatus == 0" type="warning">待采购</el-tag>
        <el-tag effect='plain' size="large" type="warning" v-else-if="form.orderStatus == 2">询价中</el-tag>
        <el-tag effect='plain' size="large" v-if="form.orderStatus == 1" type="warning">采购中</el-tag>
        <el-tag effect='plain' size="large" type="success" v-else-if="form.orderStatus == 3">采购完成</el-tag>
          </el-form-item>
          <el-form-item label="采购类型">
            <el-tag effect='plain' size="large"  type="primary" plain>{{ ['订单采购','项目采购','储备采购'][form.purchaseType] }}</el-tag>
       
          </el-form-item>
          <el-form-item label="审核状态">
            <div v-if="form.auditStatus == 1 || form.auditStatus == 2">
          <el-tag effect='plain' v-if="form.auditStatus == 1" size="large" type="success">审核成功</el-tag>
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="form.auditReason"
            placement="top-start"
            v-if="form.auditStatus == 2"
          >
            <el-tag effect='plain' size="large" type="danger">审核失败</el-tag>
          </el-tooltip>
        </div>
        <div v-else>
          <el-tag effect='plain' v-if="form.auditType == 1" size="large" type="info">待采购主管审核</el-tag>
        
          <el-tag effect='plain' v-if="form.auditType == 2" size="large" type="info">待总经理审核</el-tag>
          <el-tag effect='plain' v-else-if="form.auditType == 0" size="large"  type="info">待提交</el-tag>
         
        </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="btn_group" style="margin-right: 20px; padding-right: 20px">
        <el-button
          type="primary"
          icon="Edit"
          v-if="!isEdit && form.businessPerson == $store.getters.userInfo.user_id"
          @click="isEdit = true"
          >编辑</el-button
        >
        <el-button type="primary" icon="close" v-else-if="isEdit" @click="isEdit = false"
          >取消</el-button
        >
      </div>
    </div>
    <!-- <div style="display: flex">
        <div class="left_content">
          <div class="main_box">
            <div
              class="item"
              v-for="(item, index) in tabArr"
              :class="{ active: currentIndex == index }"
              @click="handleClick(index)"
            >
              <div class="arrow"></div>
              {{ item }}
            </div>
          </div>
        </div>
        <div style="width: calc(100% - 100px)">
          <component
            :is="currentCompoent"
            :form="form"
            :isEdit="isEdit"
            @getDetail="getDetail"
          ></component>
        </div>
      </div> -->
    <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="baseInfo">
        <div>
          <BaseInfo :form="form" v-loading="loading" :isEdit="isEdit"></BaseInfo>
        </div>
      </el-tab-pane>
      <el-tab-pane name="product" label="商品详情"></el-tab-pane>
      <el-tab-pane
        :label="item.label"
        :name="item.name"
        v-for="item in tabArray.filter(item => item.name != 'product')"
        :key="item.name"
      >
      </el-tab-pane>
    </el-tabs>
    <component
      v-if="conpletArr.includes(activeName) && activeName != 'baseInfo'"
      :userId="form.businessPerson"
      :info="{
        type: 3,
        logicId: form.id,
        customerId: form.customerId,
      }"
      :is="tabArray.find(item => item.name == activeName).component"
      :supplierList="form.supplier"
      @update="getDetail"
      :orderId="route.query.id"
    ></component>
    <el-empty v-if="!conpletArr.includes(activeName) && activeName != 'baseInfo'"></el-empty>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, shallowRef, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { computed } from 'vue';
import BaseInfo from '../detail/baseInfo.vue';
// 跟进
import Follow from '@/views/CRM/follow/allFollow.vue';
// 商品信息
import productInfo from '../detail/productInfo.vue';
// 合同信息
import contractInfo from '../contract.vue';
// 入库记录
import wareHouseHistory from '../../warehouse/inhouse.vue';
// 出库记录
import outHouseHistory from '../../warehouse/outhouse.vue';
let route = useRoute();
let router = useRouter();
let form = ref({});
let isEdit = ref(false);
let loading = ref(false);
onMounted(() => {
  getDetail();
});
watchEffect(() => {
  if (route.query.id) {
    getDetail();
  }
});
let activeName = ref('baseInfo');
const conpletArr = ['follow', 'product', 'contract', 'wareHouse', 'outHouse'];
const tabArray = ref([
  {
    label: '商品信息',
    name: 'product',
    component: shallowRef(productInfo),
  },
  {
    label: '采购合同',
    name: 'contract',
    component: shallowRef(contractInfo),
  },
  {
    label: '入库记录',
    name: 'wareHouse',
    component: shallowRef(wareHouseHistory),
  },
  {
    label: '出库记录',
    name: 'outHouse',
    component: shallowRef(outHouseHistory),
  },
  {
    label: '跟进',
    name: 'follow',
    component: shallowRef(Follow),
  },
]);
function formatData(data) {
  const keys = Object.keys(data.detailMap);
  const form = {
    ...data,
  };

  form.supplier = keys.map(item => {
    return {
      supplierName: item,
      products: data.detailMap[item].map(i => {
        console.log(i);
        return {
          ...i.productVO,
          ...i,
        };
      }),
    };
  });
  form.supplier.sort((a, b) => {
    // 如果 a 的 supplierName 是 "暂无供应商"，将其排在前面
    if (a.supplierName === '暂无供应商') {
      return -1;
    }

    // 如果 b 的 supplierName 是 "暂无供应商"，将其排在前面
    if (b.supplierName === '暂无供应商') {
      return 1;
    }

    // 如果都不是 "暂无供应商"，按原始顺序排列
    return 0;
  });
  return form;
}
function getDetail() {
  isEdit.value = false;
  loading.value = true;
  axios
    .get('/api/vt-admin/purchaseOrder/detail', {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
      loading.value = false;

      form.value = formatData(res.data.data);
    });
}
onMounted(() => {
  setTimeout(() => {
    proxy.$nextTick(() => {
      if (route.query.activeName) {
        activeName.value = route.query.activeName;
      }
    });
  }, 200);
});
let moreInfoDetail = ref(false);
function loadMore() {
  moreInfoDetail.value = true;
}
let currentIndex = ref(0);
let currentCompoent = shallowRef(BaseInfo);
function handleClick(value) {
  currentIndex.value = value;
  // currentCompoent.value = compoentArr[value];
}
let { proxy } = getCurrentInstance();
function edit() {
  proxy.$refs.dialogForm.show({
    title: '编辑',
    option: {
      column: [
        {
          label: '商务',
          component: 'wf-user-select',
          prop: 'assistant',
          value: form.value.assistant,
        },
        {
          label: '技术',
          prop: 'technicalPersonnel',
          component: 'wf-user-select',
          value: form.value.technicalPersonnel,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/customer/update', {
          ...res.data,
          id: route.query.id,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getDetail();
        });
    },
  });
}
const store = useStore();
const tag = computed(() => store.getters.tag);
function wareHousing(row) {
  router.push({
    path: '/SRM/warehouse/compoents/addInhouse',
    query: {
      orderId: form.value.id,
    },
  });
  
}
function outHouse(row) {
  router.push({
    path: '/SRM/warehouse/compoents/addOuthouse',
    query: {
      orderId: form.value.id,
    },
  });
}
function toInquiry(row, activeName = null) {
  router.push({
    path: '/SRM/procure/compoents/inquirySheet',
    query: {
      id: row.id,
      type:0
    },
  });
}
</script>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/common/var.scss' as *;
.header {
  display: flex;
  margin-left: 20px;
  justify-content: space-between;
  align-items: flex-start;
}

.title {
  margin: 0;
  margin-right: 20px;
}
.left_content {
  .main_box {
    margin-right: 10px;
    .item {
      width: 100px;
      cursor: pointer;
      background-color: #fff;
      box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.1);
      text-align: center;
      line-height: 50px;
      font-weight: bolder;
      height: 50px;
      font-size: 12px;
      margin-bottom: 10px;
      transition: all 0.2s;
      transition: all 0.2s;
      position: relative;
    }
    .item.active {
      box-shadow: inset 5px 5px 10px rgba(0, 0, 0, 0.1);
      color: $color-primary;
      .arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -12px;
        border: 6px solid transparent;
        border-left-color: $color-primary;
        height: 0;
        width: 0;
      }
    }
  }
}
</style>

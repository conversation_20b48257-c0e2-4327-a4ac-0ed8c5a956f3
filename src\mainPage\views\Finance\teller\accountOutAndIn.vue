<template>
  <basic-container :shadow="!!logicIds || !!accountId ? 'never' : 'always'">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <div style="display: flex; justify-content: flex-start; align-items: center">
          <el-text size="large" style="font-weight: bolder; margin-left: 5px">收入：</el-text>
          <el-text type="danger" size="large">￥{{ (inCome * 1).toLocaleString() }}</el-text>
          <el-text size="large" style="font-weight: bolder; margin-left: 5px">支出：</el-text>
          <el-text type="success" size="large">￥{{ (outCome * 1).toLocaleString() }}</el-text>
          <el-text size="large" style="font-weight: bolder; margin-left: 5px">结余：</el-text>
          <el-text type="primary" size="large">￥{{ (earnings * 1).toLocaleString() }}</el-text>
          <div
            v-if="accountId"
            style="display: flex; align-items: center; margin-left: 10px"
          >
            <span style="font-weight: bolder">账户余额：</span>
            <el-text type="primary" size="large">￥{{ balance.toLocaleString() || 0 }}</el-text>
          </div>
        </div>
      </template>
      <template #files="{ row }">
        <File :fileList="row.attachList"></File>
      </template>
      <template #menu="{ row }">
        <el-button type="primary" @click="handleView(row)" text icon="view">详情</el-button>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer size="80%" title="详情" v-model="drawer">
      <component :logicIds="currentRow.logicIds" :is="detailRef"></component>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import collectForContract from './component/collectForContract.vue';
import collectForOtherCost from './component/collectForOtherCost.vue';
import collectForSupplierBorrow from './component/collectForSupplierBorrow.vue';
import payForContract from './component/payForContract.vue';
import payForReimbursement from './component/payForReimbursement.vue';
import payForSupplierBorrow from './component/payForSupplierBorrow.vue';
import reallocating from './component/reallocating.vue';
import projectPay from './component/projectPay.vue';
import wokerOrderPay from './component/wokerOrderPay.vue';
const props = defineProps(['logicIds', 'accountId']);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 100,
  searchLabelWidth: 100,
  border: true,
  column: [
    {
      type: 'select',
      label: '账户名字',
      dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
      dicFormatter: res => {
        return res.data.records;
      },
      search: !props.logicIds && !props.accountId,
      hide: !!props.accountId,
      props: {
        label: 'abbreviation',
        value: 'id',
        desc: 'desc',
      },
      overHidden: true,
      cascader: [],
      span: 12,

      // search: true,
      display: true,
      formatter: row => {
        return row.accountName;
      },
      prop: 'accountId',
    },
    {
      label: '关联客户',
      prop: 'customerId',
      overHidden: true,
      hide: !!props.logicIds,
      component: 'wf-customer-drop',
      search: !props.logicIds && !props.accountId,
      dataType: 'id',
      formatter: row => {
        return row.customerName;
      },
    },
    {
      label: '关联供应商',
      prop: 'supplierId',
      component: 'wf-supplier-drop',
      overHidden: true,
      hide: !!props.logicIds,
      search: !props.logicIds && !props.accountId,
      dataType: 'id',
      formatter: row => {
        return row.supplierName;
      },
    },
    {
      label: '交易类型',
      prop: 'transactionType',
      type: 'select',
      width: 90,
      search: !props.logicIds && !props.accountId,
      dicData: [
        {
          value: 0,
          label: '收入',
        },
        {
          value: 1,
          label: '支出',
        },
      ],
    },
    {
      label: '交易时间',
      prop: 'transactionTime',
      type: 'date',
      width: 110,
      format: 'YYYY-MM-DD HH:mm',
      searchSpan: 6,
      overHidden: true,
      component: 'wf-daterange-search',
      search: true,
    },
    {
      label: '交易来源',
      prop: 'transactionSource',
      type: 'select',
      width: 120,
      search: !props.logicIds && !props.accountId,
      dicData: [
        {
          value: 0,
          label: '销售合同收款',
        },
        {
          value: 1,
          label: '其他收入',
        },
        {
          value: 2,
          label: '借货押金退还',
        },
        {
          value: 3,
          label: '账户转入',
        },
        {
          value: 4,
          label: '采购合同付款',
        },
        {
          value: 5,
          label: '费用支出',
        },
        {
          value: 6,
          label: '借货押金支出',
        },
        {
          value: 7,
          label: '账户转出',
        },
        {
          value: 8,
          label: '项目人工支出',
        },
        {
          value: 9,
          label: '工单人工支出',
        },
      ],
    },
    {
      label: '金额',
      prop: 'amount',
      html: true,
      width: 110,
      formatter: row => {
        return `<div style='color:var(--el-color-${
          row.transactionType == 0 ? 'danger' : 'success'
        })'>${row.transactionType == 0 ? '+' + row.amount : '-' + row.amount}</div>`;
      },
    },

    {
      label: '账户余额',
      prop: 'balance',
      width: 110,
    },

    {
      label: '操作人',
      prop: 'createUserName',
      width: 90,
    },
    {
      label: '附件',
      prop: 'files',
    },
    {
      label: '备注',
      prop: 'remark',
    },
    // {
    //   label: '联系电话',
    //   prop: 'followPerson',
    // },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/companyAccountTransaction/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
watch(
  () => props.logicIds,
  val => {
    if (val) {
      onLoad();
    }
  }
);
watch(
  () => props.accountId,
  val => {
    if (val) {
      onLoad();
    }
  }
);
let earnings = ref(0);
let balance = ref(0);
let inCome = ref(0);
let outCome = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        logicIds: props.logicIds,
        accountId: props.accountId,
        ...params.value,
        startTime: params.value.transactionTime && params.value.transactionTime[0],
        endTime: params.value.transactionTime && params.value.transactionTime[1],
        transactionTime: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/companyAccountTransaction/pageStatistics', {
      params: {
        size,
        current,
        logicIds: props.logicIds,
        accountId: props.accountId,
        ...params.value,
        startTime: params.value.transactionTime && params.value.transactionTime[0],
        endTime: params.value.transactionTime && params.value.transactionTime[1],
        transactionTime: null,
      },
    })
    .then(res => {
      earnings.value = res.data.data.earnings;
      balance.value = res.data.data.balance;
      inCome.value = res.data.data.inCome;
      outCome.value = res.data.data.outCome;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
let drawer = ref(false);
let currentRow = ref({});
let detailRef = ref('');
function handleView(row) {
  drawer.value = true;
  detailRef.value = '';
  currentRow.value = row;
  switch (row.transactionSource * 1) {
    case 0:
      detailRef.value = collectForContract;
      break;
    case 1:
      detailRef.value = collectForOtherCost;
      break;
    case 2:
      detailRef.value = collectForSupplierBorrow;
      break;
    case 3:
      detailRef.value = reallocating;
      break;
    case 4:
      detailRef.value = payForContract;
      break;
    case 5:
      detailRef.value = payForReimbursement;
      break;
    case 6:
      detailRef.value = payForSupplierBorrow;
      break;
    case 7:
      detailRef.value = reallocating;
      break;
    case 8:
      detailRef.value = projectPay;
      break;
    case 9:
      detailRef.value = wokerOrderPay;
      break;

    default:
      break;
  }
}
</script>

<style lang="scss" scoped></style>

<template>
  <basic-container>
    <Title
      >新增入库
      <template #foot>
        <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >
    <avue-form
      :option="option"
      ref="addFormRef"
      @submit="submit"
      style="margin-top: 5px"
      v-model="form"
    >
      <template #files v-if="route.query.type == 'detail'">
        <File></File>
      </template>
      <template #product>
        <el-form style="margin-bottom: 5px" inline v-if="form.inStorageType != 0">
          <el-form-item label="">
            <el-button type="primary" icon="plus" size="small" plain @click="addProduct"
              >新增</el-button
            >
          </el-form-item>
          <el-form-item label="">
            <productSelectDrop @select="handleProductSelectConfirm"></productSelectDrop>
          </el-form-item>
        </el-form>
        <el-table class="avue-crud" :data="form.detailDTOList" border align="center">
          <el-table-column label="序号" type="index" :index="1" width="80">
            <template #header>
              <el-button
                type="primary"
                circle
                icon="plus"
                @click="addProduct"
                v-if="form.inStorageType != 0"
              ></el-button>
            </template>
          </el-table-column>
          <el-table-column
            label="设备名称"
            show-overflow-tooltip
            prop="productName"
          ></el-table-column>
          <el-table-column
            label="供应商"
            show-overflow-tooltip
            prop="supplierName"
            #default="{ row }"
          >
            <WfSupplierSelect
              v-if="form.inStorageType != 0"
              v-model="row.supplierId"
              placeholder="请选择供应商"
              size="small"
              style="width: 100%"
            ></WfSupplierSelect>
            <span v-else>{{ row.supplierName }}</span>
          </el-table-column>
          <el-table-column
            label="规格型号"
            show-overflow-tooltip
            prop="productSpecification"
          ></el-table-column>
          <el-table-column label="产品图片" #default="{ row }">
            <el-image
              style="width: 80px"
              :preview-src-list="[row.coverUrl]"
              :src="row.coverUrl"
            ></el-image>
          </el-table-column>
          <el-table-column label="品牌" width="90" prop="productBrand"></el-table-column>
          <el-table-column label="单位" prop="unitName"></el-table-column>
          <el-table-column
            label="订单数量"
            width="100"
            v-if="form.inStorageType == 0"
            #default="{ row }"
            prop="number"
          >
          </el-table-column>
          <el-table-column
            label="单价"
            v-if="form.inStorageType !== 0"
            #default="{ row }"
            align="center"
           
            prop="costPrice"
          >
            <el-input-number
              size="small"
                controls-position="right"
              v-model="row.costPrice"
              style="width: 100%"
              placeholder=""
            ></el-input-number>
          </el-table-column>

          <el-table-column
            label="已入库数量"
            v-if="form.inStorageType == 0"
            #default="{ row }"
            prop="arriveNumber"
          >
          </el-table-column>

          <!-- <el-table-column label="采购价" width="80" #default="{ row }" prop="unitPrice">
            <span>{{ row.unitPrice }}</span>
          </el-table-column>
          <el-table-column label="采购总价" width="90" #default="{ row }" prop="totalPrice">
            {{ row.unitPrice ? row.number * row.unitPrice : '---' }}
          </el-table-column> -->
          <el-table-column label="入库数量" align="center" width="180" #default="{ row }">
            <div
              v-if="(row.isShouldOut == 1 && form.inStorageType == 0) || form.inStorageType != 0"
            >
              <el-input-number
                controls-position="right"
                :max="row.number * 1 - row.arriveNumber * 1"
                :min="0"
               
                size="small"
                v-if="!row.splitList || (row.splitList && row.splitList.length === 0)"
                v-model="row.inStorageNumber"
              ></el-input-number>
              <el-text v-else type="primary">{{ row.splitList.length }}</el-text>
            </div>
          </el-table-column>
          <el-table-column label="入库地点" align="center" width="200" #default="{ row }">
            <div
              v-if="(row.isShouldOut == 1 && form.inStorageType == 0) || form.inStorageType != 0"
            >
              <el-radio-group
                v-model="row.inStorageAddress"
                v-if="!row.splitList || (row.splitList && row.splitList.length === 0)"
                style="display: flex"
              >
                <el-radio :label="0">公司</el-radio>
                <el-radio :label="1">客户现场</el-radio>
              </el-radio-group>
              <el-tag effect="plain" v-else type="primary">拆分入库</el-tag>
            </div>
            <div v-else>
              <el-alert title="未签合同,无法入库" :closable="false" size="small" type="info" />
            </div>
          </el-table-column>
          <el-table-column label="操作" #default="{ row, $index }" width="240" align="center">
            <div
              v-if="(row.isShouldOut == 1 && form.inStorageType == 0) || form.inStorageType != 0"
            >
              <!-- <el-button type="primary" text icon="Switch" @click="hanldeSplit(row, $index)"
                >拆分入库</el-button
              > -->
              <el-button type="primary" text icon="CopyDocument" @click="handleCopy(row, $index)"
                >粘贴序列号</el-button
              >
              <el-button type="primary" text icon="delete" @click="deleteProduct(row, $index)"
                >删除</el-button
              >
            </div>
          </el-table-column>
        </el-table>
      </template>
    </avue-form>
    <el-dialog
      :show-close="false"
      :close-on-click-modal="false"
      title="拆分入库"
      v-model="dialogVisible"
      class="avue-dialog avue-dialog--top"
    >
      <el-alert
        ><template #title
          >你最多可以拆分
          <span style="color: var(--el-color-primary)">{{
            isNaN(
              form.detailDTOList[currentIndex].number * 1 -
                form.detailDTOList[currentIndex].arriveNumber * 1
            )
              ? `--`
              : form.detailDTOList[currentIndex].number * 1 -
                form.detailDTOList[currentIndex].arriveNumber * 1
          }}</span>
          条</template
        ></el-alert
      >
      <avue-form
        :option="addOption"
        ref="dialogForm"
        v-model="addForm"
        @submit="submit"
      ></avue-form>
      <!-- <slot ></slot> -->
      <div class="avue-dialog__footer">
        <el-button @click="handleCancle">取 消</el-button>
        <el-button @click="handleconfirm" type="primary">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :show-close="false"
      :close-on-click-modal="false"
      title="粘贴"
      v-model="clipboardVisible"
      class="avue-dialog avue-dialog--top"
      @close="splitData = ''"
    >
      <el-form>
        <el-form-item label="序列号">
          <el-input
            v-model="clipboardText"
            placeholder="请粘贴序列号"
            rows="15"
            @input="handleData"
            type="textarea"
          ></el-input>
        </el-form-item>
      </el-form>
      <el-alert
        type="warning"
        style="margin-bottom: 5px"
        :closable="false"
        v-for="item in duplicData"
        >{{ `${item.value}重复`
        }}<span style="color: var(--el-color-primary)">{{ `${item.count}` }}</span
        >次</el-alert
      >
      <!-- <slot ></slot> -->
      <div class="avue-dialog__footer">
        <el-button @click="clipboardVisible = false">取 消</el-button>
        <!-- <el-button @click="handleDuplic" plain type="primary">去 重</el-button> -->
        <el-button @click="handleCliboardConfirm" type="primary">确 定</el-button>
      </div>
    </el-dialog>
    <div style="margin-top: 20px; padding-left: 75px">
      <!-- <el-form>
        <el-form-item label="">
          <el-button type="primary" icon="plus" size="small" @click.stop="addProduct" plain
            >新增</el-button
          >
        </el-form-item>
      </el-form> -->
      <!-- <el-table :data="form.detailDTOList" border show-summary :summary-method="productSum">
        <el-table-column label="设备名称" prop="productName"></el-table-column>
        <el-table-column label="规格型号" prop="productSpecification"></el-table-column>
        <el-table-column label="产品图片" #default="{ row }">
          <img style="width: 200px; margin-right: 20px" :src="row.coverUrl" />
        </el-table-column>
        <el-table-column
          label="产品描述"
          show-overflow-tooltip
          width="200"
          prop="description"
        ></el-table-column>
        <el-table-column label="品牌" prop="productBrand"></el-table-column>
        <el-table-column label="单位" prop="unitName"></el-table-column>
        <el-table-column label="入库数量" #default="{ row }" prop="number">
          <el-input v-model="row.number" size="small" style="width: 80%"></el-input>
        </el-table-column>
        <el-table-column label="采购价" width="150" #default="{ row }" prop="unitPrice">
          <el-input
            v-model="row.unitPrice"
            size="small"
            style="width: 80%"
            placeholder="请输入采购价"
          ></el-input>
        </el-table-column>
        <el-table-column label="采购总价" width="90" #default="{ row }" prop="totalPrice">
          {{ row.unitPrice ? row.number * row.unitPrice : '---' }}
        </el-table-column>
        <el-table-column label="操作" align="center" prop="" width="200" #default="{ row, index }">
          <el-button type="primary" text icon="delete" @click="deleteProduct(row)">删除</el-button>
        </el-table-column>
      </el-table> -->
    </div>
    <!-- 产品选择弹窗 -->
    <wf-product-select
      ref="product-select"
      check-type="box"
      @onConfirm="handleProductSelectConfirm"
    ></wf-product-select>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import { getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import WfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import WfSupplierSelect from '@/views/plugin/workflow/components/custom-fields/wf-supplier-select/index.vue';
import { SetOperationEnum } from 'element-plus/es/components/tree-v2/src/virtual-tree';
import productSelectDrop from '@/views/CRM/quotation/compoents/productSelectDrop.vue';
let route = useRoute();
let router = useRouter();
let form = ref({
  detailDTOList: [],
});
const inStorageType = ref(route.query.inStorageType * 1 || 0);
let { proxy } = getCurrentInstance();
let isEdit = ref(true);
let option = ref({
  submitBtn: route.query.type !== 'detail',
  labelWidth: 140,
  detail: route.query.type == 'detail',
  emptyBtn: route.query.type !== 'detail',
  column: [
    {
      label: '入库人',
      prop: 'userName',
      disabled: true,
      value: proxy.$store.getters.userInfo.real_name,
    },
    {
      label: '入库日期',
      prop: 'date',
      type: 'date',
      readonly: true,
      disabled: true,
      // format: 'YYYY-MM-DD',
      // valueFormat: 'YYYY-MM-DD',
      value: new Date(),
    },
    {
      label: '入库类型',
      prop: 'inStorageType',
      type: 'radio',
      value: inStorageType.value,
      dicData: [
        {
          value: 0,
          label: '订单入库',
        },
      
        {
          value: 2,
          label: '客户退货',
        },
        {
          value: 3,
          label: '期初入库',
        },
        {
          value: 4,
          label: '其他',
        },
      ],
      rules: [{ required: true, message: '请选择入库类型' }],
      control: val => {
        form.value.detailDTOList = [];
        return {
          orderId: {
            display: val == 0,
          },
        };
      },
    },
    {
      label: '关联订单',
      prop: 'orderId',
      value: route.query.orderId,
      span: 24,
      component: 'wf-order-select',
      control: val => {
        if (!val) return;
        getDetailList(val);
      },
      rules: [
        {
          required: true,
        },
      ],
      // format: 'YYYY-MM-DD',
      // valueFormat: 'YYYY-MM-DD',
    },

    {
      label: '附件',
      prop: 'files',
      type: 'upload',
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: false,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea',
      span: 24,
    },
    {
      label: '产品信息',
      prop: 'product',
      slot: true,
      span: 24,
    },
  ],
});
watchEffect(() => {
  if (route.query.id) {
    getDetailList(route.query.id);
  }
  if (route.query.businessId) {
    getBusinessDetail(route.query.businessId);
  }
});

function formatData(data) {
  return data.reduce((pre, cur) => {
    if (!cur.splitList) {
      pre.push(cur);
      return pre;
    }
    if (cur.splitList.length > 0) {
      pre.push(...cur.splitList);
    } else {
      pre.push(cur);
    }
    return pre;
  }, []);
}
function submit(form, done) {
  const data = {
    orderId: form.orderId,
    files: form.files.map(item => item.value).join(','),
    remark: form.remark,
    inStorageType: form.inStorageType,
    detailDTOList: formatData(form.detailDTOList)
      .map(item => {
        return {
          orderDetailId: item.id,
          id: null,
          contractDetailId: item.contractDetailId,
          ...item,
          number: item.inStorageNumber,
          inStorageAddress: item.inStorageAddress,
        };
      })
      .filter(item => item.number != 0),
  };
  axios.post('/api/vt-admin/purchaseInStorage/save', data).then(
    res => {
      proxy.$message.success('入库成功');
      proxy.$router.$avueRouter.closeTag();
      proxy.$router.back();
    },
    err => {
      done();
    }
  );
}
function getDetail(id) {
  axios
    .get('/api/vt-admin/purchaseInStorage/detail', {
      params: {
        id: id,
      },
    })
    .then(res => {
      form.value = res.data.data;
    });
}

function getDetailList(id) {
  axios
    .get('/api/vt-admin/purchaseOrder/detailForInStorage', {
      params: {
        id,
      },
    })
    .then(res => {
      form.value.detailDTOList = res.data.data.detailList.map(item => {
        return {
          ...item.productVO,
          ...item,
          inStorageNumber: 0,
          inStorageAddress: 0,
        };
      });
    });
}

// 拆分
let dialogVisible = ref(false);
const addOption = ref({
  submitBtn: false,
  emptyBtn: false,
  size: 'small',
  column: [
    {
      label: '',
      prop: 'splitList',
      type: 'dynamic',
      size: 'small',
      span: 24,
      cell: false,
      labelWidth: 0,
      children: {
        align: 'center',
        headerAlign: 'center',
        size: 'small',
        rowAdd: done => {
          if (
            form.value.detailDTOList[currentIndex.value].number * 1 -
              form.value.detailDTOList[currentIndex.value].arriveNumber * 1 ==
            form.value.detailDTOList[currentIndex.value].splitList.length
          )
            return proxy.$message.warning('请勿超过最大拆分数量');
          // splitProduct();
          const {
            id,
            supplierName,
            productName,
            supplierId,
            contractDetailId,
            productVO,
            productId,
          } = form.value.detailDTOList[currentIndex.value];
          done({
            id,
            supplierName,
            supplierId,
            productName,
            contractDetailId,
            inStorageAddress: 0,
            productId: productVO ? productVO.id : productId,
            uuid: Date.now(),
            inStorageNumber: 1,
          });
        },
        rowDel: (row, done) => {
          done();
        },
        limit: 1,
        column: [
          {
            label: '产品名字',
            prop: 'productName',
            overHidden: true,
            cell: false,
          },
          {
            label: '供应商',
            prop: 'supplierId',
            component: 'wf-supplier-select',
          },
          {
            label: '序列号',
            width: 250,
            prop: 'productSerialNumber',
          },
          {
            label: '入库数量',

            prop: 'inStorageNumber',
            type: 'number',
            cell: false,
          },
          {
            label: '入库地点',
            prop: 'inStorageAddress',
            type: 'radio',
            value: 0,
            dicData: [
              {
                value: 0,
                label: '公司',
              },
              {
                value: 1,
                label: '客户现场',
              },
            ],
          },
        ],
      },
    },
  ],
});
let addForm = ref({
  splitList: [],
});
let currentIndex = ref(null);
function hanldeSplit(row, index) {
  currentIndex.value = index;
  if (!form.value.detailDTOList[currentIndex.value].splitList) {
    form.value.detailDTOList[currentIndex.value].splitList = [];
  }
  console.log(index, row.splitList);
  dialogVisible.value = true;
  console.log(addForm.value);

  addForm.value.splitList = form.value.detailDTOList[currentIndex.value].splitList;
}
function handleCancle() {
  proxy.$confirm('确认取消拆分吗？', '提示', { type: 'warning' }).then(() => {
    form.value.detailDTOList[currentIndex.value].splitList = null;
    dialogVisible.value = false;
  });
}
function handleconfirm() {
  form.value.detailDTOList[currentIndex.value].splitList = addForm.value.splitList;
  dialogVisible.value = false;
  dialogVisible.value = false;
}
function addProduct() {
  proxy.$refs['product-select'].visible = true;
}
function handleProductSelectConfirm(ids) {
  ids.split(',').forEach(item => {
    axios.get('/api/vt-admin/product/detail?id=' + item).then(r => {
      form.value.detailDTOList.push({
        ...r.data.data,
        productId: r.data.data.id,
        preSealPrice: r.data.data.sealPrice,
        unitPrice: r.data.data.purchasePrice,
        sealPrice: '',
        number: 1,
        id: null,
        inStorageAddress: 0,
        supplierId: null,
      });
    });
  });
}

let clipboardVisible = ref(false);
let clipboardText = ref('');
let duplicData = ref([]);
let splitData = ref([]);
function handleCopy(row, index) {
  currentIndex.value = index;
  clipboardVisible.value = true;
  clipboardText.value = row.productSerialNumber;
  if (!clipboardText.value) {
    navigator.clipboard
      .readText()
      .then(clipboardData => {
        proxy.$message.success('自动读取粘贴板内容');
        console.log('剪贴板内容:', clipboardData);
        console.log();

        clipboardText.value = clipboardData;
        handleData();
      })
      .catch(err => {
        console.error('无法读取剪贴板内容:', err);
      });
  } else {
    handleData();
  }
  console.log(form.value.detailDTOList[currentIndex.value]);
}
function handleData() {
  splitData.value = clipboardText.value.split('\n').map(item => {
    return item.replace('\r', '');
  });

  duplicData.value = countDuplicates(splitData.value); // 统计重复数据
}
function handleCliboardConfirm() {
  const { number, arriveNumber, inStorageNumber } = form.value.detailDTOList[currentIndex.value];
  const maxNum = number - arriveNumber;
  console.log(splitData.value);
  if (splitData.value.length != inStorageNumber)
    return proxy.$message.error('序列号数量和入库数量不一致，请核对');
  // const { id, supplierName, productName, supplierId, contractDetailId, productVO,productId } =
  //   form.value.detailDTOList[currentIndex.value];
  // form.value.detailDTOList[currentIndex.value].splitList = splitData.value.map(item => {
  //   return {
  //     id,
  //     supplierName,
  //     supplierId,
  //     productName,
  //     contractDetailId,
  //     productId: productVO? productVO.id : productId,
  //     inStorageAddress:0,
  //     uuid: Date.now(),
  //     inStorageNumber: 1,
  //     productSerialNumber: item,
  //   };
  // });
  // addForm.value.splitList = form.value.detailDTOList[currentIndex.value].splitList;
  form.value.detailDTOList[currentIndex.value].productSerialNumber = clipboardText.value;
  // dialogVisible.value = true;
  clipboardVisible.value = false;
}
function countDuplicates(arr) {
  const countMap = {};

  arr.forEach(item => {
    if (countMap[item]) {
      // 如果值已存在于countMap中，则增加计数
      countMap[item]++;
    } else {
      // 否则将其添加到countMap中，初始计数为1
      countMap[item] = 1;
    }
  });

  // 从countMap中提取重复值和它们的计数
  const result = Object.entries(countMap)
    .filter(([key, value]) => value > 1)
    .map(([key, value]) => ({ value: key, count: value }));

  return result;
}
// function handleDuplic() {
//   const newData =
// }
function deleteProduct(row, index) {
  proxy
    .$confirm('是否删除该产品?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      form.value.detailDTOList.splice(index, 1);
    })
    .catch(() => {});
}
</script>

<style lang="scss" scoped></style>

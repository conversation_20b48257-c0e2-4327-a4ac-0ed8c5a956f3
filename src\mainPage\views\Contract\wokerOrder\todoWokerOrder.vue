<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :permission="permission"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
      ><template #menu-left>
         <div class="stats-container" style="display: flex; ">
            <div class="stat-item">
              <div class="stat-label">
                <el-icon><document /></el-icon>
                工单总数
              </div>
              <el-text class="stat-value" type="primary">{{ totalNumber }}</el-text>
            </div>

            <div class="stat-item">
              <div class="stat-label">
                <el-icon><alarm-clock /></el-icon>
                总工时
              </div>
              <el-text class="stat-value" type="info">{{ totalTimes }}h</el-text>
            </div>
          </div>
      </template>
      <template #menu="{ row }">
        <el-button
          type="primary"
          icon="edit"
          text
          v-if="row.objectStatus == 1"
          @click="completeTask(row)"
          >完成</el-button
        >
        <el-button type="primary" icon="edit" text v-if="row.objectStatus == 3" @click="accept(row)"
          >接单</el-button
        >
      </template>
      <template #externalHandleUser-form="{ type }">
        <div v-if="type !== 'view'" style="display: flex; align-items: center">
          <div>
            <el-avatar
              shape="circle"
              :size="60"
              style="cursor: pointer; position: relative; overflow: visible; margin-right: 5px"
              v-for="item in form.externalHandleUserList"
            >
              <el-text style="font-weight: bolder; color: #fff">{{ item.realName }}</el-text>
              <!-- <el-icon class="closeIcon" @click="handleDelete(item)" :size="20"
          ><CircleCloseFilled
        /></el-icon> -->
            </el-avatar>
          </div>
          <div
            style="
              display: flex;
              flex-direction: column;
              align-items: center;

              justify-content: center;
            "
          >
            <div>
              <el-button type="primary" icon="plus" @click="handleAddIn" text
                >添加内部工程师</el-button
              >
            </div>
            <div>
              <el-button type="primary" icon="plus" @click="handleAddOut" text
                >添加外部工程师</el-button
              >
            </div>
          </div>
        </div>
        <el-text style="padding-left: 15px" disabled v-else>
          {{ form.handleUserName }}
        </el-text>
      </template>
      <template #objectStatus="{ row }">
        <el-tag :type="['info', 'warning', 'success', 'info'][row.objectStatus]" effect="plain">{{
          row.$objectStatus
        }}</el-tag>
      </template>
      <template #objectName="{ row }">
        <el-link type="primary" @click="$refs.crud.rowView(row)" target="_blank">{{
          row.objectName
        }}</el-link>
      </template>
      <template #filesDetail-form>
        <File :fileList="form.fileList"></File>
      </template>
      <template #completeFiles-form>
        <File :fileList="form.completeFilesList"></File>
      </template>
      <template #taskName-form>
        <el-descriptions :title="form.labelName">
          <el-descriptions-item label="要求交付时间">{{
            (form.planStartTime && form.planStartTime.split(' ')[0]) || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="服务客户">{{
            form.finalCustomer || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="服务联系人">{{ form.contact || '--' }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{
            form.contactPhone || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="服务地址" :span="2">
            {{ form.distributionAddress || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="任务描述" :span="3">
            <span style="white-space: pre-line"> {{ form.taskDescription || '--' }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <wfUserSelect
      userUrl="/api/blade-system/search/user?roleKeys=ywzy,swzy,technology_leader,technology_person"
      ref="userSelectRef"
      @onConfirm="handleConfirm"
    ></wfUserSelect>
    <humanSelect ref="userSelectRefOut" @select="handleConfirmOut"></humanSelect>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import wfUserSelect from '@/views/plugin/workflow/components/nf-user-select/index.vue';
import userSelect from './component/userSelect.vue';
import { dateFormat } from '@/utils/date.js';
import humanSelect from '@/views/Project/compoents/humanSelect.vue';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 120,
  labelWidth: 120,
  dialogWidth: '1000',
  dialogType: 'drawer',
  border: true,

  column: [
    // {
    //   label: '工单编号',
    //   prop: 'objectNo',
    //   display: false,
    // },

    {
      label: '任务类型',
      prop: 'lables',
      placeholder: '请输入工单类型',
      type: 'tree',
      display: false,
      props: {
        value: 'id',
        label: 'dictValue',
      },
      dicUrl: '/blade-system/dict-biz/dictionary?code=wokerOrderType',
    },
    {
      label: '工单名称',
      prop: 'objectName',
      search: true,
      display: false,
    },
    {
      label: '关联合同',
      prop: 'sealContractId',
      placeholder: '请选择关联合同',
      addDisplay: true,
      editDisplay: false,
      component: 'wf-contract-select',
      params: {
        Url: '/api/vt-admin/sealContract/pageForObject?selectType=5',
      },
      change: val => {
        setBaseInfo(val.value);
      },
      formatter: row => {
        return row.contractName;
      },
    },
    {
      label: '里程碑',
      hide: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      labelWidth: 0,
    },
    {
      label: '服务时间',
      prop: 'planTime',
      type: 'date',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      span: 12,
      width: 150,
      labelWidth: 110,
      display: false,
    },
    {
      label: '发布时间',
      prop: 'createTime',
      type: 'date',
      overHidden: true,
      span: 12,
      component: 'wf-daterange-search',
      searchSpan: 6,
      search: true,
      width: 150,
      labelWidth: 110,
      display: false,
    },
  {
      label: '实际完成时间',
      prop: 'completeTime',
      type: 'date',
      overHidden: true,
      span: 12,
      format: 'YYYY-MM-DD HH:mm',
      searchSpan: 6,
     
      width: 150,
      labelWidth: 110,
      display: false,
    },
    {
      label: '使用工时',
      prop: 'useTimes',
      display: false,
      width: 110,
    },
    {
      label: '派单人',
      prop: 'projectLeaderName',
      width: 110,
      display: false,
      // search: true,
    },

    {
      label: '工单状态',
      prop: 'objectStatus',
      type: 'select',
      search: true,
      display: false,
      width: 110,
      dicData: [
        {
          label: '待派单',

          value: 0,
        },
        {
          label: '待接单',
          value: 3,
        },
        {
          label: '处理中',
          value: 1,
        },
        {
          label: '处理完成',
          value: 2,
        },
      ],
    },
  ],
  group: [
    {
      label: '任务信息',
      prop: 'taskInfo',
      addDispla: true,
      disabled: true,
      column: [
        {
          labelWidth: 0,
          span: 24,
          prop: 'taskName',
        },
      ],
    },
    {
      label: '工单信息',
      prop: 'workOrderInfo',
      column: [
        {
          label: '工单名称',
          prop: 'objectName',
          placeholder: '请输入工单名称',
          span: 12,
          type: 'input',
        },
        {
          type: 'datetime',
          label: '服务时间',
          span: 12,

          width: 140,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'planTime',
          rules: [
            {
              required: true,
              message: '请选择服务时间',
              trigger: 'blur',
            },
          ],
        },

        {
          label: '工单价格',
          prop: 'orderPrice',
          placeholder: '请输入工单价格',
          type: 'number',
          span: 12,
        },
        {
          label: '工单描述',
          prop: 'remark',
          placeholder: '请输入工单描述',
          type: 'textarea',
          span: 24,
        },
        {
          label: '工单附件',
          prop: 'files',
          type: 'upload',
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          addDisplay: true,
          editDisplay: true,
          viewDisplay: false,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '工单附件',
          prop: 'filesDetail',
          type: 'upload',
          addDisplay: false,
          editDisplay: false,
          viewDisplay: true,
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '指派工程师',
          prop: 'externalHandleUser',
          component: 'wf-user-select',
          rules: [
            {
              required: true,
              message: '请选择工程师',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    {
      label: '完成信息',
      prop: 'finishInfo',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,

       column: [
        {
          type: 'date',
          label: '实际完成时间',
          span: 12,
          value: dateFormat(new Date(), 'yyyy-MM-dd'),
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'completeTime',
        },
        {
          label: '实际使用工时',
          prop: 'useTimes',
          type: 'number',
        },
        {
          label: '附件',
          prop: 'completeFiles',
          type: 'upload',
          overHidden: true,
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          slot: true,
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '服务复盘',
          prop: 'serviceReorder',
          type: 'textarea',
         
          span: 24,
          value: '问题描述:\n\n原因分析:\n\n解决方案:\n\n服务成果:\n',
        },
        {
          label: '备注',
          prop: 'completeRemark',
          type: 'textarea',
          span: 24,
        },
      ],
    },
  ],
});
let form = ref({
  externalHandleUserList: [],
});
/*
设置基础信息
*/
function setBaseInfo(id) {
  axios
    .get('/api/vt-admin/sealContract/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      form.value.contact = res.data.data.finalCustomerConcat;
      form.value.finalCustomer = res.data.data.finalCustomer;
      form.value.contactPhone = res.data.data.finalCustomerPhone;
      form.value.distributionAddress = res.data.data.deliveryAddress;
      option.value.group[0].display = false;
    });
}

// 添加内部工程师
function handleAddIn() {
  form.value.externalHandleUserList = [];
  proxy.$refs.userSelectRef.visible = true;
}
function handleConfirm(id) {
  axios
    .get('/api/blade-system/user/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      if (!form.value.externalHandleUserList) {
        form.value.externalHandleUserList = [res.data.data];
      } else {
        form.value.externalHandleUserList.push(res.data.data);
      }
      form.value.externalHandleUser = form.value.externalHandleUserList
        .map(item => item.id)
        .join(',');
    });
}
function handleConfirmOut(data) {
  ;

  if (!form.value.externalHandleUserList) {
    form.value.externalHandleUserList = [
      {
        ...data,
        realName: data.name,
      },
    ];
  } else {
    form.value.externalHandleUserList.push({
      ...data,
      realName: data.name,
    });
  }
  form.value.externalHandleUser = form.value.externalHandleUserList.map(item => item.id).join(',');
  console.log(
    form.value.externalHandleUserList.map(item => {
      ;
      return item.id;
    })
  );
}
function handleAddOut() {
  form.value.externalHandleUserList = [];
  proxy.$refs.userSelectRefOut.open();
}
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let crud = ref(null);
onMounted(() => {
  // crud.value.rowAdd();
});
const addUrl = '/api/vt-admin/sealContractObject/saveWorkOrder';
const delUrl = '/api/vt-admin/sealContractObject/remove?ids=';
const updateUrl = '/vt-admin/sealContractObject/updateWorkOrder';
const tableUrl = '/api/vt-admin/sealContractObject/externalPcPage';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let totalNumber = ref(0);
let totalTimes = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        startDateStr:
          params.value.createTime && params.value.createTime[0] ? params.value.createTime[0] : '',
        endDateStr:
          params.value.createTime && params.value.createTime[1] ? params.value.createTime[1] : '',
     
        selectType: 2,
        ...params.value,   createTime: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    })
    .catch(err => {
      loading.value = false;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/sealContractObject/externalPcPageStatistics', {
      params: {
        size,
        current,
        startDateStr:
          params.value.createTime && params.value.createTime[0] ? params.value.createTime[0] : '',
        endDateStr:
          params.value.createTime && params.value.createTime[1] ? params.value.createTime[1] : '',
    
        selectType: 2,
        ...params.value,    createTime: null,
      },
    })
    .then(res => {
      totalNumber.value = res.data.data.totalNumber;
      totalTimes.value = res.data.data.totalTimes;
    });
}
let router = useRouter();
function beforeOpen(done, type) {
  if (['add', 'view'].includes(type)) {
    if (type == 'add') {
      option.value.group[0].display = false;
    }
    if (type == 'view') {
      if (form.value.createUser == proxy.$store.getters.userInfo.user_id) {
        option.value.group[0].display = false;
      } else {
        option.value.group[0].display = false;
      }
    }
  } else {
    form.value.files =
      form.value.fileList &&
      form.value.fileList.map(item => {
        // 修正拼写错误，将 reuturn 改为 return
        return {
          label: item.originalName,
          value: item.id,
        };
      });
    if (form.value.externalHandleUser) {
      form.value.externalHandleUserList = [
        {
          id: form.externalHandleUser,
          realName: form.value.handleUserName,
        },
      ];
    }
    form.value.objectName = form.value.objectName || form.value.labelName;
    form.value.remark = form.value.remark || form.value.taskDescription;
    form.value.planTime = form.value.planTime || form.value.planStartTime;
    if (form.value.createUser == proxy.$store.getters.userInfo.user_id) {
      option.value.group[0].display = false;
    } else {
      option.value.group[0].display = true;
    }
  }
  done();
}
function rowSave(form, done, loading) {
  if (form.sealContractId) {
    const data = {
      ...form,

      files: form.files && form.files.map(item => item.url).join(','),
    };
    axios
      .post(addUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {});
  } else {
    addContract(form, done, loading);
  }
}

function addContract(form, done, loading) {
  const data = {
    ...form,
    finalCustomerConcat: form.contact,
    finalCustomerPhone: form.contactPhone,
    contractType: 2,
    deliveryAddress: form.distributionAddress,
    contractFiles: form.contractFiles && form.contractFiles.map(item => item.value).join(','),
  };
  axios
    .post('/api/vt-admin/sealContract/saveWorkOrderSealContract', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
        form.sealContractId = res.data.data;
        rowSave(form, done, loading);
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const url =
    row.createUser == proxy.$store.getters.userInfo.user_id
      ? updateUrl
      : '/api/vt-admin/sealContractObject/sendOrder';
  ;
  const data = {
    ...row,
    files: row.files && row.files.map(item => item.value).join(','),
  };
  axios
    .post(url, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function completeTask(row) {
  proxy.$refs.dialogForm.show({
    title: '完成',
    option: {
      labelWidth: 120,
      column: [
        {
          type: 'date',
          label: '实际完成时间',
          span: 12,
          value: dateFormat(new Date(), 'yyyy-MM-dd'),
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'completeTime',
        },
        {
          label: '实际使用工时',
          prop: 'useTimes',
          type: 'number',
        },
        {
          label: '附件',
          prop: 'completeFiles',
          type: 'upload',
          overHidden: true,
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '服务复盘',
          prop: 'serviceReorder',
          type: 'textarea',
         
          span: 24,
          value: '问题描述:\n\n原因分析:\n\n解决方案:\n\n服务成果:\n',
        },
        {
          label: '备注',
          prop: 'completeRemark',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    callback(res) {
      let url = '/api/vt-admin/sealContractObject/completeWorkOrder';
      const data = {
        id: row.id,
        ...res.data,
        completeFiles: res.data.completeFiles.map(item => item.value).join(','),
      };
      axios.post(url, data).then(r => {
        proxy.$message.success(r.data.msg);
        proxy.$store.dispatch('getMessageList');
        res.close();
        onLoad();
      });
    },
  });
}

function accept(row) {
  proxy
    .$confirm('确认是否接单？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      const url = '/api/vt-admin/sealContractObject/acceptOrder?id=' + row.id;
      axios
        .post(url)
        .then(res => {
          if (res.data.code === 200) {
            proxy.$message.success(res.data.msg);
            onLoad();
          }
        })
        .catch(err => {
          proxy.$message.error('接单失败');
        });
    })
    .catch(() => {
      // 用户取消操作，不做处理
    });
}
</script>

<style lang="scss" scoped>
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1.5rem;
  padding: 5px;
  background: #f8fafc;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: transform 0.2s;
}

.stat-item:hover {
  transform: translateY(-2px);
}

.stat-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.4rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.el-icon {
  font-size: 1.2rem;
  color: #3b82f6;
}

</style>

<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @keyup.enter="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <el-button type="primary" @click="addOrder" icon="plus">储备采购</el-button>
        <!-- <el-button type="primary" @click="addInquiryOpen" icon="plus">生成询价单</el-button> -->
      </template>
      <template #orderStatus="{ row }">
        <el-tag effect="plain" size="small" v-if="row.orderStatus == 0" type="warning"
          >待采购</el-tag
        >
        <el-tag effect="plain" size="small" type="warning" v-else-if="row.orderStatus == 2"
          >询价中</el-tag
        >
        <el-tag effect="plain" size="small" v-if="row.orderStatus == 1" type="warning"
          >采购中</el-tag
        >
        <el-tag effect="plain" size="small" type="success" v-else-if="row.orderStatus == 3"
          >采购完成</el-tag
        >
      </template>
      <template #menu="{ row }">
        <div v-if="row.cancelStatus != 1">
          <el-button
            type="primary"
            text
            icon="Back"
            @click="back(row)"
            v-if="row.auditType == 0 && row.purchaseType == 1"
            >退 回</el-button
          >
          <el-button
            type="primary"
            icon="delete"
            @click="deleteProduct(row)"
            v-if="row.auditType == 0 && row.purchaseType == 2"
            text
            >删除</el-button
          >
          <el-button
            type="primary"
            text
            icon="TakeawayBox"
            @click="wareHousing(row)"
            v-if="row.orderStatus == 1 && route.query.type != 0"
            >入库</el-button
          >
          <el-button
            type="primary"
            text
            icon="TakeawayBox"
            @click="outHouse(row)"
            v-if="
              (row.inStorageStatus == 1 || row.inStorageStatus == 2) &&
              route.query.type != 0 &&
              row.outStorageStatus !== 2 &&
              row.purchaseType != 2
            "
            >出库</el-button
          >
          <el-button type="primary" text icon="Printer" @click="printOrder(row)">送货单</el-button>
          <el-button type="primary" text icon="view" @click="toDetail(row)">详情</el-button>
          <el-button
            type="primary"
            text
            icon="view"
            @click="toInquiry(row)"
            v-if="route.query.type != 0"
            >询价单</el-button
          >
          <el-button
            type="primary"
            text
            title="撤回到询价状态"
            icon="back"
            @click="withdraw(row)"
            v-if="
              route.query.type != 0 &&
              (row.orderStatus == 2 || row.orderStatus == 1) &&
              row.inStorageStatus == 0
            "
            >撤回</el-button
          >
        </div>
        <el-tag type="warning" v-else effect="plain">撤销中</el-tag>
        <!-- <el-button
          type="primary"
          text
          icon="CirclePlus"
          @click="addContract(row)"
          v-if="row.isAddContract != 1 && row.isRelation == 1"
          >采购确认</el-button
        > -->
      </template>
      <template #orderNo="{ row }">
        <el-link type="primary" @click="toDetail(row)">{{ row.orderNo }}</el-link>
      </template>
      <template #auditStatus="{ row }">
        <div v-if="row.auditStatus == 1 || row.auditStatus == 2">
          <el-tag effect="plain" v-if="row.auditStatus == 1" size="small" type="success"
            >审核成功</el-tag
          >
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="row.auditReason"
            placement="top-start"
            v-if="row.auditStatus == 2"
          >
            <el-tag effect="plain" size="small" type="danger">审核失败</el-tag>
          </el-tooltip>
        </div>
        <div v-else>
          <el-tag effect="plain" v-if="row.auditType == 1" size="small" type="info"
            >待采购主管审核</el-tag
          >

          <el-tag effect="plain" v-if="row.auditType == 2" size="small" type="info"
            >待总经理审核</el-tag
          >
          <el-tag effect="plain" v-else-if="row.auditType == 0" type="info">待提交</el-tag>
        </div>
      </template>
      <template #number="{ row }">
        <el-link type="primary" @click="viewProduct(row)" size="large" style="font-size: 20px">{{
          row.number
        }}</el-link>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>

    <!-- 打印 -->
    <el-drawer :with-header="false" size="90%" v-model="printDrawer">
      <el-row style="height: 100%" :gutter="20">
        <el-col :span="10">
          <el-card style="height: 100%">
            <avue-form :option="printOption" v-model="form">
              <template #productList>
                <avue-crud
                  :option="productOption"
                  @selection-change="selectionChange"
                  :data="productList"
                  ref="productRef"
                >
                  <template #number="{ row }">
                    <el-input
                      size="small"
                      style="width: 90%"
                      controls-position="right"
                      v-model="row.number"
                    ></el-input>
                  </template>
                </avue-crud>
              </template>
            </avue-form>
          </el-card>
        </el-col>
        <el-col :span="14">
          <el-card style="padding: 0; overflow-y: scroll; height: 100%">
            <div id="printBox">
              <div
                class="header"
                style="display: flex; align-items: center; border: 1px dashed #ccc"
              >
                <div style="width: 150px; margin-right: 30px">
                  <img style="width: 150px" :src="form.companyInfo?.exportLogoUrl" alt="暂无logo" />
                </div>
                <div class="title">
                  <div class="address">
                    {{ form.companyInfo?.address }}
                  </div>
                  <div class="phone">电话：{{ form.companyInfo?.contactPhone }}</div>
                  <div style="display: flex; align-items: center; justify-content: space-between">
                    <p class="name" style="font-size: 22px; margin-right: 20px; font-weight: bold">
                      {{ form.companyInfo?.companyName }}
                    </p>
                    <p class="name">送货单号：{{ form.deliveryNumber }}</p>
                  </div>
                </div>
              </div>
              <div style="border: 5px solid #00b0f0; height: 0"></div>
              <table border style="width: 100%">
                <colgroup>
                  <col style="width: 10%" />

                  <col style="width: 30%" />

                  <col style="width: 10%" />

                  <col style="width: 20%" />
                </colgroup>
                <tr>
                  <td style="width: 100px; text-align: right">项目名称：</td>
                  <td>{{ form.sealContractName }}</td>
                  <!-- <td></td> -->
                  <td style="width: 100px; text-align: right">客户名称：</td>
                  <td>{{ form.customerName }}</td>
                </tr>
                <tr>
                  <td rowspan="2" style="width: 100px; text-align: right">送货地址：</td>
                  <td rowspan="2">{{ form.deliveryAddress }}</td>
                  <td style="width: 100px; text-align: right">联 系 人：</td>
                  <td>{{ form.deliveryUser }}</td>
                </tr>

                <tr>
                  <!-- <td style="width: 100px">签收日期：</td>
                  <td></td> -->
                  <td style="width: 100px; text-align: right">电 话：</td>
                  <td>{{ form.deliveryContact }}</td>
                </tr>
                <tr>
                  <td style="width: 100px; text-align: right">订单号：</td>
                  <td colspan="1">{{ form.purchaseCode }}</td>
                  <td style="width: 100px;text-align: right;">送货日期：</td>
                  <td colspan="1">{{ form.deliveryDate }}</td>
                </tr>
              </table>
              <div style="border: 5px solid #00b0f0; height: 0"></div>
              <table center style="width: 100%; text-align: center" border>
                <thead>
                  <tr>
                    <th style="min-width: 130px;">商品编码</th>
                    <th style="min-width: 130px;">商品名称</th>
                    <th style="min-width: 50px;">品牌</th>
                    <th style="min-width: 130px;">型号规格</th>
                    <th style="min-width: 50px;">单位</th>
                    <th style="min-width: 50px;">数量</th>
                    <th v-if="form.type == 1" style="min-width: 100px;">单价</th>
                    <th v-if="form.type == 1" style="min-width: 100px;">金额</th>
                    <th>备注</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="item in selectList">
                    <td>{{ item.product?.productCode }}</td>
                    <td>{{ item.customProductName }}</td>
                    <td>{{ item.productBrand }}</td>
                    <td>{{ item.customProductSpecification }}</td>
                    <td>{{ item.customUnit || item.product.unitName }}</td>
                    <td>{{ item.number }}</td>
                    <td v-if="form.type == 1">{{ item.sealPrice }}</td>
                    <td v-if="form.type == 1">{{ item.number * 1 * item.sealPrice }}</td>
                    <td style="max-width: 150px" contenteditable="true"></td>
                  </tr>
                  <tr v-if="form.type == 1">
                    <td>合计金额：（RMB）</td>
                    <td style="text-align: left" colspan="5">（大写）{{ totalPriceText() }}</td>
                    <td style="text-align: left">（小写）</td>
                    <td>{{ totalPrice().toFixed(2) }}</td>
                  </tr>
                </tbody>
              </table>
              <h4>备注：</h4>
              <p>
                {{ form.remark }}
              </p>
              <el-row :gutter="20">
                <el-col :span="12" style="border-bottom: 2px solid black">
                  <span>送（发）货经手人签字：</span>
                </el-col>
                <el-col :span="12" style="border-bottom: 2px solid black">
                  <span>客户签收：</span>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <template #footer>
        <el-button @click="printDrawer = false">取消</el-button>
        <!-- <el-button @click="exportDocx" type="primary">导出为word</el-button> -->
        <el-button type="primary" v-print="print">确定 打印</el-button>
        <el-button type="primary" @click="exportExcel">导出 excel</el-button>
      </template>
    </el-drawer>
    <addInquiry ref="addInquiryRef"></addInquiry>
    <el-drawer title="查看产品" v-model="productDrawer" direction="rtl" size="60%">
      <h3>自服务产品</h3>
      <avue-crud
        :option="productOption"
        :data="productData.filter(item => item.isSelfService == 1)"
      ></avue-crud>
      <h3>其它</h3>
      <avue-crud
        :option="productOption"
        :data="productData.filter(item => item.isSelfService == 0)"
      ></avue-crud>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, onActivated, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { dateFormat } from '@/utils/date.js';
import { DX } from '@/utils/util';
import addInquiry from './compoents/addInquiry.vue';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  // selection:true,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchIcon: true,
  searchIndex: 4,
  searchSpan: 4,
  menuWidth: 240,
  border: true,
  column: [
    {
      label: '订单编号',
      prop: 'orderNo',
      width: 100,
      overHidden: true,
      search: true,
    },
    {
      label: '报价名称',
      prop: 'offerName',
      overHidden: true,
      search: true,
    },
    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true,
      search: true,
      component: 'wf-customer-drop',
    },
    {
      label: '业务员',
      prop: 'businessName',
      component: 'wf-user-drop',
      search: true,
      width: 80,
      // hide: true,
    },
    {
      label: '交付时间',
      prop: 'contractDeliveryDate',
      valueFormat: 'YYYY-MM-DD',
      // search: true,
      width: 120,
    },

    {
      label: '采购数量',
      prop: 'number',
      width: 90,
    },

    {
      label: '下单时间',
      prop: 'orderDate',
      type: 'date',
      component: 'wf-daterange-search',
      search: true,
      overHidden: true,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      searchSpan: 5,
      search: true,
      width: 110,
    },
    {
      label: '采购人',
      prop: 'purchaseName',
      component: 'wf-user-select',
      searchSpan: 3,
      width: 80,
      // search: true,
    },
    {
      label: '采购类型',
      prop: 'purchaseType',
      type: 'select',
      width: 100,
      dicData: [
        {
          value: 0,
          label: '订单采购',
        },
        {
          value: 1,
          label: '项目采购',
        },
        {
          value: 2,
          label: '储备采购',
        },
      ],

      search: true,
      searchSpan: 3,
      width: 100,
      // search: true,
    },
    {
      label: '审核状态',
      prop: 'auditStatus',
      type: 'select',
      width: 100,
      dicData: [
        {
          value: 0,
          label: '待审核',
        },
        {
          value: 1,
          label: '审核成功',
        },
        {
          value: 2,
          label: '审核失败',
        },
      ],
      slot: true,
      search: true,
    },
    {
      label: '订单状态',
      prop: 'orderStatus',
      type: 'select',
      width: 100,
      dicData: [
        {
          value: 0,
          label: '待采购',
        },
        {
          value: 2,
          label: '询价中',
        },
        {
          value: 1,
          label: '采购中',
        },

        {
          value: 3,
          label: '采购完成',
        },
      ],
      slot: true,
      search: true,
    },
    {
      label: '入库状态',
      prop: 'inStorageStatus',
      type: 'select',
      width: 100,
      dicData: [
        {
          value: 0,
          label: '未入库',
        },
        {
          value: 1,
          label: '部分入库',
        },
        {
          value: 2,
          label: '全部入库',
        },
      ],
      slot: true,
      search: true,
    },
    {
      label: '出库状态',
      prop: 'outStorageStatus',
      type: 'select',
      width: 100,
      dicData: [
        {
          value: 0,
          label: '未出库',
        },
        {
          value: 1,
          label: '部分出库',
        },
        {
          value: 2,
          label: '全部出库',
        },
      ],
      slot: true,
      search: true,
      formatter: (row, value, $value) => {
        if (row.purchaseType == 2) {
          return '-';
        } else {
          return $value;
        }
      },
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const tableUrl = '/vt-admin/purchaseOrder/page';
let route = useRoute();
let params = ref({
  orderStatus: route.query.type == 19 ? 0 : route.query.type == 20 ? 1 : '',
  offerId: route.query.offerId || null,
  offerName: route.query.offerName || null,
});
watch(
  () => route.query.ids,
  val => {
    params.value = {
      orderStatus: route.query.type == 19 ? 0 : route.query.type == 20 ? 1 : '',
      offerId: route.query.offerId || null,
      offerName: route.query.offerName || null,
    };
    onLoad();
  }
);
watch(
  () => route.query.offerId,
  () => {
    params.value = {
      orderStatus: route.query.type == 19 ? 0 : route.query.type == 20 ? 1 : '',
      offerId: route.query.offerId || null,
      offerName: route.query.offerName || null,
    };
    onLoad();
  }
);
watch(
  () => route.query.offerName,
  () => {
    params.value = {
      orderStatus: route.query.type == 19 ? 0 : route.query.type == 20 ? 1 : '',
      offerId: route.query.offerId || null,
      offerName: route.query.offerName || null,
    };
    onLoad();
  }
);
let tableData = ref([]);
let { proxy } = getCurrentInstance();

onMounted(() => {
  onLoad();
});
onActivated(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        customerId: route.query.id || null,
        orderStartDate: params.value.orderDate && params.value.orderDate[0],
        orderEndDate: params.value.orderDate && params.value.orderDate[1],
        orderDate: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function searchChange(params, done) {
  onLoad();
  done();
}
function toDetail(row, activeName = null) {
  router.push({
    path: '/SRM/procure/compoents/orderDetail',
    query: {
      id: row.id,
      activeName,
    },
  });
}
function addContract(row) {
  proxy.$refs.dialogForm.show({
    title: row.name,
    tip: '确定前请核对产品供应商信息,确认后会自动生成与供应商的合同',
    option: {
      column: [
        {
          label: '确认时间',
          span: 12,
          prop: 'confirmDate',
          type: 'date',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          value: dateFormat(new Date(), 'yyyy-MM-dd'),
          disabled: true,
        },
        {
          label: '确认人',
          span: 12,
          component: 'wf-user-select',
          value: proxy.$store.getters.userInfo.user_id,
          prop: 'createUser',
          disabled: true,
        },
        {
          label: '采购日期',
          type: 'date',
          prop: 'purchaseDate',
          searchRange: true,
          value: form.value.purchaseDate,
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          search: true,
          span: 24,
          searchSpan: 5,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          cell: true,
          width: 150,
          rules: [
            {
              required: true,
              message: '请选择采购日期',
              trigger: 'change',
            },
          ],
        },
        {
          label: '送货地址',
          type: 'input',
          span: 24,
        },
        {
          label: '送货方式',
          type: 'radio',
          prop: 'deliveryMethod',
          span: 24,
          dicUrl: '/blade-system/dict/dictionary?code=delivery_method',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          label: '付款期限',
          type: 'radio',
          span: 24,
          prop: 'paymentTerm',
          dicUrl: '/blade-system/dict/dictionary?code=payment_term',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          label: '付款方式',
          type: 'radio',
          prop: 'paymentMethods',
          span: 24,
          dicUrl: '/blade-system/dict/dictionary?code=payment_methods',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },

        {
          label: '附件',
          prop: 'files',
          type: 'upload',
          dataType: 'object',
          cell: true,
          span: 24,
          loadText: '附件上传中，请稍等',
          span: 12,
          width: 150,
          slot: true,
          value: [],
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '备注',
          span: 24,
          prop: 'remark',
          type: 'textarea',
        },
      ],
    },
    callback(res) {
      console.log(res.data);
      const data = {
        ...res.data,
        files: res.data.files.map(e => e.value).join(','),
        id: row.id,
      };
      axios.post('/api/vt-admin/vt-admin/purchaseContract/generateContract', data).then(e => {
        proxy.$message.success('操作成功');
        res.close();
        onLoad();
      });
    },
  });
  // proxy
  //   .$confirm('确认一键生成所有合同', '提示', {
  //     type: 'warning',
  //   })
  //   .then(res => {
  //     axios
  //       .post('/api/vt-admin/vt-admin/purchaseContract/generateContract?id=' + row.id)
  //       .then(res => {
  //         proxy.$message.success('生成合同成功');
  //         onLoad();
  //       });
  //   });
}

// 入库
let inHouseOption = ref({
  submitBtn: false,
  labelPosition: 'left',
  emptyBtn: false,
  column: [
    {
      label: '附件',
      prop: 'files',
      type: 'upload',
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea',
      span: 24,
    },
  ],
});
let wareHouseVisible = ref(false);
let currentId = ref('');
let currentTabledata = ref([]);
let tableLoading = ref(false);
function wareHousing(row) {
  router.push({
    path: '/SRM/warehouse/compoents/addInhouse',
    query: {
      orderId: row.id,
    },
  });
}

function wareHouseSubmit() {
  const data = {
    orderId: currentId.value,
    files: form.value.files.map(item => item.value).join(','),
    remark: form.value.remark,
    detailDTOList: currentTabledata.value
      .map(item => {
        return {
          orderDetailId: item.id,
          id: null,
          contractDetailId: item.contractDetailId,
          ...item,
          number: item.inStorageNumber,
          inStorageAddress: item.inStorageAddress,
        };
      })
      .filter(item => item.number != 0),
  };
  axios.post('/api/vt-admin/purchaseInStorage/save', data).then(res => {
    proxy.$message.success('入库成功');
    onLoad();
    wareHouseVisible.value = false;
  });
}

// 出库
let outHouseVisible = ref(false);
function outHouse(row) {
  router.push({
    path: '/SRM/warehouse/compoents/addOuthouse',
    query: {
      orderId: row.id,
    },
  });
  // tableLoading.value = true;
  // outHouseVisible.value = true;
  // currentId.value = row.id;
  // axios
  //   .get('/api/vt-admin/purchaseOrder/pageForOut', {
  //     params: {
  //       id: row.id,
  //     },
  //   })
  //   .then(res => {
  //     tableLoading.value = false;
  //     currentTabledata.value = res.data.data.detailList.map(item => {
  //       return {
  //         ...item.productVO,
  //         ...item,
  //         inStorageNumber: 0,
  //         inStorageAddress: 0,
  //       };
  //     });
  //   });
}

function outHouseSubmit() {
  const data = {
    orderId: currentId.value,
    detailDTOList: currentTabledata.value
      .map(item => {
        return {
          orderDetailId: item.id,
          id: null,
          contractDetailId: item.contractDetailId,
          ...item,
          number: item.inStorageNumber,
          inStorageAddress: item.inStorageAddress,
        };
      })
      .filter(item => item.number != 0),
  };
  axios.post('/api/vt-admin/purchaseOutStorage/save', data).then(res => {
    proxy.$message.success('出库成功');
    onLoad();
    wareHouseVisible.value = false;
  });
}
function addOrder(params) {
  router.push({
    path: '/procure/orderAdd',
  });
}
function toInquiry(row, activeName = null) {
  router.push({
    path: '/SRM/procure/compoents/inquirySheet',
    query: {
      id: row.id,
      type: 0,
    },
  });
}
function reset() {
  page.value.currentPage = 1;
  params.value.orderStatus = null;
  params.value.offerId = null;
  params.value.offerName = null;
  onLoad();
}
function withdraw(row) {
  proxy
    .$confirm('确认撤回', '提示', {
      type: 'warning',
    })
    .then(res => {
      axios
        .post('/api/vt-admin/purchaseOrder/cancelAudit', {
          id: row.id,
        })
        .then(res => {
          proxy.$message.success('撤回成功');
          onLoad();
        });
    });
}

// 打印
let printDrawer = ref(false);
let companyList = ref([]);
let printOption = ref({
  submitBtn: false,
  emptyBtn: false,
  column: {
    deliverCompany: {
      label: '发货公司',
      type: 'select',
      props: {
        label: 'companyName',
        value: 'id',
      },
      dicFormatter: res => {
        companyList.value  = res.data.records;
        form.value.deliverCompany = companyList.value[0].id;
        return res.data.records;
      },
      span:24,
      change: val => {
        const data = companyList.value.find(item => item.id === val.value);
        form.value.companyInfo = data;
      },
      overHidden: true,
      cell: false,
     
      dicUrl: '/api/vt-admin/company/page?size=100',
    },
    deliveryDate: {
      span: 24,
      label: '送货时间',
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    type: {
      label: '类型',
      type: 'radio',
      span: 24,
      value: 1,
      dicData: [
        {
          value: 1,
          label: '有价格',
        },
        {
          value: 0,
          label: '无价格',
        },
      ],
    },
    productList: {
      label: '产品列表',
      type: 'input',
      span: 24,
    },
    remark: {
      span: 24,
      label: '备注',
      type: 'textarea',
      overHidden: true,
    },
  },
});

let selectList = ref([]);
function selectionChange(list) {
  selectList.value = list;
}
const print = ref({
  id: 'printBox',
});
function printOrder(row) {
  axios
    .get('/api/vt-admin/purchaseOrder/print', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      printDrawer.value = true;
      getDetail(row.id);
      form.value = {
        ...row,
        ...res.data.data,
        id: row.id,
        deliveryNumber: res.data.data.deliveryNumber,
        type: 0,
      };
    });
}
let productList = ref([]);
function getDetail(id) {
  axios
    .get('/api/vt-admin/purchaseOrder/printList', {
      params: {
        id: id,
      },
    })
    .then(res => {
      productList.value = [...res.data.data];
      proxy.$refs.productRef.$refs.table.toggleAllSelection();
    });
}
function total() {
  return selectList.value.reduce((a, b) => {
    a += b.zhhsze * 1;
    return a;
  }, 0);
}
function totalPriceText() {
  return DX(total());
}
function totalPrice() {
  return total();
}
function exportExcel() {
  const { deliverCompany: companyName, type: isHasPrice, deliveryDate, remark, id } = form.value;
  const data = {
    companyName,
    isHasPrice,
    deliveryDate,
    remark,
    id,
    detailDTOList: selectList.value,
  };
  axios({
    url: '/api/vt-admin/purchaseOrder/printDeliveryNote',
    data,
    method: 'post',
  }).then(res => {
    window.open(res.data.data);
  });
}
// 退回值请购单
function back(row) {
  proxy
    .$confirm('是否确认退回?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios.post('/api/vt-admin/purchaseOrder/cancelProjectPurchase', { id: row.id }).then(res => {
        if (res.data.code === 200) {
          proxy.$message.success('退回成功');
          onLoad();
        } else {
          proxy.$message.error(res.data.msg);
        }
      });
    })
    .catch(() => {});
}
function deleteProduct(row) {
  proxy
    .$confirm('确定删除该订单吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios.post('/api/vt-admin/purchaseOrder/delete?id=' + row.id).then(res => {
        proxy.$message.success('操作成功');

        onLoad();
      });
    })
    .catch(() => {});
}
let addInquiryRef = ref(null);
function addInquiryOpen() {
  addInquiryRef.value.open();
}

let productDrawer = ref(false);
let productOption = {
  menu: false,
  selection: false,
  tip: false,
  border: true,
  header: false,
  column: [
    {
      label: '产品名称',
      prop: 'customProductName',
      overHidden: true,
      cell: false,
      formatter: row => {
        return row.customProductName || row.productVO?.productName;
      },
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',
      overHidden: true,
      cell: false,
      span: 24,
      type: 'input',
      formatter: row => {
        return row.customProductSpecification || row.productVO?.productSpecification;
      },
    },

    {
      label: '品牌',
      cell: false,
      prop: 'productBrand',
      overHidden: true,
      formatter: row => {
        return row.productBrand || row.productVO?.productBrand;
      },
    },
    {
      label: '单位',
      type: 'select',
      cell: false,
      width: 80,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],

      prop: 'customUnit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
      formatter: row => {
        return row.customUnit || row.productVO?.unitName;
      },
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      rules: [
        {
          required: true,
          message: '请输入数量',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'inpput',
    },
  ],
};
let productData = ref([]);
function viewProduct(row) {
  axios
    .get('/api/vt-admin/purchaseOrder/detailForSelf', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      productData.value = res.data.data.detailList;
      productDrawer.value = true;
    });
}
</script>

<style lang="scss" scoped>
:deep(.el-table--small .cell) {
  padding: 0 !important;
}
</style>

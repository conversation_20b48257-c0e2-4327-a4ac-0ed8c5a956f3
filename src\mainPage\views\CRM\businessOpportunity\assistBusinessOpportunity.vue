<template>
  <basic-container :shadow="props.customerId ? 'shadow' : 'always'">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      :table-loading="loading"
      ref="crud"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @keyup.enter="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <div style="display: flex">
          <el-button
            type="primary"
            @click="handleAdd"
            icon="Plus"
            style="margin-right: 50px"
            v-if="route.query.type == 0 || route.query.type == 2 || !route.query.type"
            >新增</el-button
          >
          <div class="status_group">
            <statusGroup @click="handleStatusClick"></statusGroup>
          </div>
        </div>
      </template>
      <template #stage="{ row }">
        <el-tooltip
          :content="row.failReason || row.sendBackReason || row.pauseReason"
          :disabled="
            !(
              (row.stage == 0 && row.sendBackStatus == 1) ||
              row.$stage == '失单' ||
              row.$stage == '暂停'
            )
          "
          placement=""
        >
          <el-tag effect='plain'
            :type="
              (row.stage == 0 && row.sendBackStatus == 1) ||
              row.$stage == '失单' ||
              row.$stage == '暂停'
                ? 'danger'
                : 'primary'
            "
            >{{
              row.$stage == '方案'
                ? `方案(${row.optionStatus == 1 ? '进行中' : '已完成'})`
                : row.$stage == '报价'
                ? `报价(${row.offerStatus == 1 ? '进行中' : '已完成'})`
                : row.$stage
            }}</el-tag
          >
        </el-tooltip>
      </template>
      <template #name="{ row }">
        <el-link type="primary" @click="handleView(row)">{{ row.name }}</el-link>
      </template>
      <template #menu="{ row, index }">
        <!-- <el-button type="primary" text @click="handleEdit(row)" icon="Edit">编辑</el-button> -->
        <!-- <el-button
          type="primary"
          text
          @click="handleView(row)"
          icon="View"
          v-if="route.query.type == 0 || route.query.type == 2 || !route.query.type"
          >详情</el-button
        > -->

        <div v-if="row.stage != 5" style="display: flex; justify-content: center">
          <div>
            <el-button
              type="primary"
              text
              @click="follow(row)"
              icon="Service"
              v-if="
                (route.query.type == 0 || route.query.type == 2 || !route.query.type) &&
                row.stage != 4
              "
              >跟进</el-button
            >
          </div>
          <div v-if="route.query.type == 0 || route.query.type == 2 || !route.query.type">
            <el-button
              @click="handleCommand(1, row)"
              text
              icon="DocumentAdd"
              type="primary"
              :command="1"
              v-if="row.stage == 0"
              >方案</el-button
            >

            <!-- <el-button
              :command="2"
              icon="Tickets"
              @click="handleCommand(2, row)"
              v-if="row.stage == 1 && row.optionStatus == 2"
              text
              type="primary"
              >报价</el-button
            > -->
            <!-- <el-dropdown-item :command="2">投标</el-dropdown-item> -->
            <el-button :command="3" @click="handleCommand(3, row)" text icon="SetUp" type="primary"
              >登记</el-button
            >
            <el-button
              :command="5"
              @click="handleCommand(5, row)"
              text
              icon="Tickets"
              type="primary"
              v-if="row.stage == 0"
              >报价</el-button
            >

            <el-button
              :command="7"
              @click="$refs.crud.rowDel(row)"
              text
              icon="delete"
              type="primary"
              v-if="row.bidStatus == 0 && row.stage == 0"
              >删除</el-button
            >
          </div>
        </div>
        <el-button
          @click="handleCommand(8, row)"
          text
          icon="VideoPlay"
          type="primary"
          :command="8"
          v-else
          >重 启</el-button
        >
      </template>
      <template #followDays="{row}">
        <el-tag   v-if="row.followDays" style="font-weight:bolder" effect="dark" round :type="row.followDays < 7?'success':row.followDays < 15?'warning':'danger'">{{row.followDays}}天</el-tag>
      </template>
      <template #bidStatus="{ row }">
        <div style="display: flex; align-items: center">
          <el-tag effect='plain' :type="['info', 'warning', 'success', 'danger'][row.bidStatus]">{{
            row.$bidStatus
          }}</el-tag>
          <el-button
            :command="6"
            @click="handleCommand(6, row)"
            text
            icon="CirclePlusFilled"
            type="primary"
            v-if="row.bidStatus == 0 && row.classify == 1"
          ></el-button>
        </div>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer v-model="drawer" :with-header="false" size="80%" title="拆解">
      <el-row :gutter="20" style="height: 100%">
        <el-col :span="10">
          <el-card style="height: 100%">
            <el-alert type="success" :closable="false">请将需要拆分的产品勾选上</el-alert>
            <avue-crud
              :option="needDeposeOption"
              :data="needDeposeData"
              @row-click="handleRowClick"
            >
              <template #menu="{ row }">
                <el-switch v-model="row.isDepose" :active-value="1" :inactive-value="0">
                </el-switch>
              </template>
            </avue-crud>
          </el-card>
        </el-col>
        <el-col style="height: 100%" :span="14">
          <el-row style="margin-bottom: 10px">
            <el-col :span="24">
              <el-card>
                <!-- <avue-form :value="detailForm" :option="decomposeOption"></avue-form> -->
                <el-descriptions border :title="detailForm.customProductName" :column="3">
                  <el-descriptions-item label="规格型号">{{
                    detailForm.customProductSpecification
                  }}</el-descriptions-item>
                  <el-descriptions-item label="品牌">{{
                    detailForm.productBrand
                  }}</el-descriptions-item>
                  <el-descriptions-item label="单位">{{
                    detailForm.unitName
                  }}</el-descriptions-item>
                  <el-descriptions-item label="描述">
                    {{ detailForm.customProductDescription }}
                  </el-descriptions-item>
                </el-descriptions>
              </el-card>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-card>
                <template #header>
                  <!-- <el-alert type="info" :closable="false"
                    >拆解会删除被拆解的产品，如需保留请再拆解列表里面添加</el-alert
                  > -->
                  <div style="display: flex; justify-content: space-between">
                    <div>拆解产品</div>
                    <div>
                      <el-form v-if="detailForm.productId">
                        <el-form-item label="保留原产品">
                          <el-switch
                            v-model="detailForm.isPre"
                            @change="handleIsPreChange"
                          ></el-switch>
                        </el-form-item>
                      </el-form>
                    </div>
                  </div>
                </template>
                <avue-crud
                  @row-del="rowDelProduct"
                  :data="detailForm.detailDTOList"
                  :option="decomposeEditFormOption"
                >
                  <template #menu-left>
                    <div v-if="detailForm.isDepose">
                      <el-button
                        icon="plus"
                        type="primary"
                        size="small"
                        @click="$refs.productSelectRef.visible = true"
                      ></el-button>
                      <productSelectDrop
                        v-if="$route.query.type != 'detail'"
                        @select="handleProductSelectConfirm"
                        style="margin-left: 5px"
                      ></productSelectDrop>
                    </div>
                  </template>
                  <template #number="{ row }">
                    <el-input-number
                      size="small"
                      style="width: 80%"
                      controls-position="right"
                      v-model="row.number"
                    ></el-input-number>
                  </template>
                </avue-crud>
              </el-card>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <template #footer>
        <el-button type="primary" icon="check" @click="decomposeSubmit">确 定</el-button>
        <el-button icon="close" @click="drawer = false">取 消</el-button>
      </template>
    </el-drawer>
    <wfProductSelect
      @onConfirm="handleProductSelectConfirm"
      ref="productSelectRef"
    ></wfProductSelect>
    <detail_drawer
      :type="currentType"
      :id="currentId"
      :customer-id="customerId"
      ref="detailRef"
    ></detail_drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { businessOpportunityData, bidStatus } from '@/const/const.js';
import statusGroup from './compoents/statusGroup.vue';
import wfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import detail_drawer from './compoents/detail_drawer.vue';
import productSelectDrop from '@/views/CRM/quotation/compoents/productSelectDrop.vue';
let route = useRoute();
let store = useStore();
let permission = computed(() => store.getters.permission);
console.log(permission.value.business_menu);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  index: true,
  searchMenuSpan: 4,
  searchSpan: 4,
  searchLabelWidth: 110,
  searchIcon: true,
  searchIndex: 4,
  menuWidth: 330,
  menu: true,
  border: true,
  column: [
    {
      label: '商机名称',
      prop: 'name',
      width: 250,component: 'wf-bussiness-drop',
      overHidden: true,
      search: !route.query.id,
    },
    // {
    //   label: '商机分类',
    //   type: 'radio',
    //   prop: 'classify',
    //   span: 24,
    //   width: 85,

    //   dicData: [
    //     {
    //       value: 0,

    //       label: '产品',
    //     },
    //     {
    //       value: 1,
    //       label: '项目',
    //     },
    //   ],
    // },
    {
      // type: 'tree',
      label: '业务板块',
      multiple: true,
      width: 100,
      span: 12,
      parent: false,
      overHidden: true,
      rules: [
        {
          required: true,
          message: '请选择业务板块',
        },
      ],
      type: 'select',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
      display: true,
      filterable: true,
      prop: 'type',
      checkStrictly: true,
      // props: {
      //   labelText: '标题',
      //   label: 'categoryName',
      //   value: 'id',
      //   children: 'children',
      // },
    },
    {
      label: '商机描述',
      prop: 'description',
      width: 85,
      overHidden: true,
    },

    {
      type: 'select',
      label: '商机来源',
      search: !route.query.id,
      overHidden: true,
      width: 85,
      span: 12,
      rules: [
        {
          required: true,
          message: '请选择商机来源',
        },
      ],
      display: true,
      prop: 'source',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityResource',
      props: {
        label: 'dictValue',
        value: 'id',
      },
    },
    {
      label: '商机阶段',
      prop: 'stage',
      width: 100,
      slot: true,
      type: 'select',
      search: !route.query.id,
      dicData: businessOpportunityData,
    },
    {
      label: '投标状态',
      prop: 'bidStatus',
      width: 100, hide: true,
      slot: true,
      type: 'select',
      // search: !route.query.id,
      search: false,
      dicData: bidStatus,
    },
    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true,
      width: 150,component: 'wf-customer-drop',
      search: !props.customerId,
      hide: !!props.customerId,
    },
    {
      label: '关联联系人',
      prop: 'contactPersonName',
      width: 100,
    },
    {
      label: '介绍人',
      width: 80,
      prop: 'introducer',
      type: 'input',
    },
    {
      label: '未更新',
      width: 80,
      prop: 'followDays',
      type: 'input',
      html:true,
      formatter(row) {
        if(!row.followDays )return null
        return `<span style="color: ${row.followDays > 15? 'red' : 'green'}">${row.followDays}天</span>`;
      }
    },
    {
      label:'售前技术',
      prop: 'technicalPersonnelName',
      width:100
    },
    {
      label: '预计销售金额',
      prop: 'preSealPrice',
      type: 'number',
      width: 110,
    },
    {
      label: '登记时间',
      width: 135,
      prop: 'createTime',
      type: 'datetime',
      search: true,
      searchSpan: 6,
      component: 'wf-daterange-search',
      search: true,
      type: 'datetime',
      format: 'YYYY-MM-DD',

      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    {
      label: '预计签单日期',
      prop: 'preSignDate',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      type: 'date',
      width: 110,
      rules: [
        {
          required: true,
          message: '请选择预计签单日期',
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const delUrl = '/api/vt-admin/businessOpportunity/remove?ids=';
const tableUrl = '/api/vt-admin/businessOpportunity/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
const props = defineProps(['customerId']);
onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        customerId: props.customerId,
        selectType: 3,
        startTime: params.value.createTime ? params.value.createTime[0] : null,
        endTime: params.value.createTime ? params.value.createTime[1] : null,
        createTime: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function handleStatusClick(value) {
  params.value.stage = value;
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function handleAdd() {
  router.push({
    path: '/CRM/businessOpportunity/compoents/update',
    query: {
      customerId: route.query.id,
      type: 'assist',
    },
  });
}
let customerId = ref(null);
let currentType = ref(null);
let currentId = ref(null);
let detailRef = ref(null);
function handleView(row) {
  router.push({
    path: '/CRM/businessOpportunity/compoents/detail',
    query: {
      type: 'detail',
      businessId: row.id,
      customerId: row.customerId,
    },
  });
}
// 跟进
function follow(row) {
  router.push({
    path: '/CRM/follow/compoents/update',
    query: {
      type: 1,
      customerId: row.customerId,
      logicId: row.id,
    },
  });
}
function handleCommand(val, row) {
  switch (val) {
    case 1:
      needProgramme(row);
      break;
    case 2:
      needQuotation(row);
      break;
    case 3:
      registration(row);
      break;
    case 5:
      toQuotation(row);
      break;
    case 6:
      toBid(row);
      break;
    case 7:
      bidResult(row);
      break;
    default:
      break;
  }
}
function needProgramme(row) {
  axios.get('/api/vt-admin/businessTypeLeader/getByBusinessId?businessId=' + row.id).then(res => {
    proxy
      .$confirm(
        `<div>确定需要技术人员提供方案吗？</div><div>本次方案技术负责人：<i style="color:var(--el-color-primary)">${res.data.data.userName}</i></div>`,
        '提示',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          type: 'info',
        }
      )
      .then(() => {
        axios
          .post('/api/vt-admin/businessOpportunity/applyMakeOption', null, {
            params: {
              id: row.id,
            },
          })
          .then(e => {
            proxy.$message.success('已通知售前技术主管');

            onLoad();
          });
      });
  });
}
function needQuotation(row) {
  console.log(222);
  proxy.$refs.dialogForm.show({
    title: row.name,
    option: {
      column: [
        {
          label: '报价人员',
          component: 'wf-user-select',
          prop: 'offerPerson',
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/businessOpportunity/transferOffer', null, {
          params: {
            id: row.id,
            ...res.data,
          },
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          onLoad();
        });
    },
  });
}
function toQuotation(row) {
  router.push({
    path: '/CRM/quotation/compoents/addVersion3',
    query: {
      businessId: row.id,
      type: 'add',
    },
  });
}
function toBid(row) {
  router.push({
    path: '/businessOpportunity/bid',
    query: {
      businessId: row.id,
    },
  });
}
// 登记
let dialogRef = '';
function registration(row) {
  proxy.$refs.dialogForm.show({
    title: '登记',
    option: {
      column: [
        {
          label: '登记结果',
          prop: 'stage',
          type: 'radio',
          rules: [
            {
              required: true,
              message: '请选择登记结果',
              trigger: 'change',
            },
          ],
          dicData: [
            {
              label: '成交',
              value: 3,
              disabled: row.stage != 2,
            },
            {
              label: '暂停',
              value: 5,
            },
            {
              label: '失单',
              value: 4,
              disabled: row.stage == 3,
            },
          ],
          control: val => {
            return {
              failReason: {
                display: val == 4,
              },
              isSplit: {
                display: val == 2 && row.isIntelligentizeProject != 1,
              },
              pauseReason: {
                display: val == 5,
              },
            };
          },
          span: 24,
        },
        // {
        //   label: '拆解产品',
        //   prop: 'isSplit',
        //   type: 'switch',
        //   dicData: [
        //     {
        //       label: '否',
        //       value: 0,
        //     },
        //     {
        //       label: '是',
        //       value: 1,
        //     },
        //   ],
        //   span: 12,
        //   display: false,
        // },
        {
          label: '失单原因',
          prop: 'failReason',
          type: 'textarea',
          display: false,
          span: 24,
        },
        {
          label: '暂停原因',
          prop: 'pauseReason',
          type: 'textarea',
          display: false,
          span: 24,
        },
      ],
    },
    callback(res) {
      console.log(res);
      dialogRef = res;
      if (res.data.isSplit == 1) {
        customerConfirmAndDecompose(row);
      } else {
        if (res.data.stage == 5) {
          axios
            .post('/api/vt-admin/businessOpportunity/pause', {
              id: row.id,
              pauseReason: res.data.pauseReason,
            })
            .then(r => {
              proxy.$message.success(r.data.msg);
              res.close();
              onLoad();
            });
        } else if (res.data.stage == 4) {
          axios
            .post('/api/vt-admin/businessOpportunity/setDown', {
              id: row.id,
              ...res.data,
            })
            .then(e => {
              proxy.$message.success(e.data.msg);
              res.close();

              onLoad();
            });
        } else {
          if (row.isIntelligentizeProject == 1) {
            res.close()
            router.push({
              path: '/Project/add',
              query: {
                offerId: row.offerId,
              },
            });
          } else {
            router.push({
              path: '/Order/salesOrder/compoents/addOrder',
              query: {
                offerId: row.offerId,
              },
            });
          }
        }
      }
    },
  });
}
// function bidResult(row) {
//   proxy.$refs.dialogForm.show({
//     title: row.name,
//     option: {
//       column: [
//         {
//           label: '投标结果',
//           type: 'radio',
//           prop: 'bidStatus',
//           dicData: [
//             {
//               value: 2,
//               label: '中标',
//             },
//             {
//               value: 3,
//               label: '未中标',
//             },
//           ],
//           control: val => {
//             return {
//               bidReason: {
//                 display: val == 3,
//               },
//               bidWinningNotice: {
//                 display: val == 2,
//               },
//             };
//           },
//         },
//         {
//           label: '失单原因',
//           prop: 'bidReason',
//           type: 'textarea',
//           display:false,
//           span: 24,
//         },
//         {
//           label: '确认附件',
//           prop: 'bidWinningNotice',
//           type: 'upload',
//           display:true,
//           value: [],
//           dataType: 'object',
//           loadText: '附件上传中，请稍等',
//           span: 24,
//           slot: true,
//           // align: 'center',
//           propsHttp: {
//             res: 'data',
//             url: 'id',
//             name: 'originalName',
//             // home: 'https://www.w3school.com.cn',
//           },
//           action: '/blade-resource/attach/upload',
//         },
//       ],
//     },
//     callback(res) {
//       axios
//         .post('/api/vt-admin/businessOpportunityBiding/update', {
//           businessOpportunityId:row.id,
//           bidStatus:res.data.bidStatus,
//           bidWinningNotice:res.data.bidWinningNotice.map(item => item.value).join(','),
//         })
//         .then(e => {
//           proxy.$message.success('操作成功');
//           res.close();
//           onLoad();
//         });
//     },
//   });
// }
// 拆解
let drawer = ref(false);
let currentRow = ref({});
function customerConfirmAndDecompose(row) {
  drawer.value = true;
  currentRow.value = row;
  getNeedDeposeData(row);
}

let decomposeEditFormOption = ref({
  submitBtn: false,
  emptyBtn: false,
  editBtn: false,
  // header:false,
  addBtn: false,
  column: [
    {
      label: '产品名称',
      prop: 'productName',
      overHidden: true,
      cell: false,
    },
    {
      label: '规格型号',
      prop: 'productSpecification',
      overHidden: true,
      cell: false,
      span: 24,
      type: 'input',
    },
    {
      label: '产品分类',
      prop: 'categoryId',

      hide: true,
      filterable: true,
      type: 'tree',
      cell: false,
      rules: [
        {
          required: true,
          message: '请选择产品分类',
          trigger: 'change',
        },
      ],
      children: 'hasChildren',
      parent: false,
      addDisplay: false,
      cell: false,
      dicUrl: '/api/vt-admin/productCategory/tree',
      props: {
        label: 'categoryName',
        value: 'id',
      },
    },
    {
      label: '品牌',
      cell: false,
      prop: 'productBrand',
      overHidden: true,
    },
    {
      label: '单位',
      type: 'select',
      cell: false,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],
      value: '1693833617402880001',
      prop: 'unit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      width: 120,
      rules: [
        {
          required: true,
          message: '请输入数量',
          trigger: 'blur',
        },
      ],
    },
  ],
});
let needDeposeData = ref([]);
let needDeposeOption = ref({
  header: false,
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menuWidth: 80,
  column: [
    {
      label: '产品名称',
      prop: 'customProductName',
      overHidden: true,
      cell: false,
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',
      overHidden: true,
      cell: false,
      span: 24,
      type: 'input',
    },
    {
      label: '产品分类',
      prop: 'categoryId',

      hide: true,
      filterable: true,
      type: 'tree',
      cell: false,
      rules: [
        {
          required: true,
          message: '请选择产品分类',
          trigger: 'change',
        },
      ],
      children: 'hasChildren',
      parent: false,
      addDisplay: false,
      cell: false,
      dicUrl: '/api/vt-admin/productCategory/tree',
      props: {
        label: 'categoryName',
        value: 'id',
      },
    },
    {
      label: '品牌',
      cell: false,
      prop: 'productBrand',
      overHidden: true,
    },
    {
      label: '单位',
      type: 'select',
      cell: false,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],
      value: '1693833617402880001',
      prop: 'unit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      rules: [
        {
          required: true,
          message: '请输入数量',
          trigger: 'blur',
        },
      ],
    },
  ],
});
function getNeedDeposeData(row) {
  axios
    .get('/api/vt-admin/offer/getOfferProducts', {
      params: {
        id: row.offerId,
      },
    })
    .then(res => {
      needDeposeData.value = res.data.data;
    });
}
let detailForm = ref({});
function handleRowClick(row) {
  detailForm.value = row;
  if (!detailForm.value.detailDTOList) {
    detailForm.value.detailDTOList = [];
  }
}
async function handleProductSelectConfirm(id) {
  console.log(detailForm.value == needDeposeData.value[0]);
  const res = await axios.get('/api/vt-admin/product/detail?id=' + id);
  detailForm.value.detailDTOList.push({
    productId: res.data.data.id,
    productName: res.data.data.productName,
    productSpecification: res.data.data.productSpecification,
    productBrand: res.data.data.productBrand,
    unit: res.data.data.unitName,
    number: detailForm.value.number,
  });
}
function rowDelProduct(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      detailForm.value.detailDTOList = detailForm.value.detailDTOList.filter(
        item => item.productId !== form.productId
      );
    })
    .catch(() => {});
}
function decomposeSubmit() {
  const data = {
    id: currentRow.value.offerId,
    stage: 3,
    dismantleList: needDeposeData.value
      .filter(item => item.isDepose == 1)
      .map(item => {
        return {
          id: item.id,
          productList: item.detailDTOList.map(item => {
            return {
              productId: item.productId,
              number: item.number,
            };
          }),
        };
      }),
  };
  console.log(data);
  axios.post('/api/vt-admin/businessOpportunity/setDown', data).then(e => {
    proxy.$message.success(e.data.msg);
    dialogRef.close();
    drawer.value = false;

    onLoad();

    if (currentRow.value.isIntelligentizeProject == 1) {
      router.push({
        path: '/Project/add',
        query: {
          offerId: currentRow.value.offerId,
        },
      });
    } else {
      router.push({
        path: '/Order/salesOrder/compoents/addOrder',
        query: {
          offerId: currentRow.value.offerId,
        },
      });
    }
  });
}
function handleIsPreChange(val) {
  console.log(val);
  if (val) {
    handleProductSelectConfirm(detailForm.value.productId);
  } else {
    rowDelProduct({
      productId: detailForm.value.productId,
    });
  }
}
</script>

<style lang="scss" scoped></style>

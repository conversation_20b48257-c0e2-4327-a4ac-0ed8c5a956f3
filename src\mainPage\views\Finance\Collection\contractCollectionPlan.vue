<template>
  <basic-container :shadow="!!customerId ? 'never' : 'always'">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @selection-change="selectionChange"
      @search-reset="reset"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <div style="display: flex; justify-content: flex-start; align-items: center">
          <el-button type="primary" icon="Pointer" @click="allCollect">收款</el-button>
          <el-text size="large" style="font-weight: bolder; margin-left: 5px"
            >计划收款总额：</el-text
          >
          <el-text type="primary" size="large">￥{{ (totalPrice * 1).toLocaleString() }}</el-text>
          <el-text size="large" style="font-weight: bolder; margin-left: 5px"
            >实际收款总额：</el-text
          >
          <el-text type="primary" size="large">￥{{ (actualPrice * 1).toLocaleString() }}</el-text>
          <el-text size="large" style="font-weight: bolder; margin-left: 5px">未收款总额：</el-text>
          <el-text type="primary" size="large"
            >￥{{ (noCollectPrice * 1).toLocaleString() }}</el-text
          >
          <div
            v-if="selectList.length > 0"
            style="display: flex; align-items: center; margin-left: 10px"
          >
            <span style="font-weight: bolder">本次收款总额：</span>
            <el-text type="primary" size="large"
              >￥{{ totalCollect.toLocaleString() || 0 }}</el-text
            >
          </div>
        </div>
      </template>

      <template #invoiceId-form>
        <!-- <wfCustomerInvoiceDrop
          v-model="form.invoiceId"
          
        ></wfCustomerInvoiceDrop> -->
      </template>
      <template #menu="{ row }">
        <el-button
          text
          type="primary"
          icon="pointer"
          @click="collection(row)"
          v-if="(row.collectionStatus == 0 || row.collectionStatus == 1) && row.invoiceStatus != 3"
          >收款</el-button
        >
        <el-button text type="primary" icon="Clock" @click="collectionCord(row)"
          >收款记录</el-button
        >
        <el-button text type="primary" icon="view" @click="viewDetail1(row)">明细</el-button>
      </template>
      <template #contractName="{ row }">
        <el-popover
          placement="top-start"
          :disabled="row.contractName.split(',').length <= 1"
          width="auto"
          trigger="hover"
          content="this is content, this is content, this is content"
        >
          <template #default>
            <div v-for="(item, index) in row.contractName.split(',')" :key="item">
              <el-link
                type="primary"
                @click="
                  toDetail({
                    id: row.sealContractId.split(',')[index],
                  })
                "
              >
                {{ item }}
              </el-link>
            </div>
          </template>
          <template #reference>
            <el-link type="primary" @click="toDetail({ id: row.sealContractId })">
              {{ row.contractName }}
            </el-link>
          </template>
        </el-popover>
      </template>
      <template #planCollectionDate="{ row }">
        <div v-if="row.collectionStatus != 2">
          <span
            :style="{
              color:
                row.planCollectionDays > 7
                  ? 'var(--el-color-success)'
                  : row.planCollectionDays <= 7 && row.planCollectionDays >= 0
                  ? 'var(--el-color-warning)'
                  : 'var(--el-color-danger)',
            }"
            class="planCollectionDays"
            >{{ row.planCollectionDays }}</span
          ><span>{{ row.planCollectionDays && row.planCollectionDays == 0 ? '天' : '' }}</span>
        </div>
        <div v-else style="display: flex; justify-content: center">
          <div class="circle">
            <span class="text">收讫</span>
          </div>
        </div>
      </template>
      <template #collectionStatus="{ row }">
        <div v-if="row.collectionStatus == 2" style="display: flex; justify-content: center">
          <div class="circle">
            <span class="text">收讫</span>
          </div>
        </div>
        <el-tag effect="plain" size="small" type="warning" v-if="row.collectionStatus == 1"
          >部分收款</el-tag
        >
        <el-tag effect="plain" size="small" type="warning" v-if="row.collectionStatus == 0"
          >未收款</el-tag
        >
      </template>
      <template #invoiceStatus="{ row }">
        <el-tag
          effect="plain"
          :type="
            row.invoiceStatus == 0
              ? 'primary'
              : row.invoiceStatus == 10 || row.invoiceStatus == 4
              ? 'info'
              : row.invoiceStatus == 1 || row.invoiceStatus == 2
              ? 'success'
              : 'danger'
          "
          >{{ row.$invoiceStatus }}</el-tag
        >
      </template>
    </avue-crud>

    <dialogForm ref="dialogForm"></dialogForm>
    <el-dialog title="收款记录" v-model="dialogVisible" class="avue-dialog avue-dialog--top">
      <!-- <slot ></slot> -->
      <div style="padding-bottom: 20px;">
        <collectionList :planCollectionId="currentId"></collectionList>
      </div>
      <div class="avue-dialog__footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <!-- <el-button @click="$refs.dialogForm.submit()" type="primary">确 定</el-button> -->
      </div>
    </el-dialog>
    <el-dialog
      :title="detailForm.isContractDetail ? '合同明细' : '发票明细'"
      v-model="dialogVisible1"
      width="80%"
      class="avue-dialog avue-dialog--top"
    >
      <!-- 公共信息区域 -->
      <div v-if="detailForm.sealContractVOList && detailForm.sealContractVOList.length > 0" class="common-info-section">
        <div class="common-info-content">
          <el-row :gutter="16">
            <el-col :span="6">
              <div class="info-item">
                <span class="info-label">客户名称：</span>
                <span class="info-value">{{ detailForm.customerName || getCommonInfo('customerName') }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="info-label">客户地址：</span>
                <span class="info-value">{{  getCommonInfo('customerAddress') }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="info-label">联系人：</span>
                <span class="info-value">{{ detailForm.customerContactName ||  getCommonInfo('customerContactName') }}</span>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="info-item">
                <span class="info-label">联系电话：</span>
                <span class="info-value">{{ getCommonInfo('customerPhone') }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 合同详细信息区域 -->
      <div class="contracts-section" style="padding-bottom: 50px;">
        <div v-for="(item, index) in detailForm.sealContractVOList" :key="item.id" class="contract-item">
          <el-card class="contract-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <div class="header-left">
                  <span class="header-title">
                    <template v-if="detailForm.sealContractVOList.length > 1">合同 {{ index + 1 }}：</template>
                    <el-link type="primary" @click="toDetail({ id: item.id })" class="contract-link">
                      {{ item.contractName }}
                    </el-link>
                  </span>
                  <span class="collection-amount">本次收款金额：<span class="amount-value">￥{{ calculateContractCollectionAmount(item).toLocaleString() }}</span></span>
                </div>
                <el-tag type="primary" size="small">{{ item.contractCode || '编号待生成' }}</el-tag>
              </div>
            </template>
            <ContractInfo  :invoicePriceType="detailForm.invoicePriceType" :detailForm="item" :key="item.id" :hideCommonInfo="true"></ContractInfo>
          </el-card>
        </div>
      </div>

      <div class="avue-dialog__footer">
        <el-button @click="dialogVisible1 = false">关 闭</el-button>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script setup name="ContractCollectionPlan">
import template from './../../CRM/programme/template.vue';
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
//   import wfCustomerInvoiceDrop from '../compoents/wf-customerInvoice-drop.vue';
import { dateFormat } from '@/utils/date';
import collectionList from '../../Contract/customer/compoents/collectionList.vue';
import ContractInfo from '../invoice/contractInfo.vue';
import { ElMessage } from 'element-plus';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 6,
  labelWidth: 140,
  menuWidth: 250,
  selection: true,
  reserveSelection: true,
  border: true,
  selectable: (row, index) => {
    return row.collectionStatus == 0 || row.collectionStatus == 1;
  },
  column: [
    // {
    //   type: 'input',
    //   label: '客户名称',
    //   span: 12,
    //   hide: true,
    //   display: true,
    //   prop: 'a170080949446133484',
    // },
    // {
    //   type: 'input',
    //   label: '项目名称',
    //   span: 12,
    //   display: true,
    //   hide: true,
    //   prop: 'a170080951774565537',
    // },
    {
      label: '关联合同',
      prop: 'contractName',
      overHidden: true,
      width: 150,
      search: true,
      searchSpan: 4,
      //   component: 'wf-contract-select',
    },
    {
      label: '签订时间',
      prop: 'signDate',
      overHidden: true,
      width: 110,
      searchSpan: 6,
      component: 'wf-daterange-search',
      search: true,
    },
    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true,
      searchSpan: 5,
      component: 'wf-customer-drop',
      search: !props.customerId,
      width: 150,
      hide: !!props.customerId,
    },
    {
      type: 'input',
      label: '关联发票',
      span: 12,
      hide: true,
      display: true,
      prop: 'invoiceId',
    },
    {
      type: 'input',
      label: '关联标的',
      span: 12,
      hide: true,
      display: true,
      prop: 'a170080947476223862',
    },
    {
      type: 'input',
      label: '计划名称',
      span: 12,
      display: true,
      width: 150,
      overHidden: true,
      prop: 'planName',
      required: true,
      rules: [
        {
          required: true,
          message: '计划名称必须填写',
        },
      ],
    },
    {
      type: 'date',
      label: '离回款时间',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'planCollectionDate',
      disabled: false,
      readonly: false,
      width: 110,
      required: true,
      rules: [
        {
          required: true,
          message: '计划收款时间必须填写',
        },
      ],
    },
    {
      type: 'number',
      label: '计划收款金额',
      controls: true,
      span: 24,
      width: 110,
      display: true,
      prop: 'planCollectionPrice',
    },
    // {
    //   type: 'number',
    //   label: '收款比例',
    //   controls: true,
    //   span: 24,
    //   width: 90,
    //   addDisplay: false,
    //   editDisplay: false,
    //   prop: 'collectionRate',
    //   formatter: row => {
    //     return parseFloat(row.collectionRate) + '%';
    //   },
    // },
    {
      type: 'number',
      label: '实际收款金额',
      controls: true,
      span: 24,
      width: 110,
      addDisplay: false,
      editDisplay: false,
      prop: 'actualCollection',
    },
    // {
    //   type: 'number',
    //   label: '实际回款比例',
    //   controls: true,
    //   span: 24,
    //   width: 110,
    //   addDisplay: false,
    //   editDisplay: false,
    //   prop: 'actualCollectionRate',
    //   formatter: row => {
    //     return parseFloat(row.actualCollectionRate) + '%';
    //   },
    // },
    {
      type: 'input',
      label: '逾期',
      controls: true,
      span: 24,
      addDisplay: false,
      editDisplay: false,
      prop: 'overDays',
      html: true,
      formatter: row => {
        if (row.overDays) {
          return `<span style="color:var(--el-color-danger)">${row.overDays}天</span>`;
        } else {
          return `---`;
        }
      },
    },
    {
      label: '回款时间',
      prop: 'actualTime',
      overHidden: true,
      width: 110,
      order: 0,
      searchSpan: 6,
      format: 'YYYY-MM-DD',
      type: 'date',
      component: 'wf-daterange-search',
      // search: true,
      //   component: 'wf-contract-select',
    },
    // {
    //   label: '收款状态',
    //   prop: 'collectionStatus',
    // },
    {
      label: '收款状态',
      prop: 'collectionStatuss',
      slot: true,
      addDisplay: false,
      editDisplay: false,
      hide: true,
      type: 'select',
      searchSpan: 6,
      dicData: [
        {
          value: 0,
          label: '未收款',
        },
        {
          value: 1,
          label: '部分收款',
        },
        {
          value: 2,
          label: '已收款',
        },
      ],
      search: true,
      multiple: true,

      dataType: 'string',
    },
    {
      type: 'select',
      label: '发票状态',
      span: 12,
      addDisplay: false,
      width: 100,
      editDisplay: false,
      prop: 'invoiceStatus',
      dicData: [
        {
          label: '待开票',
          value: 0,
        },
        {
          label: '已开票',
          value: 1,
        },
        {
          label: '已邮寄',
          value: 2,
        },
        {
          label: '已作废',
          value: 3,
        },
        {
          label: '申请作废',
          value: 4,
        },
        {
          label: '无需开票',
          value: 10,
        },
        {
          label: '未关联',
          value: null,
        },
      ],
      cascader: [],
      props: {
        label: 'label',
        value: 'value',
        desc: 'desc',
      },
    },
    {
      label: '业务员',
      prop: 'businessName',
      search: true,
      component: 'wf-user-drop',
      searchSpan: 4,
    },
    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
      showWordLimit: true,
      overHidden: true,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const props = defineProps(['customerId']);
const addUrl = '/api/vt-admin/sealContractPlanCollection/save';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContractPlanCollection/page';
let route = useRoute();
let params = ref({
  collectionStatuss: props.customerId ? '' : '',
  customerName:route.query.customerName,
  // ids:route.query.ids
});
watch(() => route.query,() => {
  params.value = {
    ...params.value,
    collectionStatuss: props.customerId ? '' : '',
    customerName:route.query.customerName,
    // ids:route.query.ids
  }
})
let tableData = ref([]);
let { proxy } = getCurrentInstance();

onMounted(() => {
  onLoad();
});
let loading = ref(false);
let totalPrice = ref(0);
let noCollectPrice = ref(0);
let actualPrice = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        selectType: 1,
        customerId: props.customerId,
        signStartTime: params.value.signDate && params.value.signDate[0],
        signEndTime: params.value.signDate && params.value.signDate[1],
        signDate: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/sealContractPlanCollection/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        customerId: props.customerId,
        selectType: 1,
        signStartTime: params.value.signDate && params.value.signDate[0],
        signEndTime: params.value.signDate && params.value.signDate[1],
        signDate: null,
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
      noCollectPrice.value = res.data.data.noCollectPrice;
      actualPrice.value = res.data.data.actualPrice;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function collection(row) {
  let coverId = '';
  proxy.$refs.dialogForm.show({
    title: '收款',
    option: {
      column: [
        {
          type: 'number',
          label: '收款金额',
          controls: true,
          span: 12,
          display: true,
          value: row.planCollectionPrice * 1 - row.actualCollection * 1,
          prop: 'actualPrice',
        },
        {
          type: 'datetime',
          label: '收款时间',
          span: 12,
          display: true,
          value: dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'actualDate',
          disabled: false,
          readonly: false,
          required: true,
          rules: [
            {
              required: true,
              message: '收款时间必须填写',
            },
          ],
        },
        {
          type: 'select',
          label: '收款账号',
          cascader: [],
          span: 12,
          // search: true,
          display: true,
          dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
          dicFormatter: res => {
            return res.data.records;
          },
          props: {
            label: 'abbreviation',
            value: 'id',
            desc: 'desc',
          },
          rules: [
            {
              required: true,
              message: '请选择收款账号',
              trigger: 'blur',
            },
          ],
          prop: 'collectionAccount',
        },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          span: 24,
        },
        {
          label: '收款凭证',
          prop: 'files',
          type: 'upload',
          dataType: 'object',
          // listType: 'picture-img',
          loadText: '图片上传中，请稍等',
          span: 24,
          slot: true,
          limit: 1,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
          },
          action: '/blade-resource/attach/upload',
          // uploadAfter: (res, done) => {
          //   coverId = res.id;
          //   done();
          // },
        },
      ],
    },
    callback(res) {
      const data = {
        planCollectionId: row.id,
        ...res.data,
        collectionFiles: res.data.files.map(item => item.value).join(','),
      };
      axios.post('/api/vt-admin/sealContractPlanCollection/collection', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
let dialogVisible = ref(false);
let currentId = ref('');
function collectionCord(row) {
  currentId.value = row.id;
  dialogVisible.value = true;
}

let selectList = ref([]);
let totalCollect = ref(0);
function selectionChange(list) {
  selectList.value = list;
  totalCollect.value = list.reduce((pre, cur) => {
    return pre + cur.planCollectionPrice * 1;
  }, 0);
}

function allCollect() {
  if (selectList.value.length === 0) {
    proxy.$message.warning('请选择需要合并的收款计划');
    return;
  }
  const bol = [...new Set(selectList.value.map(item => item.customerId))];

  if (bol.length > 1) {
    proxy.$message.warning('请选择同一客户下的计划收款');
    return;
  }

  selectList.value.forEach(item => {
    item.actualPrice =
      (item.planCollectionPrice * 1 - item.actualCollection * 1 || 0).toFixed(2) * 1;
  });
  proxy.$refs.dialogForm.show({
    title: '收款',
    option: {
      column: [
        {
          label: '',
          prop: 'collectList',
          type: 'dynamic',
          labelWidth: 0,
          span: 24,
          index: false,
          value: selectList.value.map(item => {
            return {
              ...item,
            };
          }),
          children: {
            align: 'center',
            addBtn: false,
            delBtn: false,
            headerAlign: 'center',
            showSummary: true,
            sumColumnList: [{ name: 'actualPrice', type: 'sum' }],
            column: [
              {
                label: '关联合同',
                prop: 'contractName',
                overHidden: true,
                cell: false,

                //   component: 'wf-contract-select',
              },
              {
                label: '签订时间',
                prop: 'signDate',
                overHidden: true,
                cell: false,
              },
              {
                type: 'number',
                label: '计划收款金额',
                controls: true,
                span: 24,
                cell: false,
                display: true,
                prop: 'planCollectionPrice',
              },
              {
                type: 'number',
                label: '实际已收金额',
                controls: true,
                span: 24,
                cell: false,
                addDisplay: false,
                editDisplay: false,
                prop: 'actualCollection',
              },
              {
                label: '本次收款',
                prop: 'actualPrice',
                type: 'number',
                span: 24,
              },
            ],
          },
        },
        // {
        //   type: 'number',
        //   label: '收款金额',
        //   controls: true,
        //   span: 12,
        //   display: true,
        //   value: value,
        //   disabled: true,
        //   tip: '合并收款无法更改金额',
        //   prop: 'actualPrice',
        // },
        {
          type: 'datetime',
          label: '收款时间',
          span: 12,
          display: true,
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          value: dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
          prop: 'actualDate',
          disabled: false,
          readonly: false,
          required: true,
          rules: [
            {
              required: true,
              message: '收款时间必须填写',
            },
          ],
        },
        {
          type: 'select',
          label: '收款账号',

          cascader: [],
          span: 12,
          // search: true,
          display: true,

          dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
          dicFormatter: res => {
            return res.data.records;
          },
          props: {
            label: 'abbreviation',
            value: 'id',
            desc: 'desc',
          },
          rules: [
            {
              required: true,
              message: '请选择收款账号',
              trigger: 'blur',
            },
          ],
          prop: 'collectionAccount',
        },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',

          span: 24,
        },
        {
          label: '收款凭证',
          prop: 'files',
          type: 'upload',
          dataType: 'object',
          // listType: 'picture-img',
          loadText: '图片上传中，请稍等',
          span: 24,
          slot: true,
          limit: 1,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
          },
          action: '/blade-resource/attach/upload',
          // uploadAfter: (res, done) => {
          //   coverId = res.id;
          //   done();
          // },
        },
      ],
    },
    callback(res) {
      const bol = res.data.collectList.some(item => {
        return item.actualPrice > item.planCollectionPrice * 1 - item.actualCollection * 1 ;
      });
      if (bol) return ElMessage.warning('收款金额不可大于计划收款金额');
      const value = selectList.value.reduce((pre, cur) => {
        return pre + cur.actualPrice * 1;
      }, 0);
      const data = {
        planIds: selectList.value.map(item => item.id).join(','),
        ...res.data,
        collectionFiles: res.data.files.map(item => item.value).join(','),
        actualPriceList: res.data.collectList.map(item => item.actualPrice).join(','),
        remark: res.data.remark || '' + '合并收款' + value + '元',
      };
      axios.post('/api/vt-admin/sealContractPlanCollection/collectionList', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
        proxy.$refs.crud.toggleSelection();
      });
    },
  });
}
let dialogVisible1 = ref(false);
let detailForm = ref({});

function viewDetail1(row) {
  if (!row.invoiceId) {
    // 没有发票时查询合同详情
    if (!row.sealContractId) {
      return ElMessage.warning('无法获取合同信息');
    }

    axios.get('/api/vt-admin/sealContract/detail?id=' + row.sealContractId).then(res => {
      const contractData = res.data.data;

      // 获取合同的产品信息
      if (contractData.offerId) {
        axios.get('/api/vt-admin/sealContract/productPage?current=1&size=5000&offerId=' + contractData.offerId).then(productRes => {
          // 将产品信息添加到合同数据中
          contractData.invoiceDetailVOList = productRes.data.data.records || [];

          dialogVisible1.value = true;
          // 将合同详情转换为与发票详情相同的数据结构
          detailForm.value = {
            customerName: contractData.customerName,
            customerContactName: contractData.customerContactName,
            offerId: contractData.offerId,
            invoicePriceType: 0, // 默认按产品开票
            sealContractVOList: [contractData], // 将单个合同包装成数组
            isContractDetail: true, // 标识这是合同明细
            currentRowData: row // 保存当前行数据，用于获取 planCollectionPrice
          };
        }).catch(err => {
          ElMessage.error('获取合同产品信息失败');
          // 即使产品信息获取失败，也显示合同基本信息
          dialogVisible1.value = true;
          detailForm.value = {
            customerName: contractData.customerName,
            customerContactName: contractData.customerContactName,
            offerId: contractData.offerId,
            invoicePriceType: 0,
            sealContractVOList: [contractData],
            isContractDetail: true,
            currentRowData: row
          };
        });
      } else {
        // 没有 offerId 时直接显示合同基本信息
        dialogVisible1.value = true;
        detailForm.value = {
          customerName: contractData.customerName,
          customerContactName: contractData.customerContactName,
          offerId: contractData.offerId,
          invoicePriceType: 0,
          sealContractVOList: [contractData],
          isContractDetail: true,
          currentRowData: row
        };
      }
    }).catch(err => {
      ElMessage.error('获取合同详情失败');
    });
    return;
  }

  // 有发票时查询发票详情
  axios.get('/api/vt-admin/sealContractInvoice/detail?id=' + row.invoiceId).then(res => {
    dialogVisible1.value = true;
    detailForm.value = res.data.data;
  }).catch(err => {
    ElMessage.error('获取发票详情失败');
  });
}

// 获取公共信息的方法
function getCommonInfo(field) {
  if (!detailForm.value.sealContractVOList || detailForm.value.sealContractVOList.length === 0) {
    return '';
  }

  // 获取第一个合同的信息作为基准
  const firstContract = detailForm.value.sealContractVOList[0];
  const value = firstContract[field];

  // 检查是否所有合同都有相同的值
  const allSame = detailForm.value.sealContractVOList.every(contract => contract[field] === value);

  return value
}

// 计算合同本次收款金额（产品列表金额相加）
function calculateContractCollectionAmount(contract) {
  // 如果是合同明细且有当前行数据，使用 planCollectionPrice
  if (detailForm.value.isContractDetail && detailForm.value.currentRowData) {
    return parseFloat(detailForm.value.currentRowData.planCollectionPrice) || 0;
  }

  // 发票明细使用 invoiceDetailVOList
  if (contract.invoiceDetailVOList && contract.invoiceDetailVOList.length > 0) {
    return contract.invoiceDetailVOList.reduce((total, product) => {
      const amount = parseFloat(product.totalPrice) || 0;
      return total + amount;
    }, 0);
  }

  // 合同明细使用 productList 或 detailEntityList
  if (contract.productList && contract.productList.length > 0) {
    return contract.productList.reduce((total, product) => {
      const amount = parseFloat(product.totalPrice) || 0;
      return total + amount;
    }, 0);
  }

  // 如果是合同详情，可能直接使用合同总金额
  if (contract.contractTotalPrice) {
    return parseFloat(contract.contractTotalPrice) || 0;
  }

  return 0;
}

// 跳转到合同详情页面
function toDetail(row) {
  if (row.id.split(',').length > 1) {
    return;
  }

  router.push({
    path: '/Contract/customer/compoents/detail',
    query: {
      id: row.id,
      delBtn: 1,
    },
  });
}
function reset() {
  params.value = {
    ids : ''
  }
  onLoad();
}
</script>

<style lang="scss" scoped>
:deep(.planCollectionDays) {
  font-size: 25px;
  font-weight: bolder;
}
.circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 1px solid var(--el-color-success);
  color: var(--el-color-success);
  line-height: 50px;
  text-align: center;
  margin-right: 10px;
}

// 明细对话框样式
.common-info-section {
  margin-bottom: 16px;
}

.common-info-content {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px 16px;

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 0;
    min-height: 28px;

    .info-label {
      font-weight: 500;
      color: #495057;
      font-size: 13px;
      min-width: 70px;
      flex-shrink: 0;
    }

    .info-value {
      color: #212529;
      flex: 1;
      word-break: break-all;
      font-size: 13px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.contracts-section {
  .contract-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .contract-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-left {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .header-title {
        font-size: 15px;
        font-weight: 600;
        color: #303133;

        .contract-link {
          font-size: 15px;
          font-weight: 600;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .collection-amount {
        font-size: 13px;
        color: #606266;

        .amount-value {
          font-weight: 600;
          color: #e6a23c;
          font-size: 14px;
        }
      }
    }
  }
}

// 对话框内容区域样式优化
:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.avue-dialog__footer) {
  padding: 15px 20px;
  border-top: 1px solid #e4e7ed;
  background-color: #fafafa;
  text-align: right;
}
// 统计行样式
:deep(.el-table__footer-wrapper) {
  flex-shrink: 0; // 确保统计行不会被压缩

  .el-table__footer {
    .el-table__cell {
      background-color: #f5f7fa !important;
      font-weight: bold;
      color: #303133;
      border-top: 2px solid #409eff;
    }

    // 金额列特殊样式
    .el-table__cell:last-child {
      color: #e6a23c;
      font-size: 16px;
    }
  }
}
</style>

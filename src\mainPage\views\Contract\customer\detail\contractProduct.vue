<template>
  <div>
    <el-button icon="plus" size="small" type="primary" @click="invoiceApply" v-if="edit"
      >开票申请</el-button
    >

    <el-tabs
      v-model="activeName"
      v-if="changeList.length > 0"
      class="demo-tabs"
      @tab-click="handleTabclick"
    >
      <el-tab-pane
        :label="item.changeDate ? `${item.name}(${item.changeDate})` : item.name"
        v-for="(item, index) in allData"
        :name="index"
      ></el-tab-pane>
    </el-tabs>
    <el-table
      style="margin-top: 10px"
      class="avue-crud"
      :data="allData.length > 0 && allData[activeName].list"
      border
      align="center"
      size="small"
      @scroll="handleScroll"
      ref="tableRef"
      :summary-method="getSummaries"
      show-summary
      v-loading="loading"
      row-key="id"
      :max-height="edit?'height - 40':'auto'"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="55"
        v-if="edit"
        reserve-selection
        :selectable="(row, index) => (row.isInvoice === 0 || row.isInvoice == 2) && row.number > 0"
      />
      <el-table-column
        label="产品名称"
        show-overflow-tooltip
        #default="{ row }"
        prop="customProductName"
      >
        {{ row.customProductName || row.product?.productName }}
      </el-table-column>
      <el-table-column
        label="规格型号"
        show-overflow-tooltip
        #default="{ row }"
        prop="customProductSpecification"
      >
        {{ row.customProductSpecification || row.product?.productSpecification }}
      </el-table-column>
      <!-- <el-table-column label="产品图片" #default="{ row }">
        <el-image
          style="width: 80px"
          :preview-src-list="[row.product.coverUrl]"
          :src="row.product.coverUrl"
        ></el-image>
      </el-table-column> -->
      <el-table-column
        label="产品描述"
        show-overflow-tooltip
        #default="{ row }"
        prop="customProductDescription"
      >
        {{ row.customProductDescription || row.product?.description }}
      </el-table-column>
      <el-table-column label="品牌" width="100" #default="{ row }" prop="product.productBrand">
        {{ row.productBrand || row.product?.productBrand }}
      </el-table-column>
      <el-table-column
        label="单位"
        width="80"
        prop="product.unitName"
        #default="{ row }"
        align="center"
      >
        {{ row.customUnit || row.product?.unitName }}
      </el-table-column>
      <el-table-column label="数量" #default="{ row }" width="80" prop="number" align="center">
      </el-table-column>
      <el-table-column
        label="已开票数量"
        #default="{ row }"
        width="120"
        prop="invoiceNumber"
        align="center"
      >
      </el-table-column>
      <el-table-column label="单价" #default="{ row }" width="120" align="center" prop="zhhsdj">
        <span>{{ row.zhhsdj }}</span>
      </el-table-column>
      <el-table-column label="金额" #default="{ row }" width="130" align="center" prop="zhhsdj">
        <span>{{ (row.zhhsdj * row.number).toLocaleString() }}</span>
      </el-table-column>

      <el-table-column label="是否开票" width="120" align="center">
        <template #default="{ row, size }">
          <el-tag effect="plain" :size="size" v-if="row.isInvoice === 1" type="success">是</el-tag>
          <el-tag effect="plain" v-else-if="row.isInvoice === 2" type="success">部分开票</el-tag>
          <el-tag effect="plain" :size="'small'" v-else type="danger">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        v-if="((permission['contractProduct:edit'] && activeName == 0)) && edit"
        label="操作"
        width="120"
        align="center"
      >
        <template #default="{ row }">
          <el-button text type="primary" icon="edit" @click="handleEdit(row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <el-dialog
    class="avue-dialog avue-crud__dialog"
    title="产品编辑"
    v-model="editDialogVisible"
    width="50%"
  >
    <avue-form ref="editFormRef" :option="editOption" @submit="editSubmit" v-model="editForm">
    </avue-form>
    <template #footer>
      <div class="avue-dialog__footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="$refs.editFormRef.submit()">保存</el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog title="开票申请" v-model="dialogVisible" class="avue-dialog avue-dialog--top">
    <avue-form
      v-if="dialogVisible"
      ref="dialogForm"
      :option="addOption"
      @submit="submit"
      v-model="addForm"
    >
      <template #customerInvoiceInfoId>
        <wfInvoiceDrop
          v-model="addForm.customerInvoiceInfoId"
          :id="props.customerId"
        ></wfInvoiceDrop>
        <productSelect
          ref="productSelectRef"
          @select="handleConfirm"
          :sealContractId="props.sealContractId"
          :offerId="props.offerId"
        ></productSelect>
      </template>
      <template #planCollectionId>
       
        <wfPlanDrop v-model="addForm.planCollectionId" :id="props.sealContractId"></wfPlanDrop>
      </template>
    </avue-form>
    <!-- <slot ></slot> -->
    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button @click="$refs.dialogForm.submit()" type="primary">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="jsx">
import { computed, onMounted, getCurrentInstance, watch, nextTick } from 'vue';
import { dateFormat } from '@/utils/date';
import wfInvoiceDrop from '@/views/Contract/customer/compoents/wf-invoice-drop.vue';
import productSelect from '@/views/Contract/customer/compoents/productSelect.vue';
import { planNameData } from '@/const/const';
import axios from 'axios';
import wfPlanDrop from '@/views/Contract/customer/compoents/wf-plan-drop.vue';
import { useRoute } from 'vue-router';
import { useStore } from 'vuex';
const store = useStore();
let route = useRoute();
const props = defineProps({
  sealContractId: String,
  sealContractInvoiceId: String,
  offerId: String,
  customerId: String,
  contractType: Number,
  edit: {
    type: Boolean,
    default: true,
  },
});
let { proxy } = getCurrentInstance();
let height = ref(0);
onMounted(async () => {
  setTimeout(() => {
    height.value = proxy.$refs.tableRef.$el.getBoundingClientRect().bottom;
    console.log( proxy.$refs.tableRef.$el.getBoundingClientRect(),'height.value');
    
  }, 200);
  
  debugger
  await getProductList();
  await getChangeList();

  handleData();
});

watch(
  () => props.sealContractId,
  async () => {
    await getProductList();
    await getChangeList();
    handleData();
  }
);
let permission = computed(() => store.getters.permission);
let productList = ref([]);
let loading = ref(false);

function getProductList(params) {
  if (props.contractType == 2) return;
  return new Promise((resolve, reject) => {
    loading.value = true;
    axios
      .get('/api/vt-admin/sealContract/productPage', {
        params: {
          current: 1,
          size: 5000,
          offerId: props.offerId,
        },
      })
      .then(res => {
        productList.value = res.data.data.records;
        loading.value = false;
        resolve();
      });
  });
}
let changeList = ref([]);
function getChangeList(params) {
  return new Promise((resolve, reject) => {
    axios
      .post('/api/vt-admin/projectChange/completeListBySealContractId?id=' + props.sealContractId)
      .then(res => {
        changeList.value = res.data.data.map(item => {
          return {
            ...item,
            detailVOList: [
              ...item.detailVOList,
              ...item.deleteList.map(item => {
                return {
                  ...item,
                  number: `-${item.number}`,
                };
              }),
            ],
          };
        });
        resolve();
      });
  });
}
let activeName = ref(0);
let allData = ref([]);

function handleData() {
  allData.value = [
    {
      name: '原合同',
      list: productList.value,
    },
    ...changeList.value.map(item => {
      return {
        ...item,
        name: item.title,
        list: item.detailVOList.map(item => {
          return {
            ...item,
          };
        }),
      };
    }),
  ];
}
let selectList = ref([]);
function handleSelectionChange(list) {
  selectList.value = list;
}

function invoiceApply() {
  if (selectList.value.length === 0) return proxy.$message.warning('请选择开票产品');
  addForm.value.detailDTOList = selectList.value.map(item => {
    return {
      detailId: item.id,
      ...item,
      number: item.number - item.invoiceNumber,
      id: null,
    };
  });
  //  totalAmount(form.value.taxRate);
  addForm.value.invoicePrice = totalAmount();

  dialogVisible.value = true;
}
let dialogVisible = ref(false);
const editDialogVisible = ref(false);
const editForm = ref({});
const editOption = ref({
  labelWidth: 100,
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      label: '关联产品',
      prop: 'productId',
      change: ({ value }) => {
        setProductInfo(value);
      },
      component: 'wf-product-select',
    },

    { label: '产品名称', prop: 'customProductName', type: 'input' },
    { label: '品牌', prop: 'productBrand', type: 'input' },
    { label: '规格型号', prop: 'customProductSpecification', type: 'input' },
    { label: '单位', prop: 'customUnit', type: 'input' },
    { label: '单价', prop: 'zhhsdj', type: 'input' },
    { label: '产品描述', prop: 'customProductDescription', type: 'textarea', span: 24 },
  ],
});
let editFormRef = ref();
function handleEdit(row) {
  nextTick(() => {
    editDialogVisible.value = true;
    editForm.value = { ...row };
  });
}

function editSubmit(form, done) {
  axios
    .post('/api/vt-admin/sealContract/updateProductInfo', form)
    .then(async res => {
      if (res.data.code === 200) {
        proxy.$message.success('修改成功');
        await getProductList();
        await getChangeList();
        handleData();

        emit('success');
        editDialogVisible.value = false;
      }
      done();
    })
    .catch(() => done());
}
function setProductInfo(value) {
  axios
    .get('/api/vt-admin/product/detail', {
      params: {
        id: value,
      },
    })
    .then(res => {
      const { productName, productBrand, unitName, description, productSpecification } =
        res.data.data;
      editForm.value.customProductName = productName;
      editForm.value.customProductSpecification = productSpecification;
      editForm.value.customUnit = unitName;
      editForm.value.productBrand = productBrand;
      editForm.value.customProductDescription = description;
    });
}
let addForm = ref({});
let addOption = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  emptyBtn: false,
  submitBtn: false,
  viewBtn: false,
  size: 'default',
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  addTitle: '开票申请',
  addBtnText: '开票申请',
  menuWidth: 270,
  border: true,
  column: [
    {
      type: 'input',
      label: '开票信息',
      span: 24,
      display: true,
      hide: true,
      value: props.sealContractInvoiceId,
      prop: 'customerInvoiceInfoId',
    },
    {
      type: 'input',
      label: '关联计划',
      span: 24,
      display: true,
      overHidden: true,
      prop: 'planCollectionId',
      control: val => {
        return {
          planName: {
            display: !val,
          },
        };
      },
    },
    {
      label: '计划名称',
      prop: 'planName',
      span: 24,
      type: 'radio',
      dicData: planNameData,

      rules: [
        {
          required: true,
          message: '计划名称必须填写',
        },
      ],
    },
    {
      type: 'date',
      label: '开票日期',
      span: 12,

      display: false,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'invoiceDate',
    },

    {
      type: 'input',
      label: '开票金额',
      width: 110,
      span: 12,
      display: true,
      prop: 'invoicePrice',
      readonly: true,
    },
    {
      type: 'select',
      label: '开票类型',
      dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
      cascader: [],
      span: 12,
      width: 110,
      // search: true,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'invoiceType',
    },
    {
      label: '税率',
      type: 'select',
      width: 80,
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },
      cell: false,
      prop: 'taxRate',
      // change:({value}) => {
      //   totalAmount(value)
      // },
      dicUrl: '/blade-system/dict/dictionary?code=tax',
    },
    {
      label: '开票公司',
      type: 'select',

      prop: 'billingCompany',

      props: {
        label: 'companyName',
        value: 'id',
      },
      dicFormatter: res => {
        return res.data.records;
      },

      overHidden: true,
      cell: false,
      dicUrl: '/api/vt-admin/company/page?size=100',
    },
    {
      label: '申请人',
      type: 'input',
      value: proxy.$store.getters.userInfo.nick_name,
      prop: 'createName',
      readonly: true,
    },
    {
      type: 'date',
      label: '申请时间',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      readonly: true,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
      valueFormat: 'YYYY-MM-DD',
      prop: 'createTime',
    },
    {
      label: '关联产品',
      prop: 'detailDTOList',
      type: 'dynamic',
      hide: true,
      rules: [
        {
          required: true,
          message: '请选择关联产品',
        },
      ],
      span: 24,
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          proxy.$refs.productSelectRef.open();
          // done();
        },
        rowDel: (row, done) => {
          done();
        },

        column: [
          {
            label: '产品',
            prop: 'customProductName',

            cell: false,
          },
          {
            label: '规格型号',
            prop: 'customProductSpecification',

            overHidden: true,
            // search: true,
            span: 24,
            cell: false,
            type: 'input',
          },
          {
            label: '数量',
            prop: 'number',
            type: 'number',
            span: 12,
            cell: true,
            change: val => {
              addForm.value.invoicePrice = totalAmount();
            },
          },
          {
            label: '单价',
            prop: 'zhhsdj',
            type: 'number',
            span: 12,
            cell: false,
            change: val => {
              addForm.value.invoicePrice = totalAmount();
            },
          },
          {
            label: '金额',
            prop: 'totalPrice',
            type: 'number',
            span: 12,
            cell: false,
            formatter: row => {
              return row.number * row.zhhsdj;
            },
          },
        ],
      },
    },
    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
    },
  ],
});
const emit = defineEmits(['success']);
function submit(form, done, loading) {
  const data = {
    ...form,
    sealContractId: props.sealContractId,
    customerId: props.customerId,
    createUser: null,
    createTime: null,
    invoicePriceType:0
  };
  axios
    .post('/api/vt-admin/sealContractInvoice/save', data)
    .then(async res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        await getProductList();
        await getChangeList();
        handleData();

        proxy.$refs.tableRef.clearSelection();
        done();
        dialogVisible.value = false;
      }
    })
    .catch(err => {
      done();
    });
}
function totalAmount(val) {
  // form.value.invoicePrice =
  //   form.value.detailDTOList.reduce((total, item) => (total += item.totalPrice * 1), 0) *
  //   (1 - (val * 1) / 100);
  return addForm.value.detailDTOList.reduce(
    (total, item) => (total += item.number * 1 * (item.zhhsdj * 1)),
    0
  );
}

const getSummaries = param => {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (index == 8) {
      if (activeName.value == 0) {
        const value = data?.reduce((prev, curr) => {
          return (prev += curr.zhhsdj * curr.number * 1);
        }, 0);
        sums[index] = `合计：${value.toFixed(2)}`;
      } else {
        if (allData.value[activeName.value].taxPrice) {
          sums[index] = (
            <div>
              税金：{allData.value[activeName.value].taxPrice.toFixed(2)} <br />
              合计：{allData.value[activeName.value].totalPrice.toFixed(2)}
            </div>
          );
        } else {
          sums[index] = <div>合计：{allData.value[activeName.value].totalPrice.toFixed(2)}</div>;
        }
      }
    }
  });

  return sums;
};
</script>

<style lang="scss" scoped></style>

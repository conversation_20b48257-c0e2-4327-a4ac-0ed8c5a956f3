<template>
  <div class="avue-contail" :class="{ 'avue--collapse': isCollapse }">
    <div class="avue-layout" :class="{ 'avue-layout--horizontal': isHorizontal }">
      <div class="avue-sidebar" v-show="validSidebar">
        <!-- 左侧导航栏 -->
        <logo />
        <sidebar />
      </div>
      <div class="avue-main">
        <!-- 顶部导航栏 -->
        <top ref="top" />
        <!-- 顶部标签卡 -->
        <tags />
        <search class="avue-view" v-show="isSearch"></search>
       
        <!-- 工具栏 -->
        <!-- <Util></Util> -->
        <TopMenu
          v-if="$store.getters.website.setting.newGrid && $store.getters.topMenu.length != 0"
        >
        </TopMenu>
        <!-- 主体视图层 -->
       
        <div id="avue-view" v-show="!isSearch" v-if="isRefresh">
          
          <router-view style="height: 100%;" #="{ Component }">
            <keep-alive :include="$store.getters.tagsKeep">
              <transition name="slide-fade" mode="out-in" appear>
                <component :is="Component" />
              </transition>
            </keep-alive>
          </router-view>
        </div>
      </div>
    </div>
    <!-- <wechat></wechat> -->
  </div>
</template>

<script>
import index from '@/mixins/index';
import wechat from './wechat.vue';
import Util from './util.vue';
import TopMenu from './topMenu.vue';
import { validatenull } from 'utils/validate';
import { mapGetters } from 'vuex';
import tags from './tags.vue';
import search from './search.vue';
import logo from './logo.vue';
import top from './top/index.vue';
import sidebar from './sidebar/index.vue';

export default {
  mixins: [index],
  components: {
    top,
    logo,
    tags,
    search,
    sidebar,
    wechat,
    Util,
    TopMenu,
  },
  name: 'index',
  provide() {
    return {
      index: this,
    };
  },
  computed: {
    ...mapGetters([
      'isHorizontal',
      'isRefresh',
      'isLock',
      'isCollapse',
      'isSearch',
      'menu',
      'setting',
    ]),
    validSidebar() {
      return !(
        (this.$route.meta || {}).menu === false || (this.$route.query || {}).menu === 'false'
      );
    },
  },
  props: [],
  mounted() {
    console.log(this.$store.getters.menuAll);
  },
  methods: {
    //打开菜单
    openMenu(item = {}) {
    
      this.$store.dispatch('GetMenu', item.id).then(data => {
        console.log(data,'getMenu')
        if (data.length !== 0) {
          this.$router.$avueRouter.formatRoutes(data, true);
        }
        //当点击顶部菜单后默认打开第一个菜单
        /*if (!this.validatenull(item)) {
          let itemActive = {},
            childItemActive = 0;
          if (item.path) {
            itemActive = item;
          } else {
            if (this.menu[childItemActive].length === 0) {
              itemActive = this.menu[childItemActive];
            } else {
              itemActive = this.menu[childItemActive].children[childItemActive];
            }
          }
          this.$store.commit('SET_MENU_ID', item);
          this.$router.push({
            path: this.$router.$avueRouter.getPath({
              name: (itemActive.label || itemActive.name),
              src: itemActive.path
            }, itemActive.meta)
          });
        }*/
      });
    },
  },
};
</script>
<style>
.slide-fade-enter-active {
  transition: all 0.4s;
}

.slide-fade-leave-active {
}

.slide-fade-enter-from {
  /* transform: rotateX(30deg); */
  opacity: 0;
}
.slide-fade-leave-to {
}
</style>

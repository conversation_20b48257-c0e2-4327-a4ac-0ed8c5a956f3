<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      :permission="permission"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #payStatus="{ row }">
        <el-tag
          :type="row.payStatus == 0 ? priamry : row.payStatus == 3 ? 'danger' : 'success'"
          effect="plain"
          >{{ row.$payStatus }}</el-tag
        >
      </template>
      <template #menu="{ row }">
        <el-button
          text
          @click="openDetailDrawer(row)"
          icon="edit"
          type="primary"
          v-if="row.payStatus == 1"
          >付款</el-button
        >
      </template>
      <template #objectName="{ row }">
        <el-link type="primary" @click="openDetailDrawer(row)">{{ row.objectName }}</el-link>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
  <el-drawer v-model="drawerVisible" title="工单详情" size="60%">
    <avue-form :option="detailOption" v-model="detailData">
      <template #taskName>
        <el-descriptions :title="detailData.labelName">
          <el-descriptions-item label="要求交付时间">{{
            (detailData.planStartTime && detailData.planStartTime.split(' ')[0]) || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="服务客户">{{
            detailData.finalCustomer || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="服务联系人">{{
            detailData.contact || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{
            detailData.contactPhone || '--'
          }}</el-descriptions-item>
          <el-descriptions-item label="服务地址" :span="2">
            {{ detailData.distributionAddress || '--' }}
          </el-descriptions-item>
          <el-descriptions-item label="任务描述" :span="3">
            <span style="white-space: pre-line"> {{ detailData.taskDescription || '--' }}</span>
          </el-descriptions-item>
        </el-descriptions>
      </template>
      <template #filesDetail>
        <File :fileList="detailData.fileList"></File>
      </template>
      <template #completeFiles>
        <File :fileList="detailData.completeFilesList"></File>
      </template>
    </avue-form>
    <template #footer v-if="payForm.payStatus == 1">
      <div style="border-top: 1px dashed #ccc; padding-top: 10px">
        <avue-form
          v-model="payForm"
          ref="payConfirmRef"
          :option="payOption"
          @submit="payConfirm"
        ></avue-form>
      </div>
    </template>
  </el-drawer>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import moment from 'moment';
// import wokerOrderSelect from './component/wokerOrderSelect.vue';
let wokerOrderSelectRef = ref(null);

let currentId = ref(null);
let currentRow = ref({});
const detailData = ref({});
const drawerVisible = ref(false);

let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 100,
  detailBtn: true,

  dialogWidth: '500',
  //   dialogType: 'drawer',
  border: true,
  column: [
    {
      label: '关联合同',
      prop: 'contractName',
      search: true,
    },
    {
      label: '关联工单',
      prop: 'objectName',
      type: 'input',
      readonly: true,
      span: 24,
      placeholder: '请输入工单',
      click: row => {
        wokerOrderSelectRef.value.dialogVisible = true;
      },
    },
    // {
    //   label: '工单处理人',
    //   prop: 'handleUserName',
    //   type: 'input',
    //   span: 24,
    //   hide: true,
    // },
    {
      label: '付款金额',
      prop: 'totalPrice',
      type: 'number',
      span: 24,
    },
    {
      label: '申请备注',
      prop: 'applyContent',
      span: 24,
      type: 'textarea',
    },
    {
      label: '状态',
      type: 'select',
      prop: 'payStatus',
      display: false,
      search: true,
      dicData: [
        {
          value: 1,
          label: '审核通过',
        },
        {
          value: 2,
          label: '已付款',
        },
      ],
    },

    {
      label: '审核备注',
      prop: 'auditRemark',
      overHidden: true,
    },
    {
      label: '申请人',
      prop: 'createName',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/sealContractObjectPayment/save';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContractObjectPayment/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function openDetailDrawer(row) {
  currentId.value = row.id;
  currentRow.value = row;
  axios
    .get('/api/vt-admin/sealContractObject/detail', {
      params: {
        id: row.objectId,
      },
    })
    .then(res => {
      detailData.value = res.data.data;
      payForm.value.payStatus = row.payStatus;
      payForm.value.totalPrice = row.totalPrice;
      payForm.value.id = row.id
      payForm.value.payTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss');
      drawerVisible.value = true;
    });
}

function handleDetailSubmit(form, done) {
  // 此处可添加提交逻辑
  proxy.$message.success('详情已更新');
  done();
  drawerVisible.value = false;
  onLoad(); // 刷新列表
}

function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        selectType: 2,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();
function handlewokerOrderSelectConfirm(data) {
  const { id, handleUserName, orderPrice, objectName } = data[0];
  form.value.objectId = id;
  form.value.objectName = objectName;
  form.value.handleUserName = handleUserName;
  form.value.totalPrice = orderPrice;
}
function permission(key, row) {
  console.log(key, row);

  if (['editBtn', 'delBtn'].includes(key)) {
    if (row.payStatus == 0) {
      return true;
    } else {
      return false;
    }
  } else {
    return true;
  }
}
function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
let imgId = ref('');

let auditForm = ref({});
function confirm() {
  axios
    .post('/api/vt-admin/sealContractObjectPayment/audit', {
      id: currentId.value,
      ...auditForm.value,
    })
    .then(e => {
      proxy.$message.success('操作成功');
      proxy.$store.dispatch('getMessageList');
      onLoad();
      drawerVisible.value = false;
    });
}

let detailOption = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 200,
  labelWidth: 120,
  updateBtnText: '提交',
  dialogWidth: '1000',
  dialogType: 'drawer',
  border: true,
  submitBtn: false,
  emptyBtn: false,
  detail: true,

  group: [
    {
      label: '任务信息',
      prop: 'taskInfo',
      addDisplay: true,
      disabled: true,
      column: [
        {
          labelWidth: 0,
          span: 24,
          prop: 'taskName',
        },
      ],
    },
    {
      label: '工单信息',
      prop: 'workOrderInfo',
      column: [
        {
          label: '工单类型',
          type: 'radio',
          dicData: [
            {
              value: 0,
              label: '内部工单',
            },
            {
              value: 1,
              label: '外部工单',
            },
          ],
          rules: [
            {
              required: true,
              message: '请选择工单类型',
            },
          ],
          prop: 'objectType',
          control: val => {
            return {
              orderPrice: {
                display: val == 1,
              },
            };
          },
        },
        {
          label: '工单名称',
          prop: 'objectName',
          placeholder: '请输入工单名称',
          span: 12,
          rules: [
            {
              required: true,
              message: '请输入工单名称',
            },
          ],
          type: 'input',
        },
        {
          type: 'datetime',
          label: '服务时间',
          span: 12,

          width: 140,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'planTime',
          rules: [
            {
              required: true,
              message: '请选择服务时间',
              trigger: 'blur',
            },
          ],
        },

        {
          label: '工单价格',
          prop: 'orderPrice',
          placeholder: '请输入工单价格',
          type: 'number',
          span: 12,
        },
        {
          label: '工单描述',
          prop: 'remark',
          placeholder: '请输入工单描述',
          type: 'textarea',
          span: 24,
        },

        {
          label: '工单附件',
          prop: 'filesDetail',
          type: 'upload',

          viewDisplay: true,
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },

        {
          label: '指派工程师',
          prop: 'handleUserName',
        },
      ],
    },
    {
      label: '完成信息',
      prop: 'finishInfo',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,

       column: [
        {
          type: 'date',
          label: '实际完成时间',
          span: 12,
         
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'completeTime',
        },
        {
          label: '实际使用工时',
          prop: 'useTimes',
          type: 'number',
        },
        {
          label: '附件',
          prop: 'completeFiles',
          type: 'upload',
          overHidden: true,
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          slot: true,
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '服务复盘',
          prop: 'serviceReorder',
          type: 'textarea',
         
          span: 24,
          value: '问题描述:\n\n原因分析:\n\n解决方案:\n\n服务成果:\n',
        },
        {
          label: '备注',
          prop: 'completeRemark',
          type: 'textarea',
          span: 24,
        },
      ],
    },
  ],
});

let payForm = ref({});
let payOption = ref({
  column: [
    {
      type: 'number',
      label: '付款金额',
      span: 12,
      disabled: true,

      prop: 'totalPrice',
    },
    {
      type: 'datetime',
      label: '付款日期',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',

      prop: 'payTime',
    },
    {
      type: 'select',
      label: '付款账号',

      cascader: [],
      span: 12,
      display: true,

      dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
      dicFormatter: res => {
        return res.data.records;
      },
      props: {
        label: 'abbreviation',
        value: 'id',
        desc: 'desc',
      },
      prop: 'paymentAccount',
      rules: [
        {
          required: true,
          message: '请选择付款账号',
          trigger: 'change',
        },
      ],
    },

    {
      type: 'input',
      label: '备注',
      span: 12,
      display: true,
      prop: 'paymentRemark',
    },
    {
      label: '付款凭证',
      prop: 'paymentFiles',
      type: 'upload',
      dataType: 'object',
      listType: 'picture-img',
      loadText: '图片上传中，请稍等',
      span: 12,
      slot: true,
      limit: 1,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'originalName',
      },
      action: '/blade-resource/attach/upload',
      uploadAfter: (res, done) => {
        imgId.value = res.id;
        done();
      },
    },
  ],
});
let payConfirmRef = ref(null);
let payType = ref(0); // 0 单项支付 1 合并支付

function payConfirm(form, done, loading) {
  axios
    .post('/api/vt-admin/sealContractObjectPayment/payment', {
      ...form,
      paymentFiles: imgId.value,
    })
    .then(e => {
      proxy.$message.success('操作成功');
      proxy.$store.dispatch('getMessageList');
     
      imgId.value = '';
      onLoad();
      done()
      drawerVisible.value = false;
    });
}
</script>

<style lang="scss" scoped>
:deep(.avue-upload) {
  text-align: left;
}
</style>

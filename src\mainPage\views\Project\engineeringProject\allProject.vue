<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      :before-open="beforeOpen"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
    <template #menu-left>
        <div style="display: flex; gap: 10px">
          
          <div style="display: flex; align-items: center">
            <span style="font-weight: bolder">项目总额：</span>
            <el-text type="primary" size="large"
              >￥{{ (totalPrice * 1).toLocaleString() || 0 }}</el-text
            >
          </div>
        </div>
      </template>
      <template #menu="{ row }">
        <el-button type="primary" @click="edit(row)" text icon="edit">分配人员</el-button>
        <el-button type="primary" @click="handleView(row)" text icon="view">详情</el-button>
      </template>
      <template #projectCode="{ row, index }">
        <el-link @click="handleView(row)" type="primary">{{ row.projectCode }}</el-link>
      </template>
      <template #projectSchedule="{ row, index }">
        <div style="display: flex; align-items: center;justify-content: center">
          <span style="font-weight: bolder">{{ row.projectSchedule }}</span>
          <el-icon size="20" v-if="row.isAddProjectPlan == 0 && row.projectSchedule != '100%'"  title="项目计划未填写"
            ><WarningFilled
              style="color: var(--el-color-danger); cursor: pointer" /></el-icon>
          <el-icon size="20"  v-if="row.isAddProjectHours == 0 && row.projectSchedule != '100%'"  title="项目工时未填写"
            ><WarningFilled
              style="color: var(--el-color-warning); cursor: pointer"
             
             
          /></el-icon>
        </div>
      </template>
      <template #projectStatus1="{ row }">
        <div style="display: flex; align-items: center;justify-content: center;gap: 3px;">
          <el-tag size="small" style="font-weight: bolder;"
          :type="
            row.projectStatus == 0
              ? 'info'
              : row.projectStatus == 1
              ? 'warning'
              : row.projectStatus == 2
              ? 'success'
              : 'success'
          "
          effect="dark"
          >{{ ['未施工', '施工中', '施工完成', '验收完成'][row.projectStatus] }}</el-tag
        >
      
        </div>
        
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  searchIcon:true,
  searchIndex:5,
  menuWidth: 180,
  border: true,
  column: [
    {
      label: '项目编号',
      prop: 'projectCode',
      width: 150,
      overHidden: true,
    
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: '项目名称',
      prop: 'projectName',
      width: 250,
      overHidden: true,
      search: true,
      component: 'wf-project-drop',
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true,
      search: true,
      addDisplay: false,component: 'wf-customer-drop',
      editDisplay: false,
    },
    {
      label: '项目总额',
      prop: 'projectPrice',
      addDisplay: false,
      editDisplay: false,
    },
    
    {
      label: '业务员',
      prop: 'businessUserName',
      addDisplay: false,
      search: true,
      width: 100,
      component: 'wf-user-drop',
      editDisplay: false,
     
    },
    {
      label: '项目状态',
      prop: 'projectStatus',
      overHidden: true,
      span: 24,hide: true,
      type: 'select',
      dicData: [
        {
          label: '未施工',
          value: 0,
        },
        {
          label: '施工中',
          value: 1,
        },
        {
          label: '施工完成',
          value: 2,
        },
        {
          label: '验收完成',
          value: 3,
        },
        
      ],
      search: true,
    },
    {
      label: '售前技术',
      prop: 'preSaleTechnology',
      addDisplay: false,
      search: true,
      component: 'wf-user-drop',
      params: {
        roleKeys: 'technology_leader,technology_person',
      },
      searchSpan: 3,
      editDisplay: false,
      
    },
    {
      label: '协助人员',
      prop: 'technologyAssistUserName',
      addDisplay: false,
    },
    {
      label: '合同名称',
      prop: 'sealContractId',
      width: 250,
      component: 'wf-contract-select',
      params: {
        Url: '/api/vt-admin/sealContract/pageForAddProject?isProject=0',
      },
      overHidden: true,
      hide: true,
    },
    {
      label: '项目经理',
      prop: 'projectLeader',
      width: 250,
      component: 'wf-user-select',
      overHidden: true,
      hide: true,
    },
    {
      label: '项目属性',
      prop: 'projectAttribute',
      overHidden: true,
      dicData: [
        {
          label: '总包',
          value: 0,
        },
        {
          label: '分包',
          value: 1,
        },
      ],
      type: 'radio',
      hide: true,
    },

    {
      label: '项目地址',
      prop: 'projectAddress',
      overHidden: true,
      type: 'input',
      hide: true,
    },

    {
      label: '附件',
      prop: 'fileIds',
      type: 'upload',
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      hide: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },

    {
      label: '项目经理',
      prop: 'technologyUserName',
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
      search: true,
      component: 'wf-user-drop',
      params: {
        roleKeys: 'project_leader',
      },
      searchSpan: 3,
    },
    {
      label: '项目进度',
      prop: 'projectSchedule',
      overHidden: true,
      span: 24,
      addDisplay: false,
      editDisplay: false,
      type: 'textarea',
    },
    {
      label: '签订时间',
      prop: 'signDate',
      addDisplay: false,
      width:110,
      component:'wf-daterange-search',
      searchSpan:6,
      overHidden:true,
      search:true,
      editDisplay: false,
    },

    // {
    //   label: '客户联系人',
    //   prop: 'customerContact',
    //   addDisplay: false,
    // },

    // {
    //   label: '联系电话',
    //   prop: 'customerPhone',
    //   addDisplay: false,
    // },
    {
      label: '开工时间',
      prop: 'startDate',
      type: 'date',
      span: 24,
      overHidden:true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    {
      label: '要求交付时间',
      prop: 'deliveryDate',
      type: 'date',
      span: 24,
      width: 120,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    {
      label: '项目状态',
      prop: 'projectStatus1',
      overHidden: true,
      span: 24,
     
      type: 'select',
      dicData: [
        {
          label: '未施工',
          value: 0,
        },
        {
          label: '施工中',
          value: 1,
        },
        {
          label: '施工完成',
          value: 2,
        },
        {
          label: '验收完成',
          value: 3,
        },
        
      ],
   
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/project/save';
const delUrl = '';
const updateUrl = '/api/vt-admin/project/update';
const tableUrl = '/api/vt-admin/project/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let totalPrice = ref(0)
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        selectType: 2,
        businessName: params.value.businessUserName,
        startTime: params.value.signDate ? params.value.signDate[0] : null,
        endTime: params.value.signDate ? params.value.signDate[1] : null,
        createTime:null,
        signDate: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
    axios
    .get('/api/vt-admin/project/pageStatistics', {
      params: {
        ...params.value,
        selectType: 2,
        businessName: params.value.businessUserName,
        startTime: params.value.signDate ? params.value.signDate[0] : null,
        endTime: params.value.signDate ? params.value.signDate[1] : null,
        signDate: null,
        createTime:null,
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
    });
    
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    projectFiles: form.fileIds && form.fileIds.map(item => item.value).join(','),
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    projectFiles: row.fileIds && row.fileIds.map(item => item.value).join(','),
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function beforeOpen(done, type) {
  if (type == 'edit') {
    form.value.fileIds = (form.value.attachList || []).map(item => {
      return {
        value: item.id,
        label: item.originalName,
      };
    });
  }
  done();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function handleView(row) {
  router.push({
    path: '/Project/detail/detail',
    query: {
      id: row.id,
      name: row.projectName + '-详情',
    },
  });
}
function edit(row) {
  proxy.$refs.dialogForm.show({
    title: '分配项目经理',
    option: {
      column: [
        {
          label: '项目经理',
          prop: 'projectLeader',
          value: row.projectLeader,
          component: 'wf-user-select',   params: {
            userUrl:'/api/blade-system/search/user?functionKeys=projectManager'
          },
        },
        {
          label: '协助人员',
          prop: 'technologyAssistUser',
          value: row.technologyAssistUser,
          component: 'wf-user-select',   params: {
            userUrl:'/api/blade-system/search/user?functionKeys=assistant'
          },
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/project/distributionProjectLeader', {
          id: row.id,
          ...res.data,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          proxy.$store.dispatch('getMessageList');
          res.close();
          onLoad();
        });
    },
  });
}
</script>

<style lang="scss" scoped></style>

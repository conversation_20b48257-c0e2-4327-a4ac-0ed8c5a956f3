<template>
  <basic-container :shadow="props.customerId ? 'shadow' : 'always'">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @keyup.enter="onLoad"
      @size-change="onLoad"
      v-model="form"
      @expand-change="expandChanges"
      :row-class-name="getClass"
    >
      <template #menu-left="{}">
        <div style="display: flex">
          <!-- <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button> -->
          <div style="display: flex; align-items: center">
            <span style="font-weight: bolder">总报价额：</span>
            <el-text type="primary" size="large">￥{{ (totalPrice * 1).toLocaleString() }}</el-text>
          </div>
        </div>
      </template>
      <template #menu="{ row }">
        <div style="display: flex; justify-content: center">
          <!-- <div v-if="row.offerPeople == $store.getters.userInfo.user_id">
            <el-button
              text
              type="primary"
              icon="el-icon-link"
              @click="handleEditOffer(row)"
              v-if="row.offerStatus == 0 && $store.getters.permission.quation_qua"
              >报价</el-button
            >

          
          </div> -->
          <!-- <el-button
            text
            type="primary"
            icon="el-icon-edit"
            @click="handleEdit(row)"
            v-if="row.offerStatus == 0 && $store.getters.permission.quation_edit"
            >编辑</el-button
          >
          <el-button
            text
            type="primary"
            v-if="row.offerStatus == 0 && $store.getters.permission.quation_confirm_customer"
            icon="el-icon-view"
            @click="customerConfirm(row)"
            >客户确认</el-button
          > -->
          <el-button
            text
            type="primary"
            v-if="row.isHasOption == 1 && row.offerStatus != 4 && row.offerStatus != 2"
            icon="edit"
            @click="handleEdit(row)"
            >编辑</el-button
          >
          <el-button
            text
            type="primary"
            v-if="row.offerStatus == 2"
            icon="edit"
            @click="handleEditBaseInfo(row)"
            >编辑</el-button
          >
          <el-button text type="primary" icon="el-icon-view" @click="handleView(row)"
            >详情</el-button
          >
          <el-button text type="primary" icon="el-icon-clock" @click="viewProcess(row)"
            >流程</el-button
          >
          <el-button text type="primary" icon="el-icon-clock" @click="viewHistory(row)"
            >历史</el-button
          >
          <el-button
            text
            type="primary"
            v-if="row.isHasOption !== 1 && row.offerStatus != 2"
            icon="delete"
            @click="$refs.crud.rowDel(row)"
            >删除</el-button
          >
        </div>
      </template>
      <template #auditStatus="{ row }">
        <div v-if="row.auditStatus == 1 || row.auditStatus == 2">
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="row.technologyRemark"
            :disabled="!row.technologyRemark"
            placement="top-start"
            v-if="row.auditStatus == 1"
          >
            <el-badge :hidden="!row.technologyRemark" is-dot class="item">
              <el-tag effect="plain" v-if="row.auditStatus == 1" size="small" type="success"
                >审核成功</el-tag
              >
            </el-badge>
          </el-tooltip>
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="row.auditReason"
            placement="top-start"
            v-if="row.auditStatus == 2"
          >
            <el-tag effect="plain" size="small" type="danger">审核失败</el-tag>
          </el-tooltip>
        </div>
        <div v-else>
          <el-tag effect="plain" v-if="row.auditType == 1" size="small" type="info"
            >待采购审核</el-tag
          >
          <el-tag effect="plain" v-if="row.auditType == 2" size="small" type="info"
            >待主管审核</el-tag
          >
          <el-tag effect="plain" v-if="row.auditType == 3" size="small" type="info"
            >待总经理审核</el-tag
          >
        </div>
      </template>
      <template #offerStatus="{ row }">
        <el-tooltip
          :content="row.failReason || row.pauseReason"
          v-if="row.offerStatus == 4 || row.offerStatus == 6"
        >
          <el-tag effect="plain" size="small" type="danger">{{ row.$offerStatus }}</el-tag>
        </el-tooltip>
        <span v-else>{{ row.$offerStatus }}</span>
      </template>
      <template #expand="{ row }">
        <div style="padding: 0 10px">
          <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
            <el-col :span="5" v-for="item in row.offerVOS || []" :key="item.id">
              <el-card
                class="maCard"
                :shadow="item.offerVersionType == 0 ? 'never' : 'always'"
                style="height: 200px; overflow-y: auto; border: 1px solid var(--el-color-primary)"
              >
                <template #header>
                  <el-row>
                    <el-col
                      :span="24"
                      style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap"
                    >
                      <el-tooltip :content="item.offerName">
                        <el-text
                          type="primary"
                          size="default"
                          style="
                            font-weight: bolder;
                            font-family: Inter, Helvetica Neue, Helvetica, PingFang SC,
                              Hiragino Sans GB, Microsoft YaHei, \5fae\8f6f\96c5\9ed1, Arial,
                              sans-serif;
                          "
                          >{{ item.offerName }}</el-text
                        >
                      </el-tooltip>
                    </el-col>

                    <el-col :span="24">
                      <div
                        v-if="item.offerVersionType == 1"
                        style="display: flex; justify-content: flex-end"
                      >
                        <!-- <el-button
                          type="info"
                          @click="handleEditSub(item)"
                          circle
                          icon="edit"
                          text
                        ></el-button> -->
                        <el-button
                          type="info"
                          title="预览"
                          @click="handleViewSub(item)"
                          circle
                          icon="view"
                          text
                        ></el-button>
                        <el-button
                          text
                          title="下载"
                          icon="el-icon-download"
                          :type="item.offerStatus == 1 && item.auditStatus == 1 ? 'danger' : 'info'"
                          @click="download(item)"
                        ></el-button>
                      </div>
                      <div v-else style="display: flex; justify-content: flex-end">
                        <el-button
                          text
                          type="primary"
                          icon="el-icon-view"
                          @click="handleView(row)"
                        ></el-button>
                        <el-button
                          text
                          type="primary"
                          icon="el-icon-clock"
                          @click="viewProcess(row)"
                        ></el-button>
                        <el-button
                          text
                          type="primary"
                          icon="el-icon-clock"
                          @click="viewHistory(row)"
                        ></el-button>
                      </div>
                    </el-col>
                  </el-row>
                </template>
                <el-row style="margin-top: 10px; margin-left: 5px">
                  <el-col :span="20">
                    <el-text
                      style="font-size: 30px"
                      :type="item.optionStatus == 0 ? 'info' : 'success'"
                      >￥{{ item.offerPrice }}</el-text
                    >
                  </el-col>
                  <el-col :span="4">
                    <el-tag
                      effect="plain"
                      v-if="row.offerStatus == 2 || row.offerStatus == 4"
                      type="danger"
                    >
                      {{ row.offerStatus == 2 ? '失效' : '失单' }}
                    </el-tag>
                  </el-col>
                </el-row>
                <el-row style="margin-top: 10px; margin-left: 6px">
                  <el-col :span="24">
                    <el-text type="info">{{ item.remark }}</el-text>
                  </el-col>
                </el-row>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-dialog title="历史记录" v-model="dialogVisible">
      <history :id="currentId"></history>
    </el-dialog>
    <process ref="processRef" :currentId="currentId" :type="1"></process>
    <el-drawer title="基本信息" v-model="drawer">
      <avue-form
        :option="baseInfoOption"
        ref="addForm"
        style="margin-top: 5px"
        @submit="baseSubmit"
        v-model="baseForm"
      ></avue-form>
      <template #footer>
        <div style="flex: auto">
          <el-button type="primary" @click="$refs.addForm?.submit">确认</el-button>
        </div>
      </template>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { offerStatus, auditStatus } from '@/const/const.js';
import history from './compoents/history.vue';
import process from './compoents/process.vue';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 250,
  searchIcon: true,
  searchIndex: 4,
  border: true,
  expand: true,
  rowKey: 'id',
  column: [
    {
      label: '报价名称',
      prop: 'offerName',
      width: 250,
      overHidden: true,
      search: true,
    },

    {
      label: '关联商机',
      prop: 'businessOpportunityName',
      width: 250,
      overHidden: true,
      component: 'wf-bussiness-drop',
      search: true,
    },
    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true,
      search: !props.customerId,
      component: 'wf-customer-drop',
      hide: !!props.customerId,
    },
    // {
    //   label: '关联联系人',
    //   prop: 'contactPersonName',
    // },
    {
      label: '报价状态',
      type: 'select',
      dicData: offerStatus,
      prop: 'offerStatus',
      search: true,
    },
    {
      label: '审核状态',
      type: 'select',
      dicData: auditStatus,
      prop: 'auditStatus',
      search: true,
    },
    {
      label: '业务员',
      prop: 'businessPersonName',
      component: 'wf-user-drop',
      search: true,
      formatter(row, column, cellValue, index) {
        return row.createName;
      },
    },
    {
      label: '产品名称',
      type: 'input',
      width: 100,
      hide: true,
      prop: 'productName',
      search: true,
    },
    {
      label: '报价时间',
      prop: 'offerDate',
      type: 'date',
      search: true,
      component: 'wf-daterange-search',
      search: true,
      valueFormat: 'YYYY-MM-DD',
      searchSpan: 6,
    },
    {
      label: '总金额（元）',
      prop: 'offerPrice',
      formatter: row => {
        return parseFloat(row.offerPrice).toLocaleString();
      },
    },
    {
      label: '报价有效期（天）',
      prop: 'offerValidity',
    },
    {
      label: '创建时间',
      prop: 'createTime',
      type: 'date',
      format: 'YYYY-MM-DD',
    },
    // {
    //   label: '联系电话',
    //   prop: 'followPerson',
    // },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '/api/vt-admin/offer/deletedById?id=';
const updateUrl = '';
const tableUrl = '/api/vt-admin/offer/page';
let params = ref({});
let tableData = ref([]);
const props = defineProps({
  customerId: String,
});
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
let loading = ref(false);
let totalPrice = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        customerId: props.customerId || null,
        selectType: 2,
        startTime: params.value.offerDate && params.value.offerDate[0],
        endTime: params.value.offerDate && params.value.offerDate[1],
        offerDate: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/offer/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        selectType: 2,
        customerId: props.customerId || null,
        startTime: params.value.offerDate && params.value.offerDate[0],
        endTime: params.value.offerDate && params.value.offerDate[1],
        offerDate: null,
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function handleAdd() {
  router.push('/CRM/quotation/compoents/add');
}
function handleViewSub(row) {
  if (row.isHasDataJson == 1) {
    router.push({
      path: '/CRM/programme/compoents/update',
      query: {
        id: row.id,
        // name: row.optionName,
        type: 'detail',
        isEditSubedition: 1,
      },
    });
  } else if (row.isHasDataJson == 0 || !row.isHasDataJson) {
    router.push({
      path: '/CRM/programme/compoents/updateVersion3',
      query: {
        id: row.optionId,
        // name: row.optionName,
        type: 'detail',
        isEditSubedition: 1,
      },
    });
  } else {
    proxy.$message.error('未查询到方案');
  }
}
function handleEdit(row) {
  if (row.isHasOption == 1) {
    router.push({
      path: '/CRM/programme/compoents/updateVersion3',
      query: {
        id: row.businessOpportunityId,
        type: 'edit',
        name: row.name,
        isAdmin: 'admin',
      },
    });
  } else {
  }
}
function handleEditOffer(row) {
  router.push({
    path: '/CRM/quotation/compoents/editOffer',
    query: {
      id: row.id,
    },
  });
}
function handleView(row) {
  if (row.dataJson && row.isHasOption != 1) {
    router.push({
      path: '/CRM/quotation/compoents/add',
      query: {
        id: row.id,
        name: row.offerName,
        type: 'detail',
      },
    });
  } else if (row.dataJson && row.isHasOption == 1) {
    router.push({
      path: '/CRM/programme/compoents/update',
      query: {
        id: row.businessOpportunityId,
        type: 'detail',
        name: row.offerName,
        // businessOpportunityId: row.businessOpportunityId,
      },
    });
  } else if (!row.dataJson && row.isHasOption == 0) {
    router.push({
      path: '/CRM/quotation/compoents/addVersion3',
      query: {
        id: row.id,
        type: 'detail',
        name: row.offerName,
        repairId: row.repairId,
        businessOpportunityId: row.isHasOption == 1 ? row.businessOpportunityId : null,
      },
    });
  } else {
    router.push({
      path: '/CRM/programme/compoents/updateVersion3',
      query: {
        id: row.businessOpportunityId,
        type: 'detail',
        name: row.offerName,
        // businessOpportunityId: row.businessOpportunityId,
      },
    });
  }

  // }
}
function customerConfirm(row) {
  proxy.$refs.dialogForm.show({
    title: row.name,
    option: {
      column: [
        {
          label: '凭证',
          prop: 'confirmFiles',
          type: 'upload',
          dataType: 'object',
          listType: 'picture-img',
          loadText: '图片上传中，请稍等',
          span: 24,
          slot: true,
          limit: 1,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'link',
            name: 'originalName',
          },
          rules: [
            {
              required: true,
              message: '请上传凭证',
            },
          ],
          action: '/blade-resource/attach/upload',
          uploadAfter: (res, done) => {
            row.confirmFiles = res.id;
            done();
          },
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/offer/customerConfirm', {
          id: row.id,
          confirmFiles: row.confirmFiles,
        })
        .then(r => {
          proxy.$message.success(r.data.msg);
          res.close();
          onLoad();
        });
    },
  });
}
function submit(row) {
  proxy
    .$confirm('提交之后将会不可修改,确认报价?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios
        .post('/api/vt-admin/offer/updateOffer', {
          ...row,
          id: row.id,
          offerStatus: 1,
        })
        .then(res => {
          proxy.$message.success(res.data.msg);
        });
    });
}
let dialogVisible = ref(false);
let currentId = ref(null);
function viewHistory(row) {
  currentId.value = row.id;
  dialogVisible.value = true;
}
// 流程
function viewProcess(row) {
  currentId.value = row.id;
  proxy.$refs.processRef.open();
}
function expandChanges(row, expendList) {
  if (expendList.length) {
    option.value.expandRowKeys = [];
    if (row) {
      option.value.expandRowKeys.push(row.id);
    }
  } else {
    option.value.expandRowKeys = [];
  }
}
function getClass({ row }) {
  console.log(row);
  return row.isHasManyOffer == 0 ? 'hide_icon' : '';
}
function handleEditBaseInfo(row) {
  baseForm.value = row;
  drawer.value = true;
}
let drawer = ref(false);
let baseForm = ref({});
let baseInfoOption = ref({
  submitBtn: false,
  labelWidth: 140,
  detail: false,
  emptyBtn: false,
  column: [
    {
      label: '关联商机',
      prop: 'businessOpportunityId',
      component: 'wf-business-select',
      span: 24,
      disabled:true,
      params: {
        isNew: true,
      },
      change: val => {
        if (val.value) {
          getBusinessDetail(val.value);
        }
      },
    },
    {
      label: '报价名称',
      prop: 'offerName',
      span: 24,
      rules: [
        {
          required: true,
          message: '请填写方案名称',
        },
        {
          validator: (rule, value, callback) => {
            const reg = /^[^/\\?？\[\]]*$/;
            console.log(value, rule, reg);
            if (!reg.test(value)) {
              callback(new Error('不能包含特殊字符"/\?？[]"'));
            } else {
              callback();
            }
          },
          trigger: 'change',
        },
      ], 
    },

    {
      type: 'select',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
      label: '业务板块',
      // multiple: true,
      span: 24,
      parent: true,
      rules: [
        {
          required: true,
          message: '请选择业务板块',
        },
      ],
      display: true,
      filterable: true,
      prop: 'businessTypeId',
      checkStrictly: true,
    },
    {
      label: '对应客户',
      prop: 'customerId',
      span: 24,
      component: 'wf-customer-select',
      change: val => {
        const contactPerson = proxy.findObject(baseInfoOption.value.column, 'contactPerson');

        if (!val.value) {
          contactPerson.disabled = true;
          return;
        }

        contactPerson.disabled = false;
        contactPerson.params.Url = '/vt-admin/customerContact/page?customerId=' + val.value;
        setBaseInfo(val.value);
        
      },
      params: {
        Url: '/api/vt-admin/customer/page?type=5',
      },
      rules: [
        {
          required: true,
          message: '请选择客户',
        },
      ],
    },
    {
      label: '协作业务员',
      placeholder: '请选择协作业务员',
      type: 'select',
      span: 24,
      dicData: [],
      display: false, disabled:true,
      prop: 'businessPerson',
      rules: [
        {
          required: true,
          message: '请选择协作业务员',
          trigger: 'change',
        },
      ],
    },
    {
      label: '关联联系人',
      prop: 'contactPerson',
      span: 24,
      component: 'wf-contact-select',
      disabled: true,
      rules: [
        {
          required: true,
          message: '请选择客户',
        },
      ],
      params: {
        // checkType: 'box',
        Url: '/vt-admin/customerContact/page',
      },
    },

    // {
    //   label: '报价人员',
    //   component: 'wf-user-select',
    //   prop: 'offerPeople',
    // },
    {
      label: '报价日期',
      prop: 'offerDate',
      type: 'date',
      span: 24, disabled:true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    {
      label: '报价有效期（天）',
      prop: 'offerValidity',
      type: 'number',
      span: 24, disabled:true,
    },
    {
      label: '设备保修期(年)',
      prop: 'freeWarrantyYear',
      span: 24, disabled:true,
      type: 'number',
    },
    // {
    //   label: '线下报价',
    //   prop: 'isOnline',
    //   type: 'radio',
    //   span: 24,
    //   // display: !route.query.id,
    //   value: 0,
    //   dicData: [
    //     {
    //       label: '是',
    //       value: 1,
    //     },
    //     {
    //       label: '否',
    //       value: 0,
    //     },
    //   ],
    // },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea',
      span: 24, disabled:true,
    },
    // {
    //   label: '专项报价',
    //   prop: 'isHasSpecialPrice',
    //   type: 'radio',
    //   span: 24,
    //   value: 0,
    //   dicData: [
    //     {
    //       label: '是',
    //       value: 1,
    //     },
    //     {
    //       label: '否',
    //       value: 0,
    //     },
    //   ],
    // },
  ],
});
let addForm = ref();
function baseSubmit(form,done,loading) {
  console.log(form);
  done()
  axios.post('/api/vt-admin/offer/updateBaseInfo',form).then(res => {
    proxy.$message.success('操作成功')
    done()
    drawer.value = false
    onLoad()
  }).catch(err => {
    proxy.$message.error('操作失败')
  })
}
</script>

<style lang="scss" scoped>
.item {
  margin-top: 5px;
}
:deep(.el-card__header) {
  padding: 5px;
}
:deep(.el-col) {
  margin-bottom: 0px;
}
:deep(.hide_icon td:first-child .cell) {
  visibility: hidden;
}
</style>

<template>
  <div>
    <div style="display: flex; align-items: center">
      <el-select
        v-model="name"
        :size="size"
        style="width: 100%"
        suffix-icon="el-icon-user"
        :placeholder="placeholder || '客户选择'"
        :disabled="disabled"
        clearable
        @clear="selectionClear"
        filterable
        @change="handleUserSelectConfirm"
        remote
        :remote-method="remoteMethod"
      >
        <el-option
          v-for="item in data"
          :key="item.id"
          :label="item.customerName"
          :value="item.id"
        />
       
      </el-select>
      <el-button type="primary" v-if="!disabled" @click="handleSelect" icon="more"></el-button>
    </div>
    <!-- 客户选择弹窗 -->
    <wf-customer-select
      ref="customer-select"
      :check-type="checkType"
      :userUrl="Url"
      :isHasAdd="isHasAdd"
      :default-checked="modelValue"
      @onConfirm="handleUserSelectConfirm"
    ></wf-customer-select>
  </div>
</template>
<script>
import axios from 'axios';
import { getUser } from '../../../api/process/user';

import WfCustomerSelect from '@/components/Y-UI/wf-customer-select.vue';

export default {
  name: 'customer-select',
  components: { WfCustomerSelect },
  emits: ['update:modelValue','clear'],
  props: {
    modelValue: [String, Number],
    checkType: {
      // radio单选 checkbox多选
      type: String,
      default: () => {
        return 'radio';
      },
    },
    size: {
      type: String,
      // default: () => {
      //   return 'small'
      // }
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: String,
    Url: {
      type: String,
      default: () => {
        return '/vt-admin/customer/page?type=0';
      },
    },
    change: Function,
    isHasAdd: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    modelValue: {
      handler(val) {
        if (val) {
          const name = [];
          const checks = (val + '').split(',');
          const asyncList = [];
          checks.forEach(c => {
            asyncList.push(this.getDetail(c));
          });
          Promise.all(asyncList).then(res => {
            res.forEach(r => {
              const data = r.data.data;
              if (data) name.push(data.customerName);
            });
            this.name = name.join(',');
          });
        } else this.name = '';
      },
      immediate: true,
    },
  },
  data() {
    return {
      name: '',
      data: [],
    };
  },
  methods: {
    handleSelect() {
      if (this.readonly || this.disabled) return;
      else this.$refs['customer-select'].visible = true;
    },
    handleUserSelectConfirm(id,name) {
      if(!name) name = this.data.find(item => item.id == id).customerName
      this.$emit('update:modelValue', id);
      if (this.change && typeof this.change == 'function') this.change({ value: id,name:name || this.name });
    },
    getDetail(c) {
      return axios.get('/api/vt-admin/customer/detail?id=' + c, {
        id: c,
      });
    },
    remoteMethod(text) {
      if (!text) return (this.data = []);
      axios.get(this.Url,{
        params:{
          customerName: text,
        }
      }).then(res => {
        this.data = res.data.data.records;
      });
    },
    selectionClear(){
      this.$emit('clear')
    }
  },
};
</script>
<style lang="scss"></style>

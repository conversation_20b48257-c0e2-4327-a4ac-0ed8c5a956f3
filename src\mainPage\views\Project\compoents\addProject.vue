<template>
  <basic-container>
    <Title style="margin-bottom: 10px">
      新增项目
      <template #foot
        ><el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >
    <avue-form :option="option" @submit="handleSubmit" v-model="form"> </avue-form>
    <el-dialog title="添加计划收款" v-model="planVisible" class="avue-dialog avue-dialog--top">
      <avue-form
        v-if="planVisible"
        ref="planForm"
        :option="addPlanOption"
        @submit="addPlanSubmit"
        v-model="addPlanForm"
      >
        <template #planCollectionPrice>
          <div style="display: flex; align-items: center">
            <el-input-number
              style="width: calc(100% - 390px)"
              v-model="addPlanForm.planCollectionPrice"
              controls-position="right"
            ></el-input-number>
            <el-tag effect="plain" size="large" style="width: 150px; margin: 0 20px" type="primary"
              >合同额：{{ form.contractTotalPrice }}</el-tag
            >
            <el-input
              v-model="addPlanForm.rate"
              placeholder="填写比例以计算"
              style="width: 200px"
              @change="
                value => {
                  addPlanForm.planCollectionPrice =
                    form.contractTotalPrice * (addPlanForm.rate / 100);
                }
              "
            >
              <template #append>%</template>
            </el-input>
          </div>
        </template>
      </avue-form>
      <!-- <slot ></slot> -->
      <div class="avue-dialog__footer">
        <el-button @click="planVisible = false">取 消</el-button>
        <el-button @click="$refs.planForm.submit()" type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script setup>
let baseUrl = '/api/blade-system/region/lazy-tree';
import { followData } from '@/const/const';
import { dateFormat } from '@/utils/date';
import axios from 'axios';
import { useStore } from 'vuex';
import { computed } from 'vue';
import { ref, getCurrentInstance } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { planNameData } from '@/const/const';
const form = ref({});
const router = useRouter();
const route = useRoute();
const store = useStore();
const { proxy } = getCurrentInstance();

function validateName(rule, value, callback) {
  axios.get('/api/vt-admin/customer/existCustomerName?customerName=' + value).then(res => {
    if (res.data.data == 1) {
      callback(new Error('名字不能重复'));
    } else {
      callback();
    }
  });
}
// watchEffect(() => {
//   if (route.query.id) {
//     getDetail();
//   }
// });
let userInfo = computed(() => store.getters.userInfo);
onMounted(() => {
  if (route.query.offerId) {
    getquotationDetail(route.query.offerId);
  }
  if (route.query.id) {
    getDetail();
  }
});
let isPaymentPeriodData = ref([]);
const type = route.query.type;
console.log(type);
const option = ref({
  labelWidth: 120,
  emptyBtn: false,
  group: [
    {
      label: '基本信息',
      prop: 'baseInfo',
      arrow: true,
      collapse: true,
      display: true,
      column: [
        {
          type: 'input',
          label: '关联报价',
          display: true,
          component: 'wf-quotation-select',
          prop: 'offerId',
          value: route.query.offerId || null,
          params: {
            Url: '/api/vt-admin/offer/pageForAddProject?selectType=0',
          },
          rules: [
            {
              required: true,
              message: '请选择关联报价',
              trigger: 'change',
            },
          ],
          change: val => {
            if (val.value) {
              getquotationDetail(val.value);
            }
          },
        },
        {
          type: 'input',
          label: '关联商机',
          display: true,
          disabled: true,
          placeholder: '请先选择报价',
          component: 'wf-business-select',
          prop: 'businessOpportunityId',
          change: val => {
            if (val.value) {
              getBusinessDetail(val.value);
            }
          },
        },
        {
          label: '客户名称',
          span: 12,
          display: true,
          readonly: true,
          prop: 'customerName',
        },
        {
          type: 'input',
          label: '客户地址',
          span: 12,
          display: true,
          prop: 'customerAddress',
        },
        {
          label: '关联联系人',
          type: 'input',
          prop: 'customerContactId',
          component: 'wf-contact-select',
          placeholder: '请先选择报价',
          // disabled: true,
          params: {},
          change: ({ value }) => {
            setBaseInfo1(value);
          },
        },
        {
          type: 'input',
          label: '电话',
          span: 12,
          display: true,
          prop: 'customerPhone',
        },
        {
          label:'最终客户',
          prop:'finalCustomer',
          span:12,
        }
      ],
    },
    {
      label: '项目信息',
      prop: 'contractInfo',
      arrow: true,
      collapse: true,
      display: true,
      column: [
        {
          type: 'input',
          label: '项目名称',
          span: 12,
          display: true,
          prop: 'projectName',
          required: true,
          rules: [
            {
              required: true,
              message: '项目名称必须填写',
            },
          ],
        },

        {
          label: '项目属性',
          prop: 'projectAttribute',
          overHidden: true,
          dicData: [
            {
              label: '总包',
              value: 0,
            },
            {
              label: '分包',
              value: 1,
            },
          ],
          type: 'radio',
          hide: true,
        },
        {
          type: 'input',
          label: '项目总额',
          span: 12,
          display: true,
          disabled: true,
          prop: 'projectPrice',
          required: true,
          rules: [
            {
              required: true,
              message: '项目总额必须填写',
            },
          ],
          change: val => {
            console.log(val);
            form.value.contractTotalPrice = val.value;
          },
        },
        {
          label: '开工时间',
          prop: 'startDate',
          type: 'date',
          span: 12,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
        {
          label: '交付时间',
          prop: 'deliveryDate',
          type: 'date',
          span: 12,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          change: val => {
            console.log(val);
            form.value.contractDeliveryDate = val.value;
          },
        },
        {
          label: '项目地址',
          prop: 'projectAddress',
          overHidden: true,
          type: 'input',
          span: 24,
          hide: true,
        },

        {
          label: '确认附件',
          prop: 'fileIds',
          type: 'upload',
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '项目备注',
          prop: 'projectRemark',
          overHidden: true,
          span: 24,
          type: 'textarea',
        },
      ],
    },
    {
      prop: 'contractInfo',
      label: '合同信息',
      column: [
        {
          type: 'input',
          label: '合同编号',
          span: 12,
          display: true,
          readonly: true,
          placeholder: '自动生成',
          prop: 'contractCode',
        },
        {
          type: 'input',
          label: '合同名称',
          span: 12,
          display: true,
          prop: 'contractName',
          required: true,
          rules: [
            {
              required: true,
              message: '项目名称必须填写',
            },
          ],
        },
        {
          type: 'input',
          label: '对方订单编号',
          span: 12,
          display: true,
          prop: 'customerOrderNumber',
        },
        {
          type: 'input',
          label: '合同金额',
          span: 12,
          display: true,
          disabled: true,
          prop: 'contractTotalPrice',
          required: true,
          rules: [
            {
              required: true,
              message: '合同金额必须填写',
            },
          ],
        },
        {
          label: '付款期限',
          type: 'radio',
          span: 12,
          prop: 'paymentDeadline',
          dicFormatter: res => {
            isPaymentPeriodData.value = res.data;
            return res.data;
          },
          control: (val, form, b, c) => {
            return {
              fixedBillingDate: {
                display:
                  isPaymentPeriodData.value.find(item => item.id == val)?.dictValue == '固定账期',
              },
            };
          },
          dicUrl: '/blade-system/dict/dictionary?code=isPaymentPeriod',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          type: 'number',
          label: '固定账期时间',
          span: 12,
          display: true,
          min: 1,
          max: 31,
          tip: '输入1到31之间的数字,账期则为每月这个时间',
          prop: 'fixedBillingDate',
          rules: [
            {
              required: true,
              message: '请输入固定账期时间',
              trigger: 'blur',
            },
          ],
        },
        {
          label: '结算方式',
          type: 'radio',
          prop: 'settlementMethod',
          span: 24,
          dicUrl: '/blade-system/dict/dictionary?code=payment_methods',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          type: 'select',
          label: '发票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          span: 12,
          search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          type: 'date',
          label: '签订日期',
          span: 12,
          display: true,
          prop: 'signDate',
          required: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          value: dateFormat(new Date()),
          rules: [
            {
              required: true,
              message: '签订日期必须填写',
            },
          ],
        },
        {
          label: '合同附件',
          prop: 'contractFiles',
          type: 'upload',
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          type: 'date',
          label: '合同交付日期',
          span: 12,
          display: true,
          prop: 'contractDeliveryDate',
          required: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          // rules: [
          //   {
          //     required: true,
          //     message: '签订日期必须填写',
          //   },
          // ],
        },
      ],
    },
    {
      label: '计划收款',
      column: [
        {
          label: '计划收款',
          type: 'dynamic',
          span: 24,
          labelWidth: 0,
          prop: 'sealContractPlanCollectionDTOS',
          children: {
            align: 'center',
            headerAlign: 'center',
            rowAdd: done => {
              planVisible.value = true;
            },
            rowDel: (row, done) => {
              done();
            },
            column: [
              // {
              //   type: 'input',
              //   label: '客户名称',
              //   span: 12,
              //   hide: true,
              //   display: true,
              //   prop: 'a170080949446133484',
              // },
              // {
              //   type: 'input',
              //   label: '项目名称',
              //   span: 12,
              //   display: true,
              //   hide: true,
              //   prop: 'a170080951774565537',
              // },
              {
                label: '计划名称',
                prop: 'planName',

                type: 'radio',
                dicData: planNameData,
                span: 24,
                rules: [
                  {
                    required: true,
                    message: '计划名称必须填写',
                  },
                ],
              },
              {
                type: 'date',
                label: '计划收款时间',
                span: 12,
                display: true,
                format: 'YYYY-MM-DD',
                valueFormat: 'YYYY-MM-DD',
                prop: 'planCollectionDate',
                disabled: false,
                readonly: false,
                width: 200,
                required: true,
                rules: [
                  {
                    required: true,
                    message: '计划收款时间必须填写',
                  },
                ],
              },
              {
                type: 'number',
                label: '计划收款金额',
                controls: true,
                width: 200,
                span: 24,
                display: true,
                prop: 'planCollectionPrice',
              },

              {
                type: 'textarea',
                label: '备注',
                span: 24,
                display: true,
                prop: 'remark',
                showWordLimit: true,
              },
            ],
          },
        },
      ],
    },
  ],
});
function handleSubmit(form, done, loading) {
  decomposeSubmit().then(res => {
    const data = {
      ...form,
      projectFiles: form.fileIds && form.fileIds.map(item => item.value).join(','),
      signDate:form.signDate.split(' ')[0]
    };

    axios
      .post(`/api/vt-admin/project/${form.id ? 'replenishOffer' : 'save'}`, data)
      .then(res => {
        if (res.data.code == 200) {
          const contractData = {
            ...form,
            contractFiles: form.contractFiles.map(item => item.value).join(','),
            customerContact:form.customerContactId,
            id: null,
            signDate:form.signDate.split(' ')[0]
          };
          axios
            .post(`/api/vt-admin/sealContract/save`, contractData)
            .then(res => {
              if (res.data.code == 200) {
                proxy.$message.success(res.data.msg);
                router.$avueRouter.closeTag();
                router.go(-1);
              }
            })
            .catch(() => {
              done();
            });
        }
      })
      .catch(() => {
        done();
      });
  });
}
function getBusinessDetail(id) {
  axios
    .get('/api/vt-admin/businessOpportunity/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const {
        productVOList,
        // id: businessOpportunityId,
        customerId,
        id: businessOpportunityId,
        customerName,
        contactPersonName,
        contactPhone,
        address,
      } = res.data.data;
      form.value.businessOpportunityId = businessOpportunityId;
      // form.value.customerId = customerId;
      form.value.customerName = customerName;
      form.value.contactPerson = contactPersonName;
      form.value.contactPhone = contactPhone;
      form.value.address = address;
    });
}
function getquotationDetail(id) {
  axios
    .get('/api/vt-admin/offer/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const {
        productVOList,
        // id: businessOpportunityId,
        customerId,
        businessOpportunityId,
        customerName,
        customerContact,
        customerPhone,
        contactPerson,
        customerAddress,
        isHasOption,
        offerPrice,
        offerName,
        businessOpportunityName,
        fixedBillingDate,
        isPaymentPeriod,
      } = res.data.data;
      currentRow.value = res.data.data;
      form.value.businessOpportunityId = businessOpportunityId;
      // form.value.customerId = customerId;
      form.value.customerName = customerName;
      form.value.customerId = customerId;
      form.value.customerContactId = contactPerson;
      form.value.contact = contactPerson;
      form.value.customerPhone = customerPhone;
      form.value.contactPhone = customerPhone;
      form.value.customerAddress = customerAddress;
      form.value.projectAddress = customerAddress;
      form.value.projectPrice = offerPrice;
      form.value.projectName = businessOpportunityName || offerName;
      form.value.fixedBillingDate = fixedBillingDate;

      form.value.contractTotalPrice = offerPrice;
      form.value.contractName = businessOpportunityName || offerName;

      setUrl(form.value.customerId);
    });
}

function getDetail() {
  axios
    .get('/api/vt-admin/project/detail', {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
      // form.value = formatData(res.data.data)
      form.value = {
        ...res.data.data,

        fileIds: res.data.data.attachList.map(item => {
          return {
            value: item.id,
            label: item.originalName,
          };
        }),
        contractName: res.data.data.projectName,
        invoiceType: '',
      };
      console.log(form.value);

      setUrl(form.value.customerId);
      setBaseInfo1(form.value.customerContactId);
    });
}

function setUrl(id) {
  const customerContact = proxy.findObject(option.value.group[0].column, 'customerContactId');
  customerContact.params.Url = '/vt-admin/customerContact/page?customerId=' + id;
}
function setBaseInfo2(id) {
  axios
    .get('/api/vt-admin/customerContact/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      form.value.contactPhone = res.data.data.phone;
    });
}
function setBaseInfo1(id) {
  axios
    .get('/api/vt-admin/customerContact/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      form.value.customerPhone = res.data.data.phone;
    });
}
// 添加计划收款
let planVisible = ref(false);
let addPlanForm = ref({});
let addPlanOption = ref({
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  column: [
    {
      label: '计划名称',
      prop: 'planName',

      type: 'radio',
      dicData: planNameData,
      span: 24,
      change: val => {
        addPlanForm.value.rate = '';
        if (val.value == '全款') {
          console.log(val, form.value.contractTotalPrice);
          addPlanForm.value.planCollectionPrice = form.value.contractTotalPrice;
        } else {
          addPlanForm.value.planCollectionPrice = '';
        }
      },
      rules: [
        {
          required: true,
          message: '计划名称必须填写',
        },
      ],
    },

    {
      type: 'date',
      label: '计划收款时间',
      span: 24,
      display: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'planCollectionDate',
      disabled: false,
      readonly: false,
      required: true,
      rules: [
        {
          required: true,
          message: '计划收款时间必须填写',
        },
      ],
    },
    {
      type: 'number',
      label: '计划收款金额',
      controls: true,
      span: 24,
      display: true,
      prop: 'planCollectionPrice',
    },
    // {
    //   type: 'number',
    //   label: '收款比例',
    //   controls: true,
    //   span: 24,
    //   prop: 'collectionRate',

    // },

    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
      showWordLimit: true,
    },
  ],
});
function addPlanSubmit(row, done) {
  form.value.sealContractPlanCollectionDTOS.push(row);
  done();
  proxy.$refs.planForm.resetFields();
  planVisible.value = false;
}
let currentRow = ref({});
function decomposeSubmit() {
  const data = {
    id: currentRow.value.id,
    offerStatus: 2,
  };
  console.log(data);
  return axios.post('/api/vt-admin/offer/customerConfirm', data);
}
</script>

<style lang="scss" scoped></style>

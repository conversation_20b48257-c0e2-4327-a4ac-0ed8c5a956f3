<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @on-load="onLoad"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :before-open="beforeOpen"
    :table-loading="loading"
    ref="crud"
    @keyup.enter="onLoad"
    @row-del="rowDel"
    @search-reset="onLoad"
    @search-change="searchChange"
    @current-change="onLoad"
    @refresh-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
    <template #menu-left> </template>
    <template #menu="{ row, $index }">
      <el-button type="primary" icon="view" text @click="viewDetail(row, $index)">详情</el-button>
      <!-- <el-button type="primary" icon="edit" text @click="$refs.crud.rowEdit(row, $index)"
        >编辑</el-button
      > -->
      <el-button
        type="primary"
        icon="delete"
        v-if="row.invoiceStatus == 0"
        text
        @click="$refs.crud.rowDel(row, $index)"
        >删除</el-button
      >
    </template>
    <template #customerInvoiceInfoId-form>
      <wfInvoiceDrop v-model="form.customerInvoiceInfoId" :id="props.customerId"></wfInvoiceDrop>
      <productSelect
        ref="productSelectRef"
        @select="handleConfirm"
        @selectAll="handleSelectAll"
        :sealContractId="props.sealContractId"
        :offerId="props.offerId"
      ></productSelect>
    </template>
    <template #planCollectionId-form>
      <wfPlanDrop v-model="form.planCollectionId" :id="props.sealContractId"></wfPlanDrop>
    </template>
    <template #invoiceStatus="{ row }">
      <el-tooltip
        :content="row.invoiceStatus == 3 ? row.invalidDescript : row.applyVoidReason"
        :disabled="row.invoiceStatus != 3 && row.invoiceStatus != 4"
      >
        <el-tag
          effect="plain"
          :type="
            row.invoiceStatus == 3
              ? 'danger'
              : row.invoiceStatus == 0
              ? ''
              : row.invoiceStatus == 4
              ? 'info'
              : 'success'
          "
          >{{ row.$invoiceStatus }}</el-tag
        >
      </el-tooltip>
    </template>
    <template #planName-form>
      <div style="display: flex; gap: 20px; flex-direction: column">
        <el-radio-group @change="handlePlanChange" v-model="form.planName">
          <el-radio :label="item.label" v-for="item in planNameData" :key="item.value"></el-radio>
        </el-radio-group>

        <el-input
          v-if="form.planName == '其它'"
          v-model="form.planNameOther"
          style="width: 50%"
          placeholder="请输入计划名称"
        ></el-input>
      </div>
    </template>
     <template #invoicePrice-form>
      <div style="display: flex; align-items: center">
        <el-input-number
          style="width: calc(100% - 390px)"
          v-model="form.invoicePrice"
          :readonly="form.invoicePriceType == 0"
          :placeholder="form.invoicePriceType == 0 ? '请选择开票产品' : '请输入发票金额'"
          controls-position="right"
        ></el-input-number>
        <div v-if="form.invoicePriceType == 1">
           <el-popover class="box-item" popper-style="width:auto" placement="right">
          <template #default>
            <div style="display: flex; align-items: center" v-for="item in visaList">
              <el-checkbox style="margin-right: 5px;" @change="setTotalPrice" v-model="item.isCheck"></el-checkbox>
              {{ item.title }}: <el-text type="primary">{{ item.totalPrice }}</el-text>
              <!-- <el-button @click="form.invoicePrice = item.totalPrice" text type="primary"
                >填入</el-button
              > -->
            </div>
          </template>
          <template #reference>
            <el-tag
              effect="plain"
              size="large"
              style="width: 150px; margin: 0 20px; cursor: pointer"
              type="primary"
              >合同额：{{ props.contractTotalPrice }}</el-tag
            >
          </template>
        </el-popover>

        <el-input
          v-model="form.rate"
          placeholder="填写比例以计算"
          style="width: 200px"
          @change="
            value => {
              form.invoicePrice = (
                props.contractTotalPrice * (form.rate / 100) || 0
              ).toFixed(2);
            }
          "
        >
          <template #append>%</template>
        </el-input>
        </div>
      </div>
    </template>
  </avue-crud>
  <dialogForm ref="dialogForm"></dialogForm>
  <el-drawer
    title="开票详情"
    size="50%"
    v-model="dialogVisible"
    class="avue-dialog avue-dialog--top"
  >
    <avue-form ref="dialogForm" :option="detailOption" v-model="detailForm">
      <template #contractBaseInfo>
        <div v-for="item in detailForm.sealContractVOList">
          <el-divider>{{ item.contractName }}</el-divider>
          <ContractInfo
            :detailForm="item"
            :invoicePriceType="detailForm.invoicePriceType"
            :key="item.id"
          ></ContractInfo>
        </div>
      </template>
    </avue-form>
    <!-- <slot ></slot> -->
    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </div>
  </el-drawer>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import wfInvoiceDrop from '../compoents/wf-invoice-drop.vue';
import wfPlanDrop from '../compoents/wf-plan-drop.vue';
import productSelect from '../compoents/productSelect.vue';
import { dateFormat } from '@/utils/date';
import { planNameData } from '@/const/const';
import ContractInfo from '@/views/Finance/invoice/contractInfo.vue';
let { proxy } = getCurrentInstance();
const props = defineProps({
  contractCode: String,
  supplierName: String,
  sealContractInvoiceId: String,
  customerId: String,
  sealContractId: String,
  offerId: String,
  form: Object,
  isNeedInvoice: String,
    contractType: Number,
  contractTotalPrice: [String, Number],
});
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: props.isNeedInvoice == 1,

  editBtn: false,
  delBtn: false,
  viewBtn: false,
  size: 'default',
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  addTitle: '开票申请',
  addBtnText: '开票申请',
  menuWidth: 270,
  border: true,
  column: [
    {
      type: 'input',
      label: '开票信息',
      span: 24,
      display: true,
      hide: true,
      value: props.sealContractInvoiceId,
      prop: 'customerInvoiceInfoId',
    },
    {
      type: 'input',
      label: '关联计划',
      span: 24,
      display: true,
      overHidden: true,
      prop: 'planCollectionId',
      formatter: (row, column, cellValue) => {
        return row.planName;
      },
      control: val => {
        return {
          planName: {
            display: !val,
          },
        };
      },
    },
    {
      label: '计划名称',
      prop: 'planName',
      span: 24,
      type: 'radio',
      dicData: planNameData,

      rules: [
        {
          required: true,
          message: '计划名称必须填写',
        },
      ],
    },
    {
      type: 'date',
      label: '开票日期',
      span: 12,
      width: 100,
      display: false,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'invoiceDate',
    },

    {
      type: 'input',
      label: '开票金额',
      width: 110,
      span: 12,
      span:24,
      display: true,
      prop: 'invoicePrice',
    },
    {
      type: 'select',
      label: '开票类型',
      dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
      cascader: [],
      span: 12,
      width: 130,
      // search: true,
      value: '',
      display: true,
      value:props.invoiceType,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'invoiceType',
      rules: [{ required: true, message: '请选择开票类型', trigger: 'blur' }],
    },
    {
      label: '税率',
      type: 'select',
      width: 80,
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },
     
      cell: false,
      prop: 'taxRate',
      dicUrl: '/blade-system/dict/dictionary?code=tax',
      rules: [
        {
          required: true,
          message: '请选择税率',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '开票公司',
      type: 'select',

      prop: 'billingCompany',

      props: {
        label: 'companyName',
        value: 'id',
      },
      dicFormatter: res => {
        return res.data.records;
      },

      overHidden: true,
      cell: false,
      dicUrl: '/api/vt-admin/company/page?size=100',
    },
    {
      label: '申请人',
      type: 'input',
      value: proxy.$store.getters.userInfo.nick_name,
      prop: 'createName',
      width: 80,
      readonly: true,
    },
    {
      type: 'date',
      label: '申请时间',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      readonly: true,
      width: 100,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
      valueFormat: 'YYYY-MM-DD',
      prop: 'createTime',
    },
    {
      label: '开票方式',
      type: 'radio',
      prop: 'invoicePriceType',
      span: 24,
      dicData: [
        {
          label: '按产品开票',
          value: 0,
          disabled:props.contractType == 2

        },
        {
          label: '按合同额开票',
          value: 1,
        },
      ],
      value: 0,
      control: val => {
        return {
          detailDTOList: {
            display: val == 0,
          },
        };
      },
    },
    {
      label: '关联产品',
      prop: 'detailDTOList',
      type: 'dynamic',
      hide: true,
      rules: [
        {
          required: true,
          message: '请选择关联产品',
        },
      ],
      span: 24,
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          proxy.$refs.productSelectRef.open();
          // done();
        },
        rowDel: (row, done) => {
          done();
        },

        column: [
          {
            label: '产品名称',
            prop: 'customProductName',

            cell: false,
          },
          {
            label: '规格型号',
            prop: 'customProductSpecification',

            overHidden: true,
            // search: true,
            span: 24,
            cell: false,
            type: 'input',
          },
          {
            label: '数量',
            prop: 'number',
            type: 'number',
            span: 12,
            cell: true,
            change: val => {
              form.value.invoicePrice = totalAmount();
            },
          },
          {
            label: '单价',
            prop: 'zhhsdj',
            type: 'number',
            span: 12,
            cell: false,
            change: val => {
              form.value.invoicePrice = totalAmount();
            },
          },
          {
            label: '金额',
            prop: 'totalPrice',
            type: 'number',
            span: 12,
            cell: false,
            formatter: row => {
              return (row.number * row.zhhsdj).toFixed(2);
            },
          },
        ],
      },
    },
    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
    },
    {
      type: 'select',
      label: '状态',
      width: 80,
      dicData: [
        {
          label: '待开票',
          value: 0,
        },
        {
          label: '已开票 ',
          value: 1,
        },
        {
          label: '已邮寄',
          value: 2,
        },
        {
          label: '已作废',
          value: 3,
        },
        {
          label: '申请作废',
          value: 4,
        },
      ],
      cascader: [],
      span: 12,
      addDisplay: false,
      editDisplay: false,
      props: {
        label: 'label',
        value: 'value',
        desc: 'desc',
      },
      prop: 'invoiceStatus',
    },
    {
      type: 'date',
      label: '计划收款时间',
      span: 12,
      width: 110,
      addDisplay: false,
      editDisplay: false,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'planCollectionDate',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/sealContractInvoice/save';
const delUrl = '/api/vt-admin/sealContractInvoice/remove?ids=';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContractInvoice/page';
let params = ref({});
let tableData = ref([]);

let route = useRoute();
onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        sealContractId: props.sealContractId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    sealContractId: props.sealContractId,
    customerId: props.customerId,
    createUser: null,
    createTime: null,
    planName: form.planName == '其它' ? form.planNameOther : form.planName,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        proxy.$store.dispatch('getMessageList');

        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function handleConfirm(list) {
  console.log(list);
  form.value.detailDTOList = list.map(item => {
    return {
      detailId: item.id,
      ...item,
      id: null,
      number: item.number * 1 - (item.invoiceNumber * 1 || 0),
    };
  });
  // totalAmount(form.value.taxRate);
  form.value.invoicePrice = totalAmount();
}
function totalAmount(val) {
  //  console.log(val);
  // form.value.invoicePrice =  form.value.detailDTOList.reduce((total, item) => (total += item.totalPrice * 1), 0) * (1 - (val * 1) / 100)
  return form.value.detailDTOList
    .reduce((total, item) => (total += item.number * 1 * (item.zhhsdj * 1)), 0)
    .toFixed(2);
}

let dialogVisible = ref(false);
let detailForm = ref({});
let detailOption = ref({
  detail: true,
  tabs: true,
  submitBtn: false,
  emptyBtn: false,
  group: [
    {
      label: '申请信息',
      prop: 'applyInfo',
      labelWidth: 140,
      column: [
        {
          type: 'input',
          label: '申请开票金额',
          span: 12,
          display: true,
          prop: 'invoicePrice',
        },
        {
          type: 'select',
          label: '申请开票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          span: 12,
          // search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          label: '开票方式',
          type: 'radio',
          prop: 'invoicePriceType',
          span: 12,
          dicData: [
            {
              label: '按产品开票',
              value: 0,
            },
            {
              label: '按合同额开票',
              value: 1,
            },
          ],
        },
        {
          label: '税率',
          type: 'select',
          props: {
            label: 'dictValue',
            value: 'dictKey',
          },
          cell: false,
          prop: 'taxRate',
          dicUrl: '/blade-system/dict/dictionary?code=tax',
        },
        {
          label: '申请开票公司',
          type: 'select',
         
          span: 24,
          cell: false,
          prop: 'billingCompanyName',
        
        },
        {
          label: '申请人',
          type: 'input',
          component: 'wf-user-select',
          prop: 'createUser',
          readonly: true,
        },
        {
          type: 'date',
          label: '申请时间',
          span: 12,
          display: true,
          format: 'YYYY-MM-DD',
          readonly: true,
          value: dateFormat(new Date(), 'yyyy-MM-dd'),
          valueFormat: 'YYYY-MM-DD',
          prop: 'createTime',
        },
        {
          type: 'textarea',
          label: '备注',
          span: 24,
          display: true,
          prop: 'remark',
        },
      ],
    },
    {
      label: '抬头信息',
      prop: 'titleInfo',
      labelWidth: 160,
      column: [
        {
          label: '客户公司名称',
          prop: 'invoiceCompanyName',
          bind: 'sealContractMakeInvoiceInfoVO.invoiceCompanyName',
          rules: [{ required: true, message: '请输入开票公司名称', trigger: 'blur' }],
        },
        {
          label: '纳税人识别号',
          prop: 'ratepayerIdentifyNumber',
          bind: 'sealContractMakeInvoiceInfoVO.ratepayerIdentifyNumber',
          rules: [{ required: true, message: '请输入纳税人识别号', trigger: 'blur' }],
        },
        {
          label: '开户银行',
          prop: 'bankName',
          bind: 'sealContractMakeInvoiceInfoVO.bankName',
          rules: [{ required: true, message: '请输入开户银行', trigger: 'blur' }],
        },
        {
          label: '银行账号',
          prop: 'bankAccount',
          bind: 'sealContractMakeInvoiceInfoVO.bankAccount',
          rules: [{ required: true, message: '请输入银行账号', trigger: 'blur' }],
        },
        {
          label: '注册地址',
          prop: 'bankAddress',
          bind: 'sealContractMakeInvoiceInfoVO.bankAddress',
        },
        {
          label: '电话',
          prop: 'phone',
          bind: 'sealContractMakeInvoiceInfoVO.phone',
        },
        {
          label: '期初末开票余额(元)',
          prop: 'endTermNoInvoiceAmount',
          type: 'number',
          bind: 'sealContractMakeInvoiceInfoVO.endTermNoInvoiceAmount',
        },
      ],
    },
    {
      label: '发票信息',
      prop: 'invoiceInfo',
      column: [
        {
          type: 'date',
          label: '发票日期',
          span: 12,

          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          prop: 'invoiceDate',
        },

        {
          type: 'input',
          label: '发票金额',
          span: 12,
          display: true,
          prop: 'invoicePrice',
        },
        {
          type: 'select',
          label: '发票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          span: 12,
          // search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          type: 'input',
          label: '发票代码',
          span: 12,
          display: true,
          prop: 'invoiceCode',
        },
        {
          type: 'textarea',
          label: '发票号码',
          span: 24,
          display: true,
          prop: 'invoiceNumber',
        },
      ],
    },
    // {
    //   label: '关联产品',
    //   prop: 'productInfo',

    //   column: [
    //     {
    //       label: '',
    //       prop: 'detailVOList',
    //       type: 'dynamic',
    //       labelWidth: 0,
    //       rules: [
    //         {
    //           required: true,
    //           message: '请选择关联产品',
    //         },
    //       ],
    //       span: 24,
    //       children: {
    //         align: 'center',
    //         headerAlign: 'center',
    //         rowAdd: done => {
    //           proxy.$refs.productSelectRef.open();
    //           // done();
    //         },
    //         rowDel: (row, done) => {
    //           done();
    //         },

    //         column: [
    //           {
    //             label: '关联合同',
    //             prop: 'contractName',
    //             type: 'input',
    //             span: 12,
    //             cell: false,
    //             overHidden: true,
    //           },
    //           {
    //             label: '产品',
    //             prop: 'productId',

    //             formatter: row => {
    //               return row.customProductName || row.productVO?.productName;
    //             },
    //             cell: false,
    //           },
    //           {
    //             label: '规格型号',
    //             prop: 'productSpecification',

    //             formatter: row => {
    //               return row.customProductSpecification || row.productVO?.productSpecification;
    //             },
    //             overHidden: true,
    //             // search: true,
    //             span: 24,
    //             cell: false,
    //             type: 'input',
    //           },
    //           {
    //             label: '数量',
    //             prop: 'number',
    //             type: 'number',
    //             span: 12,
    //             cell: false,
    //           },
    //           {
    //             label: '单价',
    //             prop: 'zhhsdj',
    //             type: 'number',
    //             span: 12,
    //             cell: false,
    //           },
    //           {
    //             label: '金额',
    //             prop: 'totalPrice',
    //             type: 'number',
    //             span: 12,
    //             cell: false,
    //           },
    //         ],
    //       },
    //     },
    //   ],
    // },
    {
      label: '合同信息',
      prop: 'contract',
      column: [
        {
          type: 'select',
          label: '',
          span: 24,
          labelWidth: 0,
          prop: 'contractBaseInfo',
        },
      ],
    },
    {
      label: '快递信息',
      prop: 'courieInfo',
      column: [
        {
          type: 'select',
          label: '快递公司',
          dicUrl: '/blade-system/dict/dictionary?code=courierCompany',
          cascader: [],
          span: 24,
          // search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          rules: [
            {
              required: true,
              message: '请选择快递公司',
              trigger: 'blur',
            },
          ],
          prop: 'courierCompany',
        },
        {
          label: '快递单号',
          prop: 'courierNumber',
          span: 24,
          rules: [
            {
              required: true,
              message: '请输入快递单号',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
  ],
});
function viewDetail(row) {
  axios.get('/api/vt-admin/sealContractInvoice/detail?id=' + row.id).then(res => {
    dialogVisible.value = true;
    detailForm.value = res.data.data;
  });
}
function beforeOpen(done, type) {
  getVisaList()
  if (type == 'add') {
    form.value.invoiceType = props.form.invoiceType || '';
  }

  done();
}
async function handleSelectAll() {
  let data1 = await axios.get('/api/vt-admin/sealContract/productPage', {
    params: {
      current: 1,
      size: 5000,
      offerId: props.offerId,
    },
  });
  let data2 = await axios.post(
    '/api/vt-admin/projectChange/completeListBySealContractId?id=' + route.query.id
  );

  data2 = data2.data.data
    .reduce((pre, cur) => {
      return pre.concat(
        cur.detailVOList.map(item => {
          return {
            ...item,
            number: item.number * 1 - (item.invoiceNumber * 1 || 0),
          };
        })
      );
    }, [])
    .filter(item => item.invoiceNumber * 1 < item.number);
  data1 = data1.data.data.records
    .filter(item => item.invoiceNumber * 1 < item.number)
    .map(item => {
      return {
        ...item,
        number: item.number * 1 - (item.invoiceNumber * 1 || 0),
      };
    });

  handleConfirm([...data1, ...data2]);
}
function handlePlanChange(val) {
  if (val == '全款') {
    form.value.invoicePrice = props.contractTotalPrice;
  } else {
  }
}

// 定义存储签证列表的数据
let visaList = ref([]);

// 获取签证列表的函数
async function getVisaList() {
  axios
    .post('/api/vt-admin/projectChange/completeListBySealContractId?id=' + route.query.id)
    .then(res => {
      visaList.value = res.data.data;
    });
}
function setTotalPrice() {
  const totalPrice = visaList.value.filter(item => item.isCheck).reduce((total, item) => {
    return total + item.totalPrice * 1;
  }, 0);
  form.value.invoicePrice = totalPrice &&  totalPrice.toFixed(2);
}
</script>

<style lang="scss" scoped></style>

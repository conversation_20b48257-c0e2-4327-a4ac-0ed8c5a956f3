# 表格行高固定修改说明

## 修改目标
将表格的行高固定为最小值（30px），确保表格显示更加紧凑，提高数据密度。

## 修改内容

### 1. VirtualTable.vue 修改

#### 1.1 调整默认高度参数
```javascript
// 修改前
itemHeight: {
  type: Number,
  default: 50
},
categoryHeight: {
  type: Number,
  default: 40
},

// 修改后
itemHeight: {
  type: Number,
  default: 30
},
categoryHeight: {
  type: Number,
  default: 30
},
```

#### 1.2 修改表格行样式
```css
/* 修改前 */
tr {
  height: 25px;
}

td {
  padding: 8px;
  border: 1px solid #ddd;
}

/* 修改后 */
tr {
  height: 30px;
  min-height: 30px;
  max-height: 30px;
}

td {
  padding: 4px 8px;
  border: 1px solid #ddd;
  height: 30px;
  min-height: 30px;
  max-height: 30px;
  line-height: 22px;
}
```

#### 1.3 修改编辑状态样式
```css
/* 修改前 */
td.active {
  white-space: normal;
  overflow: visible;
  text-overflow: clip;
  height: auto;
  line-height: 25px;
}

/* 修改后 */
td.active {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 30px;
  min-height: 30px;
  max-height: 30px;
  line-height: 22px;
}
```

#### 1.4 修改分类行样式
```css
/* 修改前 */
.category {
  background-color: var(--el-color-primary-light-8);
}

.category-content {
  text-align: left !important;
  padding: 8px 12px;
}

/* 修改后 */
.category {
  background-color: var(--el-color-primary-light-8);
  height: 30px;
  min-height: 30px;
  max-height: 30px;
}

.category-content {
  text-align: left !important;
  padding: 4px 12px;
  height: 30px;
  min-height: 30px;
  max-height: 30px;
  line-height: 22px;
}
```

### 2. ProductRow.vue 修改

#### 2.1 修改基础单元格样式
```css
/* 修改前 */
td {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: keep-all;
  max-width: 0;
  font-size: 14px;
}

/* 修改后 */
td {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: keep-all;
  max-width: 0;
  font-size: 14px;
  height: 30px;
  min-height: 30px;
  max-height: 30px;
  line-height: 22px;
  padding: 4px 8px;
}
```

#### 2.2 修改编辑状态样式
```css
/* 修改前 */
td.active {
  white-space: normal;
  overflow: visible;
  text-overflow: clip;
  height: auto;
  line-height: 25px;
}

/* 修改后 */
td.active {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 30px;
  min-height: 30px;
  max-height: 30px;
  line-height: 22px;
}
```

#### 2.3 修改可编辑元素样式
```css
/* 修改前 */
div[contenteditable="true"] {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  min-height: 20px;
  font-size: 14px;
}

/* 修改后 */
div[contenteditable="true"] {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  height: 22px;
  min-height: 22px;
  max-height: 22px;
  line-height: 22px;
  font-size: 14px;
}
```

#### 2.4 修改textarea样式
```css
/* 修改前 */
style="
  white-space: nowrap;
  overflow: hidden;
  outline: none;
  border: none;
  resize: none;
  height: 100%;
"

/* 修改后 */
style="
  white-space: nowrap;
  overflow: hidden;
  outline: none;
  border: none;
  resize: none;
  height: 22px;
  min-height: 22px;
  max-height: 22px;
  line-height: 22px;
  font-size: 14px;
"
```

## 修改效果

### 优化前
- 产品行高度：50px
- 分类行高度：40px
- 编辑状态下行高度：自动（可能很高）
- 内边距：8px

### 优化后
- 产品行高度：30px（固定）
- 分类行高度：30px（固定）
- 编辑状态下行高度：30px（固定）
- 内边距：4px 8px
- 行间距：22px

## 主要改进

1. **紧凑显示**：行高从50px减少到30px，提高了数据密度
2. **固定高度**：即使在编辑状态下也保持固定行高，避免表格跳动
3. **一致性**：所有行（产品行、分类行）都使用相同的高度标准
4. **优化内边距**：减少垂直内边距，保持水平内边距便于阅读
5. **防止溢出**：确保内容不会超出固定的行高范围

## 注意事项

1. 由于行高固定为30px，长文本内容会被截断并显示省略号
2. 如果需要查看完整内容，可以通过悬停提示或编辑面板查看
3. 所有输入框和可编辑元素都限制在固定高度内
4. 保持了表格的整体美观性和一致性

<template>
  <basic-container :shadow="supplierId ? 'never' : 'always'">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :before-open="beforeOpen"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left="{}">
        <el-button type="primary" @click="toReceiveTicket" icon="plus" >收票</el-button>
        <el-button
          type="warning"
          @click="exportData"
          icon="download"
          v-if="$store.getters.permission.ticket_export"
          plain
          >导出</el-button
        >

        <el-text size="large" style="font-weight: bolder; margin-left: 10px">发票总额：</el-text>
        <el-text type="primary" size="large">￥{{ (totalPrice * 1).toLocaleString() }}</el-text>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">不含税总额：</el-text>
        <el-text type="primary" size="large">￥{{ (noTaxPrice * 1).toLocaleString() }}</el-text>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">税额总额：</el-text>
        <el-text type="primary" size="large">￥{{ (taxPrice * 1).toLocaleString() }}</el-text>
      </template>
      <template #invoiceFiles="{ row }"> <File :fileList="row.attachList || []"></File></template>
      <template #planName-form>
        <wfPlanDrop v-model="form.contractPlanId" :id="form.purchaseContractId"></wfPlanDrop>
      </template>
      <template #menu="{ row, $index }">
        <el-button
          type="primary"
          text
          icon="edit"
          v-if="permission.tiket_edit && !row.authenticationDate"
          @click="$refs.crud.rowEdit(row, $index)"
          >编辑</el-button
        >
        <el-button
          type="primary"
          v-if="permission.tiket_edit && !row.authenticationDate"
          text
          icon="delete"
          @click="$refs.crud.rowDel(row)"
          >删除</el-button
        >
        <el-button
          type="primary"
          text
          icon="tools"
          @click="authentication(row)"
          v-if="!row.authenticationDate && permission.tiket_auth"
          >认证</el-button
        >
        <!-- <el-button
          type="primary"
          text
          icon="CopyDocument"
          v-if="permission.tiket_copy"
          @click="copy(row)"
          >复制</el-button
        > -->
      </template>
      <template #invoicePrice="{ row }">
        <el-link type="primary" @click="viewDetail(row)">{{ row.invoicePrice }}</el-link>
      </template>
      <template #paymentStatus="{ row }">
        <el-tag
          effect="plain"
          :type="
            ['danger', 'warning', 'success'][row.paymentStatus == null ? 0 : row.paymentStatus]
          "
        >
          {{ row.$paymentStatus }}
        </el-tag>
      </template>
      <template #invoiceNumber="{ row }">
        <el-link type="primary" @click="viewProduct(row)">{{ row.invoiceNumber }}</el-link>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-dialog title="金额详情" v-model="detailDrawer" width="80%">
      <avue-crud :option="detailOption" :data="detailForm.detailEntityList"></avue-crud>
    </el-dialog>
    <el-drawer title="关联产品" size="60%" v-model="productDrawer">
      <avue-crud :option="productOption" :data="detailForm.productVOList"></avue-crud>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import wfPlanDrop from '@/views/SRM/procure/compoents/wf-plan-drop.vue';
import { dateFormat } from '@/utils/date';
import { downloadXls } from '@/utils/download';
let store = useStore();
let permission = computed(() => store.getters.permission);
console.log(permission.value);
const props = defineProps(['supplierId', 'contractId']);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchIcon: true,
  searchIndex: 4,
  searchLabelWidth: 100,
  dialogWidth: '80%',
  searchSpan: 4,
  menuWidth: 220,
  border: true,
  column: [
    {
      type: 'input',
      label: '合同编号',
      span: 24,
      overHidden: true,
      hide: !!props.contractId,
      component: 'wf-purchaseContract-select',
      prop: 'purchaseContractId',
      formatter(row, column, cellValue, index) {
        return row.contractCode;
      },
      change: val => {
        const { value } = val;
        setSupplierName(value);
      },
      width: 120,
      rules: [
        {
          required: true,
          message: '请选择合同',
        },
      ],
    },
    {
      type: 'input',
      label: '供应商名称',
      span: 12,
      width: 120,
      display: false,
      search: !props.supplierId,
      hide: !!props.supplierId,
      placeholder: '',
      prop: 'supplierName',
      component: 'wf-supplier-drop',
      disabled: true,
      overHidden: true,
    },
    // {
    //   type: 'input',
    //   label: '公司名称',
    //   span: 24,
    //   display: true,
    //   width: 120,
    //   overHidden: true,
    //   search: true,
    //   prop: 'supplierName',

    //   // hide: true,
    // },
    // {
    //   type: 'input',
    //   label: '付款计划',
    //   span: 12,
    //   display: true,
    //   prop: 'planName',
    //   width: 120,
    // },
    // {
    //   type: 'select',
    //   label: '付款状态',
    //   addDisplay: false,
    //   editDisplay: false,
    //   span: 12,
    //   width: 90,
    //   prop: 'paymentStatus',
    //   dicData: [
    //     {
    //       value: null,
    //       label: '未关联',
    //     },
    //     {
    //       value: 0,
    //       label: '未付款',
    //     },
    //     {
    //       value: 1,
    //       label: '部分付款',
    //     },
    //     {
    //       value: 2,
    //       label: '已付款',
    //     },
    //   ],
    // },
    {
      type: 'date',
      label: '登记日期',
      span: 12,
      width: 120,
      display: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      value: dateFormat(new Date(), 'yyyy-MM-dd'),

      prop: 'createTimeBf',
      formatter(row, column, cellValue, index) {
        return dateFormat(new Date(row.createTime), 'yyyy-MM-dd');
      },
    },
    {
      type: 'date',
      label: '发票日期',
      span: 12,
      width: 120,
      display: true,
      format: 'YYYY-MM-DD',

      searchSpan: 6,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
      component: 'wf-daterange-search',
      search: true,
      display: false,
      valueFormat: 'YYYY-MM-DD',
      prop: 'invoiceDateRange',
      hide: true,
    },
    {
      type: 'date',
      label: '发票日期',
      span: 12,
      display: true,
      width: 120,
      format: 'YYYY-MM-DD',

      searchSpan: 6,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),

      valueFormat: 'YYYY-MM-DD',
      prop: 'invoiceDate',
    },
    {
      type: 'input',
      label: '发票号码',
      span: 12,
      display: true,
      width: 180,
      search: true,
      overHidden: true,
      prop: 'invoiceNumber',
    },
    // {
    //   type: 'textarea',
    //   label: '发票内容',
    //   span: 24,
    //   overHidden: true,
    //   display: true,
    //   prop: 'invoiceContent',
    // },
    {
      type: 'input',
      label: '不含税金额',
      span: 24,
      overHidden: true,
      width: 100,
      display: false,
      prop: 'noTaxPrice',
    },
    {
      type: 'input',
      label: '税率',
      span: 24,
      overHidden: true,
      display: false,
      width: 100,
      prop: 'taxRate',
    },
    {
      type: 'input',
      label: '税额',
      span: 24,
      overHidden: true,
      display: false,
      width: 100,
      prop: 'taxPrice',
    },
    {
      type: 'input',
      label: '发票总额',
      span: 12,
      overHidden: true,
      display: true,
      width: 100,
      prop: 'invoicePrice',
    },
    {
      type: 'select',
      label: '发票类型',
      dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
      cascader: [],
      span: 12,
      search: true,
      width: 120,
      display: true,
      overHidden: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'invoiceType',
      rules: [
        {
          required: true,
          message: '请选择发票类型',
        },
      ],
    },
    {
      label: '上传发票',
      prop: 'invoiceFiles',
      type: 'upload',
      overHidden: true,
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      width: 120,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },

      tip: '先上传发票可以自动填充上面部分类容',
      uploadAfter: (res, done) => {
        console.log(res);
        done();
        const { id } = res;
        axios
          .get('/api/vt-admin/purchaseContractInvoiceDetail/analysisInvoice', {
            params: {
              id,
            },
          })
          .then(res => {
            const {
              date: invoiceDate,
              buyerName: companyName,
              totalAmount: invoicePrice,
              number: invoiceNumber,
            } = res.data.data;
            const list = res.data.data.detailList.map(item => {
              const {
                totalPrice,
                amount: productTotalPrice,
                taxAmount: taxPrice,
                taxRate,
                count: number,
                name: productName,
                model: specification,
                unit: unitName,
                price: price,
              } = item;
              return {
                totalPrice,
                productTotalPrice,
                taxPrice,
                taxRate,
                number,
                productName,
                specification,
                unitName,
                price,
                disabled: true,
              };
            });

            if (form.value.detailEntityList) {
              form.value.detailEntityList.push(...list);
            } else {
              form.value.detailEntityList = list;
            }
            form.value.invoiceDate = invoiceDate;
            // form.value.companyName = companyName;
            form.value.invoicePrice = invoicePrice;
            form.value.invoiceNumber = invoiceNumber;
          });
      },
      uploadPreview: file => {
        return;
      },

      action: '/blade-resource/attach/upload',
    },
    {
      type: 'input',
      label: '明细信息',
      span: 24,
      display: true,
      hide: true,
      prop: 'detailEntityList',
      type: 'dynamic',
      rules: [
        {
          required: true,
          message: '请输入明细信息',
        },
      ],
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          done();
        },
        rowDel: (row, done) => {
          done();
        },
        column: [
          {
            label: '货物、应税劳务及服务',
            prop: 'productName',
          },
          {
            label: '规格型号',
            prop: 'specification',
          },
          {
            label: '单位',
            prop: 'unitName',
            width: 80,
          },
          {
            label: '数量',
            prop: 'number',
            width: 80,
            change: (a, b, c) => {
              const { row } = a;
              if (row.productTotalPrice && !row.disabled) {
                row.price = ((row.productTotalPrice * 1) / row.number) * 1;
              }
            },
          },
          {
            label: '单价',
            prop: 'price',
            width: 100,
            change: (a, b, c) => {
              // const { row } = a;
              // if (row.number) {
              //   row.productTotalPrice = (row.number * 1 * row.price * 1).toFixed(2);
              // }
            },
          },
          {
            label: '金额',
            prop: 'productTotalPrice',
            width: 120,
            change: (a, b, c) => {
              // const { row } = a;
              // if (row.taxRate) {
              //   row.taxPrice = ((row.productTotalPrice * 1 * (row.taxRate * 1)) / 100).toFixed(2);

              //   row.totalPrice = (row.productTotalPrice * 1 + row.taxPrice * 1).toFixed(2);
              // }
              const { row } = a;
              if (row.number && !row.disabled) {
                row.price = ((row.productTotalPrice * 1) / row.number) * 1;
              }
            },
          },
          {
            label: '税率',
            type: 'select',
            cell: true,
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            width: 100,
            prop: 'taxRate',
            dicUrl: '/blade-system/dict/dictionary?code=tax',
            change: (a, b, c) => {
              // const { row } = a;
              // if (row.productTotalPrice) {
              //   row.taxPrice = ((row.productTotalPrice * 1 * (row.taxRate * 1)) / 100).toFixed(2);

              //   row.totalPrice = (row.productTotalPrice * 1 + row.taxPrice * 1).toFixed(2);
              // }
              const { row } = a;
              if (row.totalPrice && !row.disabled) {
                row.productTotalPrice = (row.totalPrice / (1 + (row.taxRate * 1) / 100)).toFixed(2);

                row.taxPrice = (row.totalPrice - row.productTotalPrice).toFixed(2);
              }
            },
          },
          {
            label: '税额',
            prop: 'taxPrice',
            width: 120,
          },
          {
            label: '小计',
            prop: 'totalPrice',
            width: 120,
            change: a => {
              form.value.invoicePrice = form.value.detailEntityList
                .reduce((pre, cur) => {
                  return (pre += cur.totalPrice * 1);
                }, 0)
                .toFixed(2);
              const { row } = a;
              if (row.taxRate && !row.disabled) {
                row.productTotalPrice = (row.totalPrice / (1 + (row.taxRate * 1) / 100)).toFixed(2);

                row.taxPrice = (row.totalPrice - row.productTotalPrice).toFixed(2);
              }
            },
          },
        ],
      },
    },

    {
      type: 'date',
      label: '认证日期',
      addDisplay: false,
      editDisplay: false,
      span: 12,
      width: 120,
      search: false,
      searchSpan: 6,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'authenticationDate',
      component: 'wf-daterange-search',
      type: 'date',
      html: true,
      formatter(row, column, cellValue, index) {
        let text;
        if (row.authenticationDate) {
          text = `<div style="color:var(--el-color-success)">${row.authenticationDate}</div>`;
        } else {
          text = `<div style="color:var(--el-color-warning)">未认证</div>`;
        }
        return text;
      },
    },
    {
      type: 'textarea',
      label: '备注',
      overHidden: true,
      span: 24,
      display: true,
      prop: 'remark',
    },
  ],
});

let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/purchaseContractInvoice/save';
const delUrl = '/api/vt-admin/purchaseContractInvoice/deleteByIds?id=';
const updateUrl = '/api/vt-admin/purchaseContractInvoice/update';
const tableUrl = '/api/vt-admin/purchaseContractInvoice/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
let loading = ref(false);
let totalPrice = ref(0);
let taxPrice = ref(0);
let noTaxPrice = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        invoiceStartDate: params.value.invoiceDateRange && params.value.invoiceDateRange[0],
        invoiceEndDate: params.value.invoiceDateRange && params.value.invoiceDateRange[1],
        invoiceDate: null,
        authenticationStartDate:
          params.value.authenticationDate && params.value.authenticationDate[0],
        authenticationEndDate:
          params.value.authenticationDate && params.value.authenticationDate[1],
        authenticationDate: null,
        supplierId: props.supplierId,
        purchaseContractId: props.contractId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records.map(item => {
        return {
          ...item,
          invoiceFiles: item.attachList.map(item1 => {
            return {
              value: item1.id,
              label: item1.originalName,
            };
          }),
        };
      });
      page.value.total = res.data.data.total;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/purchaseContractInvoice/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        invoiceStartDate: params.value.invoiceDateRange && params.value.invoiceDateRange[0],
        invoiceEndDate: params.value.invoiceDateRange && params.value.invoiceDateRange[1],
        invoiceDate: null,
        authenticationStartDate:
          params.value.authenticationDate && params.value.authenticationDate[0],
        authenticationEndDate:
          params.value.authenticationDate && params.value.authenticationDate[1],
        authenticationDate: null,
        supplierId: props.supplierId,
        purchaseContractId: props.contractId,
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
      noTaxPrice.value = res.data.data.noTaxPrice;
      taxPrice.value = res.data.data.taxPrice;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    invoiceFiles: form.invoiceFiles.map(item => item.value).join(','),
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    invoiceFiles: row.invoiceFiles.map(item => item.value).join(','),
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function authentication(row) {
  proxy.$refs.dialogForm.show({
    title: row.name,
    option: {
      column: [
        {
          type: 'date',
          label: '认证日期',
          span: 12,
          display: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          value: dateFormat(new Date(), 'yyyy-MM-dd'),
          prop: 'authenticationDate',
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/purchaseContractInvoice/authentication', {
          id: row.id,
          ...res.data,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          onLoad();
        });
    },
  });
}
let form = ref({});
function copy(row) {
  form.value = row;
  form.value.id = '';
  proxy.$refs.crud.rowAdd();
}
function setSupplierName(id) {
  axios
    .get('/api/vt-admin/purchaseContract/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const { supplierName } = res.data.data;
      form.value.companyName = supplierName;
    });
}
function beforeOpen(done, type) {
  if (type == 'edit') {
    axios
      .get('/api/vt-admin/purchaseContractInvoice/detail', {
        params: {
          id: form.value.id,
        },
      })
      .then(res => {
        form.value = {
          ...res.data.data,
          invoiceFiles: form.value.attachList.map(item => {
            return {
              value: item.id,
              label: item.originalName,
            };
          }),
          detailEntityList: res.data.data.detailEntityList.map(item => {
            return {
              ...item,
              taxRate: '' + item.taxRate * 1,
            };
          }),
        };
      });
  }
  done();
}
let detailDrawer = ref(false);
let detailForm = ref({});
let detailOption = ref({
  border: true,
  header: false,
  menu: false,
  align: 'center',
  column: [
    {
      label: '货物、应税劳务及服务',
      prop: 'productName',
    },
    {
      label: '规格型号',
      prop: 'specification',
    },
    {
      label: '单位',
      prop: 'unitName',
      width: 80,
    },
    {
      label: '数量',
      prop: 'number',
      width: 80,
      formatter: row => {
        return parseFloat(row.number);
      },
    },
    {
      label: '单价',
      prop: 'price',
      width: 100,
      overHidden: true,
    },
    {
      label: '金额',
      prop: 'productTotalPrice',
      width: 120,
    },
    {
      label: '税率',
      type: 'select',
      cell: true,
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },
      width: 100,
      prop: 'taxRate',
      dicUrl: '/blade-system/dict/dictionary?code=tax',
      formatter: row => {
        return parseFloat(row.taxRate) + '%';
      },
    },
    {
      label: '税额',
      prop: 'taxPrice',
      width: 120,
    },
    {
      label: '小计',
      prop: 'totalPrice',
      width: 120,
    },
  ],
});
function viewDetail(row) {
  axios
    .get('/api/vt-admin/purchaseContractInvoice/detail', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      detailForm.value = res.data.data;
      detailDrawer.value = true;
    });
}
function exportData() {
  const { pageSize: size, currentPage: current } = page.value;
  downloadXls({
    url: '/api/vt-admin/purchaseContractInvoice/exportList',
    method: 'post',
    params: {
      size,
      current,
      ...params.value,
      invoiceStartDate: params.value.invoiceDateRange && params.value.invoiceDateRange[0],
      invoiceEndDate: params.value.invoiceDateRange && params.value.invoiceDateRange[1],
      invoiceDate: null,
      authenticationStartDate:
        params.value.authenticationDate && params.value.authenticationDate[0],
      authenticationEndDate: params.value.authenticationDate && params.value.authenticationDate[1],
      authenticationDate: null,
      supplierId: props.supplierId,
      purchaseContractId: props.contractId,
    },
  });
}
let currentRow = ref({});
let productDrawer = ref(false);
let productOption = ref({
  header: false,
  border: true,
  menu: false,
  column: [
    {
      label: '产品',
      prop: 'productId',
      bind: 'productVO.productName',
      cell: false,
    },
    {
      label: '规格型号',
      prop: 'productSpecification',
      bind: 'productVO.productSpecification',
      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '单位',
      prop: 'unitName',
      bind: 'productVO.unitName',
      type: 'input',
      span: 12,
      cell: false,
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      span: 12,
      cell: false,
    },
    {
      label: '单价',
      prop: 'unitPrice',
      type: 'number',
      span: 12,
      cell: false,
    },
    {
      label: '金额',
      prop: 'totalPrice',
      type: 'number',
      span: 12,
      cell: false,
      overHidden: true,
    },
  ],
});
function viewProduct(row) {
  axios
    .get('/api/vt-admin/purchaseContractInvoice/detail', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      detailForm.value = res.data.data;
      productDrawer.value = true;
    });
}
function toReceiveTicket() {
  router.push({
    path: '/SRM/supplier/supplierStatement',
  });
}
</script>

<style lang="scss" scoped></style>

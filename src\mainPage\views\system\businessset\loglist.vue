<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
    <template #moduleType="{row}">
      <el-tag type="success" size="normal"  effect="plain"  >{{ row.$moduleType }}</el-tag>
      
    </template>
    <template #logContent="{row}">
      <span v-html="cusHtml(row.logContent)"></span>
    </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: true,
  menu:false,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
  {
      label: '日志ID',
      prop: 'id',
      width: 250,
      overHidden: true,
    },
    {
      label: '日志模块',
      prop: 'moduleType',
      width:120,
      type:'select',
      search:true,
      overHidden: true,
      dicData: [
        {
          value: 0,
          label: '商机',
        },
        {
          value: 1,
          label: '方案',
        },
        {
          value: 2,
          label: '报价',
        },
        {
          value: 3,
          label: '采购订单',
        },
        {
          value: 4,
          label: '销售合同',
        },
        {
          value: 5,
          label: '供应商合同',
        },
        {
          value: 6,
          label: '项目',
        },
      ],
    },
    {
      label: '日志类型',
      prop: 'logTypeName',
      width: 250,
      overHidden: true,
    },
    {
      label: '日志内容',
      prop: 'logContent',
    },

    {
      label: '操作人',
      prop: 'createName',
    },

    {
      label: '操作时间',
      prop: 'createTime',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/businessOpportunityProgress/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function cusHtml(content) {

const one =   content.replace(/</g, '<span style="color:var(--el-color-primary)">');
 const two =  one.replace(/>/g, '</span>');

  return two
}
</script>

<style lang="scss" scoped></style>

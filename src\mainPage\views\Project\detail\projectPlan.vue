<template>
  <el-row :gutter="20" v-if="isCompletePlan == 0" style="height: 100%">
    <el-col :span="4">
      <el-text
        >总计：<el-text type="primary" size="large">{{ allHours && allHours.toFixed(2) }}</el-text
        >工时</el-text
      >
      <avue-tree
        ref="tree"
        style="height: 100%; margin-top: 8px"
        :option="treeOption"
        :data="treeData"
        @node-click="nodeClick"
        @save="handleSave"
        @del="handleDel"
        :before-open="handleOpen"
        @update="handleUpdate"
        node-key="id"
      >
        <template #default="{ node, data }">
          <div style="display: flex; justify-content: space-between">
            <span style="margin-right: 10px">{{ node.label }}</span>
            <el-space>
              <el-text type="primary">{{
                parseFloat(data.planHours) == 0 ? '' : parseFloat(data.planHours)
              }}</el-text>
              <!-- <el-text>{{ data.completeHours }}</el-text> -->
            </el-space>
          </div>
        </template>
      </avue-tree>
    </el-col>
    <el-col :span="20">
      <avue-crud
        :option="option"
        :data="tableData"
        v-model:page="page"
        v-model:search="params"
        @on-load="onLoad"
        @row-update="rowUpdate"
        @row-save="rowSave"
        @row-click="rowClick"
        :table-loading="loading"
        :before-open="beforeOpen"
        ref="crud"
        @keyup.enter="onLoad"
        @row-del="rowDel"
        @search-reset="onLoad"
        @search-change="searchChange"
        @refresh-change="onLoad"
        @current-change="onLoad"
        @size-change="onLoad"
        v-model="form"
      >
        <template #menu-left>
          <span style="display: flex; justify-content: flex-start; align-items: center">
            <el-button type="primary" icon="plus" @click="$refs.crud.rowAdd">新增</el-button>
            <el-button type="warning" @click="exportExcel" icon="download">导出</el-button>
            <el-button type="primary" plain @click="copyPlan" icon="copyDocument"
              >从其他项目导入</el-button
            >
            <el-checkbox
              v-model="isNeedPlan"
              style="margin-left: 10px"
              @change="changeIsNeedPlan"
              label="无需项目计划和工时"
            ></el-checkbox>
          </span>
        </template>

        <template #menu="{ row, index }">
          <el-button
            type="primary"
            @click="register(row)"
            icon="edit"
            v-if="row.planStatus != 1"
            text
            >登记</el-button
          >
          <el-button
            type="primary"
            @click="$refs.crud.rowEdit(row, index)"
            icon="edit"
            v-if="row.planStatus != 1"
            text
            >编辑</el-button
          >
          <el-button
            type="primary"
            @click="complete(row)"
            text
            icon="check"
            v-if="row.planStatus != 1"
            >完成</el-button
          >

          <!-- <el-button type="primary" @click="delay(row)" icon="clock" v-if="row.planStatus != 1" text
            >延期</el-button
          > -->
          <el-button
            type="primary"
            @click="$refs.crud.rowDel(row)"
            v-if="row.planStatus != 1 || proxy.$store.getters.userInfo.role_name.indexOf('admin') > -1 || proxy.$store.getters.userInfo.role_name.indexOf('manager') > -1"
            icon="delete"
            text
            >删除</el-button
          >
        </template>
        <template #planName-form>
          <el-autocomplete
            v-model="form.planName"
            :fetch-suggestions="querySearch"
            :trigger-on-focus="false"
            clearable
            style="width: 100%"
            class="inline-input w-50"
            placeholder="请输入任务名称"
          >
          </el-autocomplete>
        </template>
        <template #status="{ row }">
          <el-popover
            placement="top"
            :width="200"
            trigger="hover"
            :disabled="row.overDays == 0 && row.planStatus == 0"
          >
            <template #reference>
              <el-tag
                effect="plain"
                style="cursor: pointer"
                v-if="row.overDays == 0 && row.planStatus == 0"
                >未完成</el-tag
              >
              <el-tag
                effect="plain"
                style="cursor: pointer"
                type="success"
                v-if="row.planStatus == 1"
                >已完成</el-tag
              >
              <el-tag
                effect="plain"
                style="cursor: pointer"
                type="danger"
                v-if="row.overDays > 0 && row.planStatus == 0"
                >延期</el-tag
              >
            </template>
            <template #default>
              <el-form>
                <el-form-item label="完成时间:" v-if="row.planStatus == 1">{{
                  row.actualDate
                }}</el-form-item>
                <el-form-item label="完成附件:" v-if="row.planStatus == 1">
                  <File :fileList="row.attachList"></File
                ></el-form-item>
                <el-form-item label="延期时间:" v-if="row.delayDate">
                  {{ row.delayDate }}
                </el-form-item>
                <el-form-item label="延期说明:" v-if="row.delayDate">
                  {{ row.delayRemark }}
                </el-form-item>
              </el-form>
            </template>
          </el-popover>
        </template>
        <template #radio="{ row }">
          <el-radio v-model="selectRow" :label="row.id">{{}}</el-radio>
        </template>
        <template #epibolyActualDays="{ row }">
          <el-link type="primary" @click="viewDetail(row,0)">{{ row.epibolyActualDays }}</el-link>
        </template>
        <template #spotWorkerActualDays="{ row }">
          <el-link type="primary" @click="viewDetail(row,1)">{{ row.spotWorkerActualDays }}</el-link>
        </template>
        <template #interiorWorkerActualDays="{ row }">
          <el-link type="primary" @click="viewDetail(row,2)">{{ row.interiorWorkerActualDays }}</el-link>
        </template>
      </avue-crud></el-col
    >
  </el-row>
  <el-empty v-else description="该项目无需计划和工时">
    <el-button type="primary" @click="handleOpen">开启</el-button>
  </el-empty>
  <!-- 项目选择 -->
  <wfProjectSelect
    :userUrl="'/api/vt-admin/project/page?selectType=0'"
    ref="projectSelectRef"
    @onConfirm="handleConfirm"
  ></wfProjectSelect>
  <dialogForm ref="dialogForm"></dialogForm>
  <el-dialog
    width="60%"
    title="实际工时记录"
    v-model="dialogVisible"
    class="avue-dialog avue-dialog--top"
  >
    <!-- <slot ></slot> -->
    <HoursList :planId="currentId" :type="currentType"></HoursList>
    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false">关 闭</el-button>
      <!-- <el-button @click="$refs.dialogForm.submit()" type="primary">确 定</el-button> -->
    </div>
  </el-dialog>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, computed, watchEffect, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import { dateFormat } from '@/utils/date';
import { downloadXls } from '@/utils/download.js';
import wfProjectSelect from '@/components/Y-UI/wf-project-select.vue';
import { ElDatePicker, ElMessage, ElMessageBox } from 'element-plus';
import HoursList from './hoursList.vue';
let option = ref({
  // height: 'auto',
  align: 'center',
  editBtn: false,
  delBtn: false,
  addBtn: false,
  // calcHeight: 30,
  labelWidth: 150,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 280,
  showSummary: true,
  sumColumnList: [
    {
      label: '小计:',
      name: 'sum',
      type: 'sum',
    },
  ],
  border: true,
  column: [
    {
      label: '',
      prop: 'radio',
      width: 60,
      hide: true,
      display: false,
    },
    {
      label: '计划编号',
      prop: 'planCode',
      width: 100,
      span: 24,
      order: 6,
      overHidden: true,
    },
    {
      label: '计划名称',
      prop: 'planName',
      width: 150,
      span: 24,
      rules: [
        {
          required: true,
          message: '请输入计划名称',
          trigger: 'blur',
        },
      ],
      order: 5,
      overHidden: true,
    },
    {
      label: '计划内容',
      prop: 'planContent',
      width: 120,
      type: 'textarea',
      order: 1,
      overHidden: true,
      span: 24,
    },
    {
      label: '计划的时间（天）',
      prop: 'planDays',
      width: 100,
      span: 12,
      order: 4,
      overHidden: true,
      rules: [
        {
          required: true,
          message: '请输入计划人工（人）',
          trigger: 'blur',
        },
      ],
      type: 'number',
    },
    {
      label: '计划的人工（人）',
      prop: 'planHumans',
      width: 100,
      span: 12,
      type: 'number',
      order:3,
      overHidden: true,
      rules: [
        {
          required: true,
          message: '请输入计划工时（人•天）',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '计划工时（人•天）',
      prop: 'sum',
      width: 100,
      span: 12,
      display: false,

      type: 'number',
      overHidden: true,
    },

    // {
    //   label: '计划开始时间',
    //   prop: 'planStartDate',
    //   type: 'date',
    //   width: 120,
    //   order: 9,
    //   span: 12,
    //   format: 'YYYY-MM-DD',
    //   valueFormat: 'YYYY-MM-DD',
    //   // blur:val => {
    //   //   console.log(val);
    //   // }
    // },
    // {
    //   label: '计划完成时间',
    //   prop: 'planDate',
    //   type: 'date',
    //   width: 120,
    //   span: 12,
    //   format: 'YYYY-MM-DD',
    //   valueFormat: 'YYYY-MM-DD',
    // },
    //   {
    //     label: '计划完成时间',
    //     prop: 'planEndTime',
    //     type:'date',
    //     format: 'YYYY-MM-DD',
    //     valueFormat: 'YYYY-MM-DD',
    //   },
    // {
    //   label: '计划阶段',
    //   prop: 'stage',
    //   type: 'input',
    //   span: 24,
    // },
    {
      label: '劳务外包（实际人天）',
      prop: 'epibolyActualDays',

      addDisplay: false,
      editDisplay: false,
      type: 'textarea',
      span: 24,
    },
    {
      label: '点工（实际人天）',
      prop: 'spotWorkerActualDays',

      addDisplay: false,
      editDisplay: false,
      type: 'textarea',
      span: 24,
    },
    {
      label: '内部（实际人天）',
      prop: 'interiorWorkerActualDays',

      addDisplay: false,
      editDisplay: false,
      type: 'textarea',
      span: 24,
    },

    {
      label: '状态',
      prop: 'status',
      width: 120,
      addDisplay: false,
      editDisplay: false,
      type: 'textarea',
      span: 24,
    },
    {
      label: '计划排序',
      prop: 'sort',
      hide: true,
      span: 24,
    },

    // {
    //   label: '实际完成时间',
    //   prop: 'actualDate',
    //   type: 'date',
    //   format: 'YYYY-MM-DD',
    //   valueFormat: 'YYYY-MM-DD',
    //   type: 'date',
    //   width: 120,
    //   addDisplay: false,
    //   editDisplay: false,
    // },
    // {
    //   label: '完成附件',
    //   prop: 'fileIds',
    //   width: 120,
    //   overHidden: true,
    //   addDisplay: false,
    //   editDisplay: false,
    // },
    // {
    //   label: '延期时间',
    //   prop: 'delayDate',
    //   addDisplay: false,
    //   width: 120,
    //   editDisplay: false,
    // },
    // {
    //   label: '逾期',
    //   prop: 'overDays',
    //   addDisplay: false,
    //   width: 80,
    //   editDisplay: false,
    // },
    // {
    //   label: '延期备注',
    //   prop: 'delayRemark',
    //   addDisplay: false,
    //   editDisplay: false,
    // },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const props = defineProps({
  check: {
    type: Boolean,
    default: false,
  },
  projectId: {
    type: String,
    default: '',
  },
  humanInfo: {
    type: Object,
    default: () => {
      return {
        humanResourceEntities: [],
      };
    },
  },
  isCompletePlan: {
    type: Number || String,
  },
  startDate: {
    type: String,
  },
});

onMounted(() => {
  if (props.check) {
    option.value.menu = false;
    option.value.addBtn = false;
    option.value.column[0].hide = false;
  }
});
watch(
  () => props.projectId,
  () => {
    onLoad();
    getTreeData();
  }
);
const addUrl = '/api/vt-admin/projectPlan/save';
const delUrl = '/api/vt-admin/projectPlan/remove?ids=';
const updateUrl = '/api/vt-admin/projectPlan/update';
const tableUrl = '/api/vt-admin/projectPlan/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let currentStageId = ref(null);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        projectId: route.query.id,
        stageId: currentStageId.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records.map(item => {
        return {
          ...item,
          sum: item.planHumans * item.planDays,
        };
      });
      // tableData.value = [{ status: 0 }];
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    projectId: route.query.id,
    planHours:form.planHumans * form.planDays,
    stageId: currentStageId.value,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        proxy.$store.dispatch('getMessageList');
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function complete(row) {
  let previewUrlList = [];
  proxy.$refs.dialogForm.show({
    title: '完成',
    option: {
      labelWidth: 120,
      column: [
        {
          type: 'date',
          label: '实际完成时间',
          span: 24,
          value: dateFormat(new Date(), 'yyyy-MM-dd'),
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          prop: 'actualDate',
        },

        {
          label: '附件',
          prop: 'fileIds',
          type: 'upload',
          overHidden: true,
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
          uploadAfter: (res, done) => {
            console.log(res, done);
            previewUrlList.push({
              thumbUrl: res.link,
              id: res.id,
              url: res.link,
            });
            done();
          },
          uploadDelete: (file, column) => {
            console.log(file);
            previewUrlList = previewUrlList.filter(item => item.id != file.url);
            return new Promise((resolve, reject) => {
              resolve();
            });
          },
          uploadPreview: file => {
            proxy.$ImagePreview(previewUrlList, 0);
          },
        },
      ],
    },
    callback(res) {
      let url = '/api/vt-admin/projectPlan/complete';
      const data = {
        id: row.id,
        ...res.data,
        files: res.data.fileIds.map(item => item.value).join(','),
      };
      axios.post(url, data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
function delay(row) {
  proxy.$refs.dialogForm.show({
    title: '开票',
    option: {
      labelWidth: 120,
      column: [
        {
          type: 'date',
          label: '延期时间',
          span: 24,

          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          prop: 'delayDate',
        },
        {
          label: '延期备注',
          prop: 'delayRemark',
          addDisplay: false,
          type: 'textarea',
          span: 24,
          editDisplay: false,
        },
        // {
        //   label: '附件',
        //   prop: 'fileIds',
        //   type: 'upload',
        //   overHidden: true,
        //   dataType: 'object',
        //   loadText: '附件上传中，请稍等',
        //   span: 12,
        //   slot: true,
        //   propsHttp: {
        //     res: 'data',
        //     url: 'id',
        //     name: 'originalName',
        //     // home: 'https://www.w3school.com.cn',
        //   },
        //   action: '/blade-resource/attach/upload',
        // },
      ],
    },
    callback(res) {
      let url = '/api/vt-admin/projectPlan/delay';
      const data = {
        id: row.id,
        ...res.data,
      };
      axios.post(url, data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}

// 左边树操作
let treeData = ref([]);
let treeOption = ref({
  defaultExpandAll: true,
  addBtn: true,
  formOption: {
    labelWidth: 100,
    menuPosition: 'right',
    column: [
      {
        label: '阶段名称',
        prop: 'stage',
        span: 24,
      },
      // {
      //   label: '备注',
      //   prop: 'remark',
      //   type: 'textarea',
      //   span: 24,
      // },
    ],
  },
  props: {
    label: 'stage',
    value: 'id',
    children: 'children',
  },
});
onMounted(() => {
  getTreeData();
});
function getTreeData() {
  axios
    .get('/api/vt-admin/projectStage/list', {
      params: {
        size: 5000,
        projectId: route.query.id,
      },
    })
    .then(res => {
      treeData.value = res.data.data;
      proxy.$nextTick(() => {
        proxy.$refs.tree.setCurrentKey(treeData.value[0].id);
        nodeClick(treeData.value[0]);
      });
    });
}
function handleSave(parent, data, done, loading) {
  if (parent.id) {
    done();
    getTreeData();
    return proxy.$message.warning('暂不支持往下添加');
  }
  // loading()
  axios
    .post('/api/vt-admin/projectStage/save', { ...data, projectId: route.query.id })
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        getTreeData();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function handleUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post('/api/vt-admin/projectStage/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function handleDel(form) {
  console.log(form);
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post('/api/vt-admin/projectStage/remove?ids=' + form.data.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        getTreeData();
      });
    })
    .catch(() => {});
}
function nodeClick(node) {
  currentStageId.value = node.id;
  onLoad();
  // option.value.addBtn = true;
}
function querySearch(value, cb) {
  console.log(value);
  const arr = [
    {
      value: '项目交接',
    },
    {
      value: '准备工具、材料',
    },
    {
      value: '施工人员落实及培训',
    },
    {
      value: '预付款落实',
    },
    {
      value: '进度款落实',
    },
    {
      value: '培训及使用说明交付',
    },
    {
      value: '所有文档资料交付',
    },
    {
      value: '结算及结算款落实',
    },
  ];
  cb(arr.map(item => (item.value.indexOf(value) > -1 ? item : '')));
}
function rowClick(row) {
  selectRow.value = row.id;
  selectRowInfo.value = row;
}
let selectRow = ref(null);
let selectRowInfo = ref({});
defineExpose({
  selectRowInfo,
  selectRow,
});
function exportExcel() {
  downloadXls({
    url: '/api/vt-admin/projectPlan/exportList',
    method: 'post',
    params: { projectId: route.query.id },
  });
}
let projectSelectRef = ref();
function copyPlan() {
  projectSelectRef.value.visible = true;
}
function handleConfirm(id) {
  axios
    .post('/api/vt-admin/projectPlan/copy', {
      id: id,
      targetId: props.projectId,
    })
    .then(res => {
      ElMessage.success(res.data.msg);
      getTreeData();
    });
}
const allHours = computed(() => {
  return treeData.value.reduce((pre, cur) => {
    return pre + cur.planHours * 1;
  }, 0);
});

let addOption = ref({
  // height: 'auto',
  align: 'center',
  calcHeight: 30,
  searchMenuSpan: 4,
  cellBtn: true,
  searchSpan: 4,
  labelWidth: 120,
  menuWidth: 270,
  column: [{}],
  column: {
    index: {
      type: 'input',
      width: 80,
      label: '序号',
    },
    userName: {
      label: '工人/团队姓名',
      hide: true,
      width: 120,
      overHidden: true,
      click: () => {
        proxy.$refs.humanSelectRef.open();
      },
    },

    // customSalary: {
    //   label: '自定义工价',
    //   width: 120,
    //   // hide: true,
    //   cell: true,
    //   //   addDisplay: false,
    // },
    hours: {
      label: '工时（人·天）',
      width: 120,
      //   addDisplay: false,
      cell: true,
      type: 'number',
    },
    customSalary: {
      label: '工价（人·天）',
      type: 'select',
      width: 200,
      cell: true,
      dicData: [],
      allowCreate: true,
      filterable: true,
      // hide: true,

      props: {
        value: 'salary',
        label: 'salary',
      },
    },
    type: {
      label: '工人类型',
      width: 180,
      type: 'radio',
      dicData: [
        {
          value: 0,
          label: '外包',
        },
        {
          value: 1,
          label: '点工',
        },
      ],
      //   addDisplay: false,
      hide: true,
    },
    startTime: {
      label: '工作开始时间',

      type: 'date',
      width: 185,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      cell: true,
    },
    endTime: {
      label: '工作结束时间',
      width: 185,
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      cell: true,
    },
    planName: {
      label: '关联计划',
      width: 150,
      type: 'input',
      clearable: false,
      overHidden: true,
      click: () => {
        planDialogVisible.value = true;
      },
      span: 24,
      cell: true,
    },
    workContent: {
      label: '工作内容',
      overHidden: true,
      type: 'textarea',
      span: 24,
      cell: true,
      row: 2,
    },
  },
});

const userList = ref([]);
if (props.humanInfo.technologyUserInfo?.id) {
  userList.value.push({
    id: props.humanInfo.technologyUserInfo.id,
    name: props.humanInfo.technologyUserInfo.name,
  });
}
if (props.humanInfo.interiorUserList?.length > 0) {
  props.humanInfo.interiorUserList.forEach(item => {
    userList.value.push({
      id: item.id,
      name: item.name,
    });
  });
}
if (props.humanInfo.humanResourceEntities?.length > 0) {
  
  props.humanInfo.humanResourceEntities.forEach(item => {
    userList.value.push({
      id: item.id,
      name: item.name,
      salary: item.salaryVOS[0]?.salary,
    });
  });
}
function register(row) {
  proxy.$refs.dialogForm.show({
    title: '工时登记',
    width: '70%',
    option: {
      size: 'small',
      column: [
        {
          label: '',
          prop: 'list',
          type: 'dynamic',
          labelWidth: 0,
          span: 24,
          value: [
            {
              workContent: row.planContent,
            },
          ],
          index: false,
          children: {
            align: 'center',
            addBtn: true,
            delBtn: true,
            headerAlign: 'center',

            showSummary: true,
            rowAdd: done => {
              done({
                workContent: row.planContent,
              });
            },
            sumColumnList: [{ name: 'actualPrice', type: 'sum' }],
            column: {
              humanId: {
                label: '工人/团队姓名',
                type: 'select',
                width: 160,
                overHidden: true,
                dicData: userList.value,
                rules: [{ required: true, message: '请选择工人/团队' }],
                props: {
                  value: 'id',
                  label: 'name',
                },
                change: ({ row, value, column }) => {
                  if (!value) return;
                
                  const data = column.dicData.find(item => item.id === value);
                  row.customSalary = data.salary;
                  axios.get('/api/vt-admin/projectHumanSalary/actualPay',{
                    params:{
                      projectId:props.projectId,
                      humanId:value
                    }
                  }).then(res => {
                    row.type = res.data.data.type
                   
                  })
                },
              },
            

              // customSalary: {
              //   label: '自定义工价',
              //   width: 120,
              //   // hide: true,
              //   cell: true,
              //   //   addDisplay: false,
              // },
              hours: {
                label: '工时（人·天）',
                width: 150,
                //   addDisplay: false,
                cell: true,
                rules: [
                  {
                    required: true,
                    message: '请输入工时',
                    trigger: 'blur',
                  },
                ],
                type: 'number',
              },
              customSalary: {
                label: '工价（人·天）',
                type: 'input',
                width: 200,
                cell: true,
               
              },

              startTime: {
                label: '工作开始时间',

                type: 'date',
                width: 185,
                format: 'YYYY-MM-DD',
                valueFormat: 'YYYY-MM-DD HH:mm:ss',
                cell: true,
              },
              endTime: {
                label: '工作结束时间',
                width: 185,
                type: 'date',
                format: 'YYYY-MM-DD',
                valueFormat: 'YYYY-MM-DD HH:mm:ss',
                cell: true,
              },

              workContent: {
                label: '工作内容',
                overHidden: true,
                type: 'textarea',
                span: 24,
                cell: true,
                row: 2,
              },
            },
          },
        },
      ],
    },
    callback(res) {
      const projectHours = res.data.list;
      axios
        .post('/api/vt-admin/projectHours/batchSave', {
          id: row.id,
          projectHours,
        })
        .then(r => {
          proxy.$message.success(r.data.msg);
          res.close();
          onLoad();
          proxy.$refs.crud.toggleSelection();
        });
    },
  });
}
let dialogVisible = ref(false);
let currentId = ref(null);
let currentType = ref(0)  // 0 外包  1  点工 2 内部
function viewDetail(row,value) {
  currentId.value = row.id;
  dialogVisible.value = true;
  currentType.value = value;
}

let isNeedPlan = ref(false);
const emits = defineEmits(['success']);
function changeIsNeedPlan(value) {
  if (value) {
    ElMessageBox.confirm('是否确认不需要计划', '提示', {
      type: 'warning',
    })
      .then(() => {
        isNeedPlan.value = false;
        axios
          .post('/api/vt-admin/project/updateCompletePlan', {
            id: route.query.id,
            isCompletePlan: 1,
          })
          .then(res => {
            ElMessage.success(res.data.msg);
            emits('success');
          });
      })
      .catch(() => {
        isNeedPlan.value = false;
      });
  }
}
function handleOpen() {
  axios
    .post('/api/vt-admin/project/updateCompletePlan', { id: route.query.id, isCompletePlan: 0 })
    .then(res => {
      ElMessage.success(res.data.msg);
      emits('success');
    });
}
let startDate = ref('');

function beforeOpen(done, type) {
  if (type === 'add') {
    if (!props.startDate) {
      ElMessageBox({
        title: '未填写开工时间，请先完善',
        message: () => [
          h('p', null, [
            h('div', { style: 'margin-top:5px' }, [
              h('span', null, '开工时间：'),
              h(ElDatePicker, {
                modelValue: startDate.value,
                valueFormat: 'YYYY-MM-DD',
                type: 'date',
                placeholder: '请选择开工时间',
                'onUpdate:modelValue': val => {
                  startDate.value = val;
                },
              }),
            ]),
          ]),
        ],
      }).then(res => {
        axios
          .post('/api/vt-admin/project/update', {
            id: props.projectId,
            startDate: startDate.value,
          })
          .then(e => {
            proxy.$message.success('操作成功');
            emits('success');
            done();
          });
      });
    }else{
      done();
    }
  } else {
    done();
  }
}
</script>

<style lang="scss" scoped></style>

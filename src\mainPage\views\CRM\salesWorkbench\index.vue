<template>
  <div class="sales-workbench">
    <!-- 顶部导航栏
    <header class="workbench-header">
      <div class="header-container">
        <div class="header-content">
          左侧Logo和标题
          <div class="header-left">
            <div class="logo-icon">
              <el-icon><TrendCharts /></el-icon>
            </div>
            <span class="header-title">销售智能工作台</span>
          </div>

          右侧用户信息和通知
          <div class="header-right">
            <el-badge :value="notificationCount" class="notification-badge">
              <el-button :icon="Bell" circle class="notification-btn" />
            </el-badge>
            <div class="user-info">
              <el-avatar :size="32" :src="userAvatar" />
              <span class="username">{{ userName }}</span>
              <el-icon><ArrowDown /></el-icon>
            </div>
          </div>
        </div>
      </div>
    </header>

    快捷操作栏
    <div class="quick-actions">
      <el-button type="primary" :icon="Plus" @click="handleAddCustomer">
        新增客户
      </el-button>
      <el-button :icon="Lightning" @click="handleCreateOpportunity">
        创建商机
      </el-button>
      <div class="urgent-alerts">
        <el-tag type="danger" effect="dark">
          <el-icon><Warning /></el-icon>
          {{ urgentCount }}个紧急
        </el-tag>
      </div>
    </div> -->
    <!-- 主体内容 -->
    <main class="main-content">
      <!-- 页面标题和时间筛选 -->
      <!-- <div class="page-header">
        <div class="page-title-section">
          <h1 class="page-title">业务数据概览</h1>
          <p class="page-subtitle">全面掌握客户、商机、合同、收款数据动态</p>
        </div>
        <div class="time-filter">
          <el-radio-group v-model="timeFilter" class="time-filter-group">
            <el-radio-button label="today">今日</el-radio-button>
            <el-radio-button label="week">本周</el-radio-button>
            <el-radio-button label="month">本月</el-radio-button>
            <el-radio-button label="custom">
              <el-icon><Calendar /></el-icon>自定义
            </el-radio-button>
          </el-radio-group>
        </div>
      </div> -->

      <!-- 关键预警提示 - 置顶显示 -->
      <section class="alert-section">
        <el-alert type="error" :closable="false" class="urgent-alert">
          <template #title>
            <div class="alert-content">
              <el-icon class="alert-icon"><Warning /></el-icon>
              <span
                >您有 <strong>{{ overduePayments }}个逾期收款</strong> 和
                <strong>{{ overdueOpportunities }}个超期未跟进商机</strong> 需要处理</span
              >
              <el-button type="primary" text @click="viewAllAlerts">查看全部提醒</el-button>
            </div>
          </template>
        </el-alert>
      </section>

      <!-- 核心指标卡片 - 最显眼位置 -->
      <section class="metrics-section">
        <h2 class="section-title">
          <el-icon><Odometer /></el-icon>核心业务指标
        </h2>
        <div class="metrics-grid">
          <!-- 客户总数 -->
          <div class="metric-card-new">
            <div class="metric-header">
              <div class="metric-title-section">
                <span class="metric-title">新增客户数</span>
                <span class="metric-summary">总计 {{ customerStats?.total || 0 }}</span>
              </div>
              <div class="metric-icon-new primary">
                <el-icon><User /></el-icon>
              </div>
            </div>
            <div style="display: flex; gap: 20px">
              <div>
                <div class="metric-title">本月</div>
                <div class="metric-value-new">{{ customerStats?.currentMonth || 0 }}</div>
                <div
                  class="metric-growth-new"
                  :class="
                    parseFloat(customerStats?.lastMonthPercent || '0') >= 0
                      ? 'positive'
                      : 'negative'
                  "
                >
                  <el-icon
                    ><ArrowUp
                      v-if="parseFloat(customerStats?.lastMonthPercent || '0') >= 0" /><ArrowDown
                      v-else
                  /></el-icon>
                  <span>{{ customerStats?.lastMonthPercent || '0%' }} 较上月</span>
                </div>
              </div>
              <div>
                <div class="metric-title">本年</div>
                <div class="metric-value-new">{{ customerStats?.currentYear || 0 }}</div>
                <div
                  class="metric-growth-new"
                  :class="
                    parseFloat(customerStats?.lastYearPercent || '0') >= 0 ? 'positive' : 'negative'
                  "
                >
                  <el-icon
                    ><ArrowUp
                      v-if="parseFloat(customerStats?.lastYearPercent || '0') >= 0" /><ArrowDown
                      v-else
                  /></el-icon>
                  <span>{{ customerStats?.lastYearPercent || '0%' }} 较上年</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 活跃商机 -->
          <div class="metric-card-new warning-border">
            <div class="metric-header">
              <div class="metric-title-section">
                <span class="metric-title">商机数量</span>
                <span class="metric-summary">总计 {{ businessStats?.total || 0 }}</span>
              </div>
              <div class="metric-icon-new warning">
                <el-icon><Lightning /></el-icon>
              </div>
            </div>
            <div style="display: flex; gap: 12px">
              <div style="flex: 1">
                <div class="metric-title">本月</div>
                <div class="metric-value-new">{{ businessStats?.currentMonth || 0 }}</div>
                <div
                  class="metric-growth-new"
                  :class="
                    parseFloat(businessStats?.lastMonthPercent || '0') >= 0
                      ? 'positive'
                      : 'negative'
                  "
                >
                  <el-icon
                    ><ArrowUp
                      v-if="parseFloat(businessStats?.lastMonthPercent || '0') >= 0" /><ArrowDown
                      v-else
                  /></el-icon>
                  <span>{{ businessStats?.lastMonthPercent || '0%' }} 较上月</span>
                </div>
              </div>
              <div style="flex: 1">
                <div class="metric-title">本年</div>
                <div class="metric-value-new">{{ businessStats?.currentYear || 0 }}</div>
                <div
                  class="metric-growth-new"
                  :class="
                    parseFloat(businessStats?.lastYearPercent || '0') >= 0 ? 'positive' : 'negative'
                  "
                >
                  <el-icon
                    ><ArrowUp
                      v-if="parseFloat(businessStats?.lastYearPercent || '0') >= 0" /><ArrowDown
                      v-else
                  /></el-icon>
                  <span>{{ businessStats?.lastYearPercent || '0%' }} 较上年</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 合同金额 -->
          <div class="metric-card-new success-border">
            <div class="metric-header">
              <div class="metric-title-section">
                <span class="metric-title">合同金额</span>
                <span class="metric-summary">总计 ¥{{ contractStats?.total || 0 }}</span>
              </div>
              <div class="metric-icon-new success">
                <el-icon><Document /></el-icon>
              </div>
            </div>
            <div style="display: flex; gap: 12px">
              <div style="flex: 1">
                <div class="metric-title">本月</div>
                <div class="metric-value-new">¥{{ contractStats?.currentMonth || 0 }}</div>
                <div
                  class="metric-growth-new"
                  :class="
                    parseFloat(contractStats?.lastMonthPercent || '0') >= 0
                      ? 'positive'
                      : 'negative'
                  "
                >
                  <el-icon
                    ><ArrowUp
                      v-if="parseFloat(contractStats?.lastMonthPercent || '0') >= 0" /><ArrowDown
                      v-else
                  /></el-icon>
                  <span>{{ contractStats?.lastMonthPercent || '0%' }} 较上月</span>
                </div>
              </div>
              <div style="flex: 1">
                <div class="metric-title">本年</div>
                <div class="metric-value-new">¥{{ contractStats?.currentYear || 0 }}</div>
                <div
                  class="metric-growth-new"
                  :class="
                    parseFloat(contractStats?.lastYearPercent || '0') >= 0 ? 'positive' : 'negative'
                  "
                >
                  <el-icon
                    ><ArrowUp
                      v-if="parseFloat(contractStats?.lastYearPercent || '0') >= 0" /><ArrowDown
                      v-else
                  /></el-icon>
                  <span>{{ contractStats?.lastYearPercent || '0%' }} 较上年</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 已收款金额 -->
          <div class="metric-card-new info-border">
            <div class="metric-header">
              <div class="metric-title-section">
                <span class="metric-title">已收款金额</span>
                <span class="metric-summary">总计 ¥{{ receivedStats?.total || 0 }}</span>
              </div>
              <div class="metric-icon-new info">
                <el-icon><Money /></el-icon>
              </div>
            </div>
            <div style="display: flex; gap: 12px">
              <div style="flex: 1">
                <div class="metric-title">本月</div>
                <div class="metric-value-new">¥{{ receivedStats?.currentMonth || 0 }}</div>
                <div
                  class="metric-growth-new"
                  :class="
                    parseFloat(receivedStats?.lastMonthPercent || '0') >= 0
                      ? 'positive'
                      : 'negative'
                  "
                >
                  <el-icon
                    ><ArrowUp
                      v-if="parseFloat(receivedStats?.lastMonthPercent || '0') >= 0" /><ArrowDown
                      v-else
                  /></el-icon>
                  <span>{{ receivedStats?.lastMonthPercent || '0%' }} 较上月</span>
                </div>
              </div>
              <div style="flex: 1">
                <div class="metric-title">本年</div>
                <div class="metric-value-new">¥{{ receivedStats?.currentYear || 0 }}</div>
                <div
                  class="metric-growth-new"
                  :class="
                    parseFloat(receivedStats?.lastYearPercent || '0') >= 0 ? 'positive' : 'negative'
                  "
                >
                  <el-icon
                    ><ArrowUp
                      v-if="parseFloat(receivedStats?.lastYearPercent || '0') >= 0" /><ArrowDown
                      v-else
                  /></el-icon>
                  <span>{{ receivedStats?.lastYearPercent || '0%' }} 较上年</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 总应收款 -->
          <div class="metric-card-new danger-border">
            <div class="metric-header">
              <div class="metric-title-section">
                <span class="metric-title">总应收款</span>
                <span class="metric-summary">当前 ¥{{ receivableStats?.total || 0 }}</span>
              </div>
              <div class="metric-icon-new danger">
                <el-icon><Money /></el-icon>
              </div>
            </div>
            <div class="metric-value-new">¥{{ receivableStats?.total || 0 }}</div>
            <div class="metric-growth-new warning">
              <el-icon><Warning /></el-icon>
              <span>逾期¥{{ receivableStats?.overDueReceivablePrice || 0 }}</span>
            </div>
          </div>
        </div>
      </section>

      <!-- 业务卡片区域 - 每行三个 -->
      <div class="business-cards-grid">
        <!-- 超期未跟进商机 -->
        <el-card
          class="business-card overdue-opportunities-card"
          shadow="hover"
        >
          <template #header>
            <div class="card-header">
              <el-icon class="header-icon"><Warning /></el-icon>
              <span>超期未跟进商机</span>
            </div>
          </template>

          <div
            class="opportunity-list scrollable-list"
            :class="{ 'no-scroll': overdueBusinessList.length <= 3 }"
            ref="overdueListRef"
            @wheel="handleOverdueWheel"
            @mouseenter="pauseOverdueScroll"
            @mouseleave="resumeOverdueScroll"
          >
            <div
              class="list-container"
              :class="{ 'smooth-scroll': overdueBusinessList.length > 3 && !overdueResetting }"
              :style="
                overdueBusinessList.length > 3
                  ? { transform: `translateY(${overdueTransform}px)` }
                  : {}
              "
            >
              <div
                v-for="opportunity in allOverdueList"
                :key="opportunity.id"
                class="opportunity-item"
                :class="{ 'severe-overdue': opportunity.overdueDays > 10 }"
              >
                <div class="opportunity-header">
                  <div class="opportunity-info">
                    <el-link type="primary" @click="goToOpportunityDetail(opportunity)">
                      {{ opportunity.businessName }} - {{ opportunity.customerName }}
                    </el-link>
                    <p class="opportunity-status">
                      {{ stageData[opportunity.stage] }} · 已超期{{ opportunity.overdueDays }}天
                    </p>
                  </div>
                  <el-tag :type="opportunity.overdueDays > 10 ? 'danger' : 'warning'" size="small">
                    {{ opportunity.overdueDays > 10 ? '严重超期' : '超期' }}
                  </el-tag>
                </div>
                <div class="opportunity-footer">
                  <span class="last-follow">上次跟进: {{ opportunity.followTime }}</span>
                  <el-button type="primary" size="small" @click="followUpOpportunity(opportunity)">
                    立即跟进
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 商机阶段分布 -->
          <div class="stage-distribution">
            <div class="distribution-title">所有商机阶段分布</div>
            <div class="stage-grid-compact">
              <div class="stage-item primary">
                <p class="stage-label">新增</p>
                <p class="stage-value">{{ stageStatistics && stageStatistics[0].count }}</p>
              </div>
              <div class="stage-item info">
                <p class="stage-label">方案</p>
                <p class="stage-value">{{ stageStatistics && stageStatistics[1].count }}</p>
              </div>
              <div class="stage-item warning">
                <p class="stage-label">报价</p>
                <p class="stage-value">{{ stageStatistics && stageStatistics[2].count }}</p>
              </div>
              <div class="stage-item success">
                <p class="stage-label">成交</p>
                <p class="stage-value">{{ stageStatistics && stageStatistics[3].count }}</p>
              </div>
              <div class="stage-item danger">
                <p class="stage-label">失单</p>
                <p class="stage-value">{{ stageStatistics && stageStatistics[4].count }}</p>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 合同续费商机提醒 -->
        <el-card
          class="business-card renewal-opportunities-card"
          shadow="hover"
        >
          <template #header>
            <div class="card-header">
              <el-icon class="header-icon"><Refresh /></el-icon>
              <span>合同续费商机提醒</span>
            </div>
          </template>

          <div
            class="renewal-list scrollable-list"
            :class="{ 'no-scroll': renewalBusinessList.length <= 3 }"
            ref="renewalListRef"
            @wheel="handleRenewalWheel"
            @mouseenter="pauseRenewalScroll"
            @mouseleave="resumeRenewalScroll"
          >
            <div
              class="list-container"
              :class="{ 'smooth-scroll': renewalBusinessList.length > 3 && !renewalResetting }"
              :style="
                renewalBusinessList.length > 3
                  ? { transform: `translateY(${renewalTransform}px)` }
                  : {}
              "
            >
              <div
                v-for="renewal in allRenewalList"
                :key="renewal.id"
                class="renewal-item"
                :class="
                  renewal.remainingDays > 90
                    ? 'low'
                    : renewal.remainingDays > 30
                    ? 'medium'
                    : renewal.remainingDays > 0
                    ? 'high'
                    : ''
                "
              >
                <div class="renewal-header">
                  <div class="renewal-info">
                    <el-link type="primary" @click="handleRenewalClick(renewal)">
                      {{ renewal.businessName }} - {{ renewal.customerName }}
                    </el-link>
                    <p class="renewal-status">
                      续费到期: {{ renewal.remainingDays }}天后({{
                        renewal.overDate.split(' ')[0]
                      }})
                    </p>
                  </div>
                  <!-- <el-tag
                    :type="
                      renewal.intentionLevel === 'high'
                        ? 'warning'
                        : renewal.intentionLevel === 'medium'
                        ? 'info'
                        : 'primary'
                    "
                    size="small"
                  >
                    {{ renewal.intentionText }}
                  </el-tag> -->
                </div>
                <div class="renewal-footer">
                  <span class="current-fee">续费金额: ¥{{ renewal.totalValue }}</span>
                  <el-button type="primary" size="small" @click="handleToAddOffer(renewal)">
                    制定报价
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          <!-- 续费商机价值分析 -->
          <div class="renewal-value-analysis">
            <!-- <div class="analysis-title">续费商机价值</div> -->
            <div class="value-list">
              <div class="value-item" v-for="item in valueStatistics">
                <div class="value-header">
                  <span>{{ item.periodName }}</span>
                  <span>¥{{ item.totalValue }}</span>
                </div>
                <el-progress
                  :percentage="(item.totalValue / valueStatistics[0].totalValue) * 100"
                  color="#F56C6C"
                  :show-text="false"
                  :stroke-width="6"
                />
              </div>
            </div>
          </div>
        </el-card>

        <!-- 报备商机提醒 -->
        <el-card
          class="business-card report-opportunities-card"
          shadow="hover"
        >
          <template #header>
            <div class="card-header">
              <el-icon class="header-icon"><Bell /></el-icon>
              <span>报备商机提醒</span>
            </div>
          </template>

          <div
            class="report-list scrollable-list"
            :class="{ 'no-scroll': businessReportList.length <= 3 }"
            ref="reportListRef"
            @wheel="handleReportWheel"
            @mouseenter="pauseReportScroll"
            @mouseleave="resumeReportScroll"
          >
            <div
              class="list-container"
              :class="{ 'smooth-scroll': businessReportList.length > 3 && !reportResetting }"
              :style="
                businessReportList.length > 3
                  ? { transform: `translateY(${reportTransform}px)` }
                  : {}
              "
            >
              <div
                v-for="report in allReportList"
                :key="report.id"
                class="report-item"
                :class="report.priority"
              >
                <div class="report-header">
                  <div class="report-info">
                    <el-link type="primary" @click="handleReportDetail(report)">
                      {{ report.businessName }} - {{ report.manufacturer }}
                    </el-link>
                    <p class="report-status">报备到期: {{ report.reportStatus }}</p>
                  </div>
                </div>
                <div class="report-footer">
                  <span class="project-value">项目价值: ¥{{ report.projectValue }}万</span>
                  <el-button type="primary" size="small" @click="handleReportReminder(report)">
                    处理报备
                  </el-button>
                </div>
              </div>
            </div>
          </div>
          <!-- 报备商机统计分析 -->
          <div class="report-statistics">
            <div class="analysis-title">报备到期统计</div>
            <div class="statistics-list">
              <div class="statistics-item" v-for="item in reportStatistics" :key="item.periodName">
                <div class="statistics-header">
                  <span>{{ item.periodName }}</span>
                  <span>{{ item.businessCount }}个</span>
                </div>
                <el-progress
                  :percentage="65"
                  color="#F56C6C"
                  :show-text="false"
                  :stroke-width="6"
                />
              </div>
            </div>
          </div>
        </el-card>

        <!-- 客户多维度分析 -->
        <el-card class="business-card customer-analysis-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon class="header-icon"><UserFilled /></el-icon>
              <span>客户多维度分析</span>
            </div>
          </template>

          <!-- 客户行业分布图表 -->
          <div class="chart-section">
            <h3 class="chart-title">客户行业分布</h3>
            <div class="chart-container-compact" ref="industryChartRef"></div>
          </div>

          <!-- 客户销售额分析 -->
          <div class="customer-ranking-compact">
            <div style="display: flex; gap: 20px">
              <div class="ranking-section" style="flex: 1">
                <h3 class="ranking-title">客户销售额TOP5</h3>
                <div class="ranking-list">
                  <div
                    v-for="customer in customerIndustryData.salesTop5"
                    :key="customer.id"
                    class="ranking-item"
                  >
                    <div class="ranking-header">
                      <span class="customer-name">{{ customer.name }}</span>
                      <span class="customer-amount">¥{{ customer.totalPrice }}</span>
                    </div>
                    <el-progress
                      :percentage="
                        (customer.totalPrice / customerIndustryData.salesTop5[0].totalPrice) * 100
                      "
                      :show-text="false"
                      :stroke-width="6"
                    />
                  </div>
                </div>
              </div>
              <div class="ranking-section" style="flex: 1">
                <h3 class="ranking-title">客户收款额TOP5</h3>
                <div class="ranking-list">
                  <div
                    v-for="customer in customerIndustryData.collectionTop5"
                    :key="customer.id"
                    class="ranking-item"
                  >
                    <div class="ranking-header">
                      <span class="customer-name">{{ customer.name }}</span>
                      <span class="customer-amount">¥{{ customer.totalCollection }}</span>
                    </div>
                    <el-progress
                      :percentage="
                        (customer.totalCollection /
                          customerIndustryData.collectionTop5[0].totalCollection) *
                        100
                      "
                      :show-text="false"
                      :stroke-width="6"
                      status="success"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-card>

        <!-- 合同与收款 - 占两个卡片的宽度 -->
        <el-card class="business-card contract-payment-card" shadow="hover">
          <template #header>
            <div class="card-header">
              <el-icon class="header-icon"><Document /></el-icon>
              <span>合同与收款</span>
            </div>
          </template>

          <div class="contract-payment-content">
            <!-- 左侧折线图 - 70% -->
            <div class="chart-area">
              <h3 class="chart-title">合同金额与收款趋势</h3>
              <div class="chart-container-compact" ref="contractPaymentChartRef"></div>
            </div>

            <!-- 右侧合同信息 - 30% -->
            <div class="contract-info-area">
              <!-- 收款预警 -->
              <div class="contract-alerts">
                <h3 class="section-subtitle">收款预警</h3>
                <div
                  class="alert-list scrollable-list"
                  :class="{ 'no-scroll': overduePaymentsList.length <= 3 }"
                  ref="paymentListRef"
                  @mouseenter="pausePaymentScroll"
                  @mouseleave="resumePaymentScroll"
                  @wheel="handlePaymentWheel"
                >
                  <div
                    class="list-container"
                    :class="{ 'smooth-scroll': overduePaymentsList.length > 3 && !paymentResetting }"
                    :style="
                      overduePaymentsList.length > 3
                        ? { transform: `translateY(${paymentTransform}px)` }
                        : {}
                    "
                  >
                    <div
                      v-for="payment in allPaymentList"
                      :key="payment.id"
                      class="alert-item overdue-payment"
                      :class="getPaymentStatusClass(payment.planCollectionDays)"
                    >
                      <div class="alert-info">
                        <el-link type="primary" @click="toCollectionList(payment)">{{ payment.contractNumber }}</el-link>
                        <p class="customer-name">{{ payment.customerName }}</p>
                      </div>
                      <div class="alert-amount">
                        <p class="amount">¥{{ payment.amount }}</p>
                        <p class="overdue-days">{{ getPaymentStatusText(payment.planCollectionDays) }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 合同状态分布 -->
              <!-- <div class="contract-status-distribution">
                <h3 class="section-subtitle">合同状态分布</h3>
                <div class="status-grid">
                  <div class="status-item primary">
                    <p class="status-label">执行中</p>
                    <p class="status-value">{{ contractStatus.active }}</p>
                  </div>
                  <div class="status-item warning">
                    <p class="status-label">即将到期</p>
                    <p class="status-value">{{ contractStatus.expiring }}</p>
                  </div>
                  <div class="status-item info">
                    <p class="status-label">待签署</p>
                    <p class="status-value">{{ contractStatus.pending }}</p>
                  </div>
                  <div class="status-item success">
                    <p class="status-label">已完成</p>
                    <p class="status-value">{{ contractStatus.completed }}</p>
                  </div>
                </div>
              </div> -->
            </div>
          </div>
        </el-card>
      </div>
    </main>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick, onUnmounted, computed } from 'vue';
import {
  Warning,
  UserFilled,
  Refresh,
  Document,
  User,
  ArrowUp,
  ArrowDown,
  Lightning,
  Money,
  Bell,
  Odometer,
} from '@element-plus/icons-vue';
import { useRouter, useRoute } from 'vue-router';
import * as echarts from 'echarts';
import axios from 'axios';
// import func from '@/mainPage/utils/func';
const router = useRouter();
const route = useRoute();
// 响应式数据

// 预警数据
const overduePayments = ref(3);
const overdueOpportunities = ref(5);

// 核心指标数据 - API返回的数据结构
const customerStats = ref(null); // 客户数据统计
const businessStats = ref(null); // 商机数据统计
const contractStats = ref(null); // 合同数据统计
const receivedStats = ref(null); // 已收款数据统计
const receivableStats = ref(null); // 应收款数据统计

const stageData = ['新增', '方案', '报价', '成交', '失单'];

// 商机阶段分布
const stageStatistics = ref([]);

// 超期商机列表
const overdueBusinessList = ref([
  {
    id: 1,
    customerName: '未来科技',
    projectName: '企业服务升级',
    stage: '报价阶段',
    overdueDays: 15,
    lastFollowDate: '2023-06-10',
  },
  {
    id: 2,
    customerName: '云达数据',
    projectName: '系统扩容项目',
    stage: '方案阶段',
    overdueDays: 8,
    lastFollowDate: '2023-06-17',
  },
  {
    id: 3,
    customerName: '智慧云服务',
    projectName: '数字化改造',
    stage: '需求阶段',
    overdueDays: 12,
    lastFollowDate: '2023-06-13',
  },
  {
    id: 4,
    customerName: '星辰科技',
    projectName: 'AI智能平台',
    stage: '报价阶段',
    overdueDays: 20,
    lastFollowDate: '2023-06-05',
  },
  {
    id: 5,
    customerName: '数据科技',
    projectName: '大数据分析',
    stage: '方案阶段',
    overdueDays: 6,
    lastFollowDate: '2023-06-19',
  },
]);

// 客户排行数据
const topSalesCustomers = ref([
  { id: 1, name: '科技创新有限公司', amount: 248, percentage: 100 },
  { id: 2, name: '未来贸易集团', amount: 186, percentage: 75 },
  { id: 3, name: '智慧云服务公司', amount: 156, percentage: 63 },
  { id: 4, name: '星辰科技', amount: 128, percentage: 52 },
  { id: 5, name: '云达数据', amount: 98, percentage: 39 },
]);

// 续费商机数据
const renewalBusinessList = ref([
  {
    id: 1,
    customerName: '智慧云服务公司',
    serviceName: '年度服务',
    daysToExpire: 30,
    currentFee: 28.5,
    intentionLevel: 'high',
    intentionText: '高意向',
    priority: 'high',
  },
  {
    id: 2,
    customerName: '未来贸易集团',
    serviceName: '系统维护',
    daysToExpire: 60,
    currentFee: 15.2,
    intentionLevel: 'medium',
    intentionText: '中等意向',
    priority: 'medium',
  },
  {
    id: 3,
    customerName: '科技创新有限公司',
    serviceName: '技术支持',
    daysToExpire: 45,
    currentFee: 22.8,
    intentionLevel: 'low',
    intentionText: '低意向',
    priority: 'low',
  },
  {
    id: 4,
    customerName: '科技创新有限公司',
    serviceName: '技术支持',
    daysToExpire: 45,
    currentFee: 22.8,
    intentionLevel: 'low',
    intentionText: '低意向',
    priority: 'low',
  },
  {
    id: 5,
    customerName: '科技创新有限公司',
    serviceName: '技术支持',
    daysToExpire: 45,
    currentFee: 22.8,
    intentionLevel: 'low',
    intentionText: '低意向',
    priority: 'low',
  },
  {
    id: 6,
    customerName: '科技创新有限公司',
    serviceName: '技术支持',
    daysToExpire: 45,
    currentFee: 22.8,
    intentionLevel: 'low',
    intentionText: '低意向',
    priority: 'low',
  },
  {
    id: 7,
    customerName: '科技创新有限公司',
    serviceName: '技术支持',
    daysToExpire: 45,
    currentFee: 22.8,
    intentionLevel: 'low',
    intentionText: '低意向',
    priority: 'low',
  },
]);

// 报备商机数据
const businessReportList = ref([
  {
    id: 1,
    customerName: '科技创新有限公司',
    projectName: '数字化转型项目',
    daysToExpire: 12,
    projectValue: 45.8,
    urgencyLevel: 'urgent',
    urgencyText: '紧急',
    priority: 'high',
  },
  {
    id: 2,
    customerName: '星辰科技',
    projectName: '系统集成服务',
    daysToExpire: 25,
    projectValue: 32.6,
    urgencyLevel: 'warning',
    urgencyText: '提醒',
    priority: 'medium',
  },
  {
    id: 3,
    customerName: '未来科技',
    projectName: '云平台建设',
    daysToExpire: 18,
    projectValue: 38.2,
    urgencyLevel: 'warning',
    urgencyText: '提醒',
    priority: 'medium',
  },
]);

// 报备统计数据
const reportStatistics = ref([]);

// 续费价值分析
const valueStatistics = ref([]);

// 收款预警列表
const overduePaymentsList = ref([]);

// 合同状态分布
const contractStatus = reactive({
  active: 156,
  expiring: 24,
  pending: 18,
  completed: 342,
});
// 客户多维度数据
const customerIndustryData = ref({});
// 合同金额与收款数据
const contractPaymentData = ref([]);
// 图表引用
const industryChartRef = ref(null);
const contractPaymentChartRef = ref(null);

// 滚动列表引用
const overdueListRef = ref(null);
const renewalListRef = ref(null);
const reportListRef = ref(null);
const paymentListRef = ref(null);

// 滚动播放相关变量
const overdueCurrentIndex = ref(0);
const renewalCurrentIndex = ref(0);
const reportCurrentIndex = ref(0);
const paymentCurrentIndex = ref(0);

// 滚动位移变量
const overdueTransform = ref(0);
const renewalTransform = ref(0);
const reportTransform = ref(0);
const paymentTransform = ref(0);

// 重置状态变量（用于控制transition）
const overdueResetting = ref(false);
const renewalResetting = ref(false);
const reportResetting = ref(false);
const paymentResetting = ref(false);

// 每个项目的高度（包括间距）
const itemHeight = 95; // 74px项目高度 + 6px间距

// 定时器
let overdueTimer = null;
let renewalTimer = null;
let reportTimer = null;
let paymentTimer = null;

// 用于平滑滚动的完整数据列表（计算属性）
const allOverdueList = computed(() => {
  if (overdueBusinessList.value.length <= 3) {
    // 数据少于等于3条时，只显示原始数据，不滚动
    return overdueBusinessList.value.slice(0, 3);
  }
  // 创建一个包含重复数据的列表，用于无缝滚动
  return [...overdueBusinessList.value, ...overdueBusinessList.value];
});

const allRenewalList = computed(() => {
  if (renewalBusinessList.value.length <= 3) {
    return renewalBusinessList.value.slice(0, 3);
  }
  return [...renewalBusinessList.value, ...renewalBusinessList.value];
});

const allReportList = computed(() => {
  if (businessReportList.value.length <= 3) {
    return businessReportList.value.slice(0, 3);
  }
  return [...businessReportList.value, ...businessReportList.value];
});

const allPaymentList = computed(() => {
  if (overduePaymentsList.value.length <= 3) {
    return overduePaymentsList.value.slice(0, 3);
  }
  return [...overduePaymentsList.value, ...overduePaymentsList.value];
});

// 方法

const viewAllAlerts = () => {
  console.log('查看全部提醒');
};

// 获取收款预警状态样式类
const getPaymentStatusClass = (planCollectionDays) => {
  if (planCollectionDays > 7) {
    return 'payment-success';
  } else if (planCollectionDays >= 0 && planCollectionDays <= 7) {
    return 'payment-warning';
  } else {
    return 'payment-danger';
  }
};

// 获取收款预警状态文本
const getPaymentStatusText = (planCollectionDays) => {
  if (planCollectionDays > 7) {
    return `还有${planCollectionDays}天到期`;
  } else if (planCollectionDays >= 0 && planCollectionDays <= 7) {
    return `${planCollectionDays}天内到期`;
  } else {
    return `逾期${Math.abs(planCollectionDays)}天`;
  }
};

const followUpOpportunity = opportunity => {
  router.push({
    path: '/CRM/follow/compoents/update',
    query: {
      customerId: opportunity.customerId,
      logicId: opportunity.id,
      type: '1',
    },
  });
};

const handleToAddOffer = renewal => {
  router.push({
    path: '/CRM/quotation/compoents/addVersion3',
    query: {
      businessId: renewal.id,
      type: 'add',
    },
  });
};

const handleReportReminder = report => {
  handleReportDetail(report);
};

// 滚动播放函数
const startScrolling = () => {
  // 超期商机滚动
  if (overdueBusinessList.value.length > 3) {
    overdueTimer = setInterval(() => {
      overdueCurrentIndex.value++;
      overdueTransform.value = -(overdueCurrentIndex.value * itemHeight);

      // 当滚动到原始列表的末尾时，无缝重置到开头继续滚动
      if (overdueCurrentIndex.value >= overdueBusinessList.value.length) {
        // 等待当前滚动动画完成
        setTimeout(() => {
          // 禁用transition
          overdueResetting.value = true;
          // 重置到开头
          overdueCurrentIndex.value = 0;
          overdueTransform.value = 0;
          // 下一帧重新启用transition
          nextTick(() => {
            setTimeout(() => {
              overdueResetting.value = false;
            }, 50);
          });
        }, 800);
      }
    }, 5000); // 每5秒滚动一次（从3秒改为5秒）
  }

  // 续费商机滚动
  if (renewalBusinessList.value.length > 3) {
    renewalTimer = setInterval(() => {
      renewalCurrentIndex.value++;
      renewalTransform.value = -(renewalCurrentIndex.value * itemHeight);

      if (renewalCurrentIndex.value >= renewalBusinessList.value.length) {
        setTimeout(() => {
          renewalResetting.value = true;
          renewalCurrentIndex.value = 0;
          renewalTransform.value = 0;
          nextTick(() => {
            setTimeout(() => {
              renewalResetting.value = false;
            }, 50);
          });
        }, 800);
      }
    }, 5500); // 每5.5秒滚动一次（从3.5秒改为5.5秒）
  }

  // 报备商机滚动
  if (businessReportList.value.length > 3) {
    reportTimer = setInterval(() => {
      reportCurrentIndex.value++;
      reportTransform.value = -(reportCurrentIndex.value * itemHeight);

      if (reportCurrentIndex.value >= businessReportList.value.length) {
        setTimeout(() => {
          reportResetting.value = true;
          reportCurrentIndex.value = 0;
          reportTransform.value = 0;
          nextTick(() => {
            setTimeout(() => {
              reportResetting.value = false;
            }, 50);
          });
        }, 800);
      }
    }, 6000); // 每6秒滚动一次（从4秒改为6秒）
  }

  // 收款预警滚动
  if (overduePaymentsList.value.length > 3) {
    paymentTimer = setInterval(() => {
      paymentCurrentIndex.value++;
      paymentTransform.value = -(paymentCurrentIndex.value * itemHeight);

      if (paymentCurrentIndex.value >= overduePaymentsList.value.length) {
        setTimeout(() => {
          paymentResetting.value = true;
          paymentCurrentIndex.value = 0;
          paymentTransform.value = 0;
          nextTick(() => {
            setTimeout(() => {
              paymentResetting.value = false;
            }, 50);
          });
        }, 800);
      }
    }, 6500); // 每6.5秒滚动一次（从4.5秒改为6.5秒）
  }
};

const stopScrolling = () => {
  if (overdueTimer) {
    clearInterval(overdueTimer);
    overdueTimer = null;
  }
  if (renewalTimer) {
    clearInterval(renewalTimer);
    renewalTimer = null;
  }
  if (reportTimer) {
    clearInterval(reportTimer);
    reportTimer = null;
  }
  if (paymentTimer) {
    clearInterval(paymentTimer);
    paymentTimer = null;
  }
};

// 暂停和恢复滚动的函数
const pauseOverdueScroll = () => {
  if (overdueTimer) {
    clearInterval(overdueTimer);
    overdueTimer = null;
  }
};

const resumeOverdueScroll = () => {
  if (!overdueTimer && overdueBusinessList.value.length > 3) {
    overdueTimer = setInterval(() => {
      overdueCurrentIndex.value++;
      overdueTransform.value = -(overdueCurrentIndex.value * itemHeight);

      if (overdueCurrentIndex.value >= overdueBusinessList.value.length) {
        setTimeout(() => {
          overdueResetting.value = true;
          overdueCurrentIndex.value = 0;
          overdueTransform.value = 0;
          nextTick(() => {
            setTimeout(() => {
              overdueResetting.value = false;
            }, 50);
          });
        }, 800);
      }
    }, 5000); // 更新为5秒
  }
};

const pauseRenewalScroll = () => {
  if (renewalTimer) {
    clearInterval(renewalTimer);
    renewalTimer = null;
  }
};

const resumeRenewalScroll = () => {
  if (!renewalTimer && renewalBusinessList.value.length > 3) {
    renewalTimer = setInterval(() => {
      renewalCurrentIndex.value++;
      renewalTransform.value = -(renewalCurrentIndex.value * itemHeight);

      if (renewalCurrentIndex.value >= renewalBusinessList.value.length) {
        setTimeout(() => {
          renewalResetting.value = true;
          renewalCurrentIndex.value = 0;
          renewalTransform.value = 0;
          nextTick(() => {
            setTimeout(() => {
              renewalResetting.value = false;
            }, 50);
          });
        }, 800);
      }
    }, 5500); // 更新为5.5秒
  }
};

const pausePaymentScroll = () => {
  if (paymentTimer) {
    clearInterval(paymentTimer);
    paymentTimer = null;
  }
};

const resumePaymentScroll = () => {
  if (overduePaymentsList.value.length > 3 && !paymentTimer) {
    paymentTimer = setInterval(() => {
      paymentCurrentIndex.value++;
      paymentTransform.value = -(paymentCurrentIndex.value * itemHeight);

      if (paymentCurrentIndex.value >= overduePaymentsList.value.length) {
        setTimeout(() => {
          paymentResetting.value = true;
          paymentCurrentIndex.value = 0;
          paymentTransform.value = 0;
          nextTick(() => {
            setTimeout(() => {
              paymentResetting.value = false;
            }, 50);
          });
        }, 800);
      }
    }, 6500); // 更新为6.5秒
  }
};

const pauseReportScroll = () => {
  if (reportTimer) {
    clearInterval(reportTimer);
    reportTimer = null;
  }
};

const resumeReportScroll = () => {
  if (!reportTimer && businessReportList.value.length > 3) {
    reportTimer = setInterval(() => {
      reportCurrentIndex.value++;
      reportTransform.value = -(reportCurrentIndex.value * itemHeight);

      if (reportCurrentIndex.value >= businessReportList.value.length) {
        setTimeout(() => {
          reportResetting.value = true;
          reportCurrentIndex.value = 0;
          reportTransform.value = 0;
          nextTick(() => {
            setTimeout(() => {
              reportResetting.value = false;
            }, 50);
          });
        }, 800);
      }
    }, 6000); // 更新为6秒
  }
};

// 滚轮事件处理函数
const handleOverdueWheel = (event) => {
  event.preventDefault();
  if (overdueBusinessList.value.length <= 3) return;

  const delta = event.deltaY > 0 ? 1 : -1;
  let newIndex = overdueCurrentIndex.value + delta;

  // 边界处理
  if (newIndex < 0) {
    newIndex = overdueBusinessList.value.length - 1;
  } else if (newIndex >= overdueBusinessList.value.length) {
    newIndex = 0;
  }

  overdueCurrentIndex.value = newIndex;
  overdueTransform.value = -(newIndex * itemHeight);
};

const handleRenewalWheel = (event) => {
  event.preventDefault();
  if (renewalBusinessList.value.length <= 3) return;

  const delta = event.deltaY > 0 ? 1 : -1;
  let newIndex = renewalCurrentIndex.value + delta;

  // 边界处理
  if (newIndex < 0) {
    newIndex = renewalBusinessList.value.length - 1;
  } else if (newIndex >= renewalBusinessList.value.length) {
    newIndex = 0;
  }

  renewalCurrentIndex.value = newIndex;
  renewalTransform.value = -(newIndex * itemHeight);
};

const handleReportWheel = (event) => {
  event.preventDefault();
  if (businessReportList.value.length <= 3) return;

  const delta = event.deltaY > 0 ? 1 : -1;
  let newIndex = reportCurrentIndex.value + delta;

  // 边界处理
  if (newIndex < 0) {
    newIndex = businessReportList.value.length - 1;
  } else if (newIndex >= businessReportList.value.length) {
    newIndex = 0;
  }

  reportCurrentIndex.value = newIndex;
  reportTransform.value = -(newIndex * itemHeight);
};

const handlePaymentWheel = (event) => {
  event.preventDefault();
  if (overduePaymentsList.value.length <= 3) return;

  const delta = event.deltaY > 0 ? 1 : -1;
  let newIndex = paymentCurrentIndex.value + delta;

  // 边界处理
  if (newIndex < 0) {
    newIndex = overduePaymentsList.value.length - 1;
  } else if (newIndex >= overduePaymentsList.value.length) {
    newIndex = 0;
  }

  paymentCurrentIndex.value = newIndex;
  paymentTransform.value = -(newIndex * itemHeight);
};

// 初始化客户行业分布图表
const initIndustryChart = () => {
  nextTick(() => {
    if (industryChartRef.value && customerIndustryData.value.industryDistribution) {
      const industryChart = echarts.init(industryChartRef.value);
      const industryOption = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} 家 ({d}%)',
        },
        legend: {
          orient: 'vertical',
          right: 10,
          top: 20,
          bottom: 20,
          itemWidth: 12,
          itemHeight: 12,
          textStyle: {
            fontSize: 11,
          },
        },
        series: [
          {
            name: '客户行业分布',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['40%', '50%'],
            data: customerIndustryData.value.industryDistribution.map(item => {
              return {
                name: item.industryName,
                value: item.customerCount,
              };
            }),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      };
      industryChart.setOption(industryOption);
    }
  });
};

// 初始化合同与收款趋势图
const initContractPaymentChart = () => {
  nextTick(() => {
    console.log('开始初始化合同收款图表...');
    console.log('图表容器元素:', contractPaymentChartRef.value);
    console.log('图表数据:', contractPaymentData.value);

    if (contractPaymentChartRef.value) {
      try {
        const contractChart = echarts.init(contractPaymentChartRef.value);

        // 获取各系列数据
        const contractData = contractPaymentData.value.find(item => item.name == '合同金额')?.data || [];
        const receivedData = contractPaymentData.value.find(item => item.name == '收款额')?.data || [];
        const receivableData = contractPaymentData.value.find(item => item.name == '对应收款额')?.data || [];

        console.log('合同金额数据:', contractData);
        console.log('收款额数据:', receivedData);
        console.log('对应收款额数据:', receivableData);

        const contractOption = {
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
            },
          },
          legend: {
            data: ['合同金额', '收款额', '对应收款额'],
            top: 10,
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '15%',
            containLabel: true,
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: [
              '1月',
              '2月',
              '3月',
              '4月',
              '5月',
              '6月',
              '7月',
              '8月',
              '9月',
              '10月',
              '11月',
              '12月',
            ],
          },
          yAxis: {
            type: 'value',
            axisLabel: {
              formatter: '{value}万',
            },
          },
          series: [
            {
              name: '合同金额',
              type: 'line',
              smooth: true,
              symbol: 'circle',
              symbolSize: 6,
              lineStyle: {
                width: 3,
              },
              areaStyle: {
                opacity: 0.1,
              },
              data: contractData,
              itemStyle: {
                color: '#165DFF',
              },
            },
            {
              name: '收款额',
              type: 'line',
              smooth: true,
              symbol: 'circle',
              symbolSize: 6,
              lineStyle: {
                width: 3,
              },
              areaStyle: {
                opacity: 0.1,
              },
              data: receivedData,
              itemStyle: {
                color: '#36CFC9',
              },
            },
            {
              name: '对应收款额',
              type: 'line',
              smooth: true,
              symbol: 'circle',
              symbolSize: 6,
              lineStyle: {
                width: 3,
              },
              areaStyle: {
                opacity: 0.1,
              },
              data: receivableData,
              itemStyle: {
                color: '#F56C6C',
              },
            },
          ],
        };

        console.log('图表配置:', contractOption);
        contractChart.setOption(contractOption);
        console.log('图表初始化完成');
      } catch (error) {
        console.error('图表初始化失败:', error);
      }
    } else {
      console.error('图表容器元素未找到');
    }
  });
};

// 生命周期
onMounted(() => {
  getDetail();

  // 延迟启动滚动播放，确保数据加载完成
  setTimeout(() => {
    startScrolling();
  }, 2000);
});

// 组件卸载时清理定时器
onUnmounted(() => {
  stopScrolling();
});

function goToOpportunityDetail(item) {
  router.push({
    path: '/CRM/businessOpportunity/myBusinessOpportunity',
    query: {
      name: item.businessName,
    },
  });
}
function handleRenewalClick(item) {
  router.push({
    path: '/CRM/businessOpportunity/renewBusinessOpportunity',
    query: {
      name: item.businessName,
    },
  });
}
function handleReportDetail(item) {
  router.push({
    path: '/CRM/reporting/myReport',
    query: {
      businessOpportunityName: item.businessName,
    },
  });
}
function handleReportDetail(item) {
   router.push({
      path: '/Finance/Collection/collectionPlanMy',
      query: {
        ids: row.relationIds,
      },
    });
}

function getDetail() {
  // 获取核心业务指标数据
  getCoreBusiness();
  // 获取超期未跟进数据
  getOverdue();
  // 获取续费商机数据
  getRenewal();
  // 获取报备到期数据
  getReportExpire();
  // 获取客户行业统计数据
  getIndustry();
  // 获取合同与收款数据
  getContractPayment();
  // 获取收款预警数据
  getPaymentAlerts();
}
// 获取核心业务指标数据
async function getCoreBusiness() {
  try {
    // 这里需要替换为实际的API调用
    // 示例：假设有一个统一的API返回所有核心指标数据
    const response = await axios.get('/api/vt-admin/statistics/workbench_core_index', {
      params: {
        type: 0,
      },
    });
    const data = response.data.data;

    // 根据API返回的数据结构进行赋值
    customerStats.value = data.customerStats;
    businessStats.value = data.businessStats;
    contractStats.value = data.contractStats;
    receivedStats.value = data.receivedStats;
    receivableStats.value = data.receivableStats;
  } catch (error) {
    console.error('获取核心指标数据失败:', error);
    // 可以在这里设置默认值或显示错误提示
  }
}
async function getOverdue() {
  try {
    axios
      .get('/api/vt-admin/statistics/overdueBusinessStatistics', {
        params: {
          type: 0,
        },
      })
      .then(res => {
        overdueBusinessList.value = res.data.data.overdueBusinessList;
        stageStatistics.value = res.data.data.stageStatistics;
      });
  } catch (error) {
    console.error('获取逾期数据失败:', error);
    // 可以在这里设置默认值或显示错误提示
  }
}
async function getReportExpire() {
  try {
    axios
      .get('/api/vt-admin/statistics/businessReportRemind', {
        params: {
          type: 0,
        },
      })
      .then(res => {
        businessReportList.value = res.data.data.businessReportList;
        reportStatistics.value = res.data.data.reportStatistics;
      });
  } catch (error) {
    console.error('获取逾期数据失败:', error);
    // 可以在这里设置默认值或显示错误提示
  }
}
async function getIndustry() {
  try {
    axios
      .get('/api/vt-admin/statistics/customerMultiDimensionAnalysis', {
        params: {
          type: 0,
        },
      })
      .then(res => {
        customerIndustryData.value = res.data.data;
        initIndustryChart();
      });
  } catch (error) {
    console.error('获取客户行业数据失败:', error);
    // 可以在这里设置默认值或显示错误提示
  }
}
async function getRenewal() {
  try {
    axios
      .get('/api/vt-admin/statistics/renewalBusinessRemind', {
        params: {
          type: 0,
        },
      })
      .then(res => {
        renewalBusinessList.value = res.data.data.renewalBusinessList;
        valueStatistics.value = res.data.data.valueStatistics.reverse();
      });
  } catch (error) {
    console.error('获取逾期数据失败:', error);
    // 可以在这里设置默认值或显示错误提示
  }
}
async function getContractPayment() {
  try {
    console.log('开始获取合同收款数据...');
    const results = await Promise.allSettled([getAllAmount(), getRefundAmount(), getRefundAmountBySignDate()]);
    const data = ['合同金额', '收款额', '对应收款额'];

    console.log('API请求结果:', results);

    contractPaymentData.value = results.map((item, index) => {
      // 检查请求是否成功
      if (item.status === 'fulfilled' && item.value?.data?.data?.y) {
        console.log(`${data[index]}数据:`, item.value.data.data.y);
        return {
          name: data[index],
          data: item.value.data.data.y,
        };
      } else {
       
      }
    });

    console.log('最终合同收款数据:', contractPaymentData.value);

    // 数据获取完成后初始化合同收款图表
    initContractPaymentChart();
  } catch (error) {
   
    initContractPaymentChart();
  }
}
function getAllAmount() {
  return axios.get('/api/vt-admin/statistics/contractAmountStatistics', {
    params: {
      year: new Date().getFullYear(),
      selectType: 1,
    },
  });
}

function getRefundAmount() {
  return axios.get('/api/vt-admin/statistics/refundAmountStatistics', {
    params: {
      year: new Date().getFullYear(),
      selectType: 1,
    },
  });
}

function getRefundAmountBySignDate() {
  return axios.get('/api/vt-admin/statistics/refundAmountBySignDateStatistics', {
    params: {
      year: new Date().getFullYear(),
      selectType: 1,
    },
  });
}

// 获取收款预警数据
async function getPaymentAlerts() {
  try {
    console.log('开始获取收款预警数据...');
    const response = await axios.get('/api/vt-admin/sealContractPlanCollection/page', {
      params: {
        current: 1,
        size: 20, // 获取前20条数据用于滚动显示
      },
    });

    if (response.data && response.data.data && response.data.data.records) {
      overduePaymentsList.value = response.data.data.records.map(item => ({
        id: item.id,
        contractNumber: item.contractName || item.contractNo || '未知合同',
        customerName: item.customerName || item.companyName || '未知客户',
        amount: item.planCollectionPrice || item.planCollectionAmount || 0,
        planCollectionDays: item.planCollectionDays || 0,
      }));

      console.log('收款预警数据获取成功:', overduePaymentsList.value);

      // 数据获取完成后启动滚动
      nextTick(() => {
        startScrolling();
      });
    } else {
      console.warn('收款预警数据格式异常:', response.data);
      overduePaymentsList.value = [];
    }
  } catch (error) {
    console.error('获取收款预警数据失败:', error);
    // 设置测试数据
    overduePaymentsList.value = [
      {
        id: 1,
        contractNumber: 'HT-2023-001',
        customerName: '测试客户A',
        amount: 15.6,
        planCollectionDays: 10,
      },
      {
        id: 2,
        contractNumber: 'HT-2023-002',
        customerName: '测试客户B',
        amount: 28.9,
        planCollectionDays: 3,
      },
      {
        id: 3,
        contractNumber: 'HT-2023-003',
        customerName: '测试客户C',
        amount: 42.3,
        planCollectionDays: -5,
      },
      {
        id: 4,
        contractNumber: 'HT-2023-004',
        customerName: '测试客户D',
        amount: 19.7,
        planCollectionDays: 0,
      },
    ];
    console.log('使用测试数据:', overduePaymentsList.value);

    // 启动滚动
    nextTick(() => {
      startScrolling();
    });
  }
}
</script>

<style scoped>
.sales-workbench {
  min-height: calc(100vh - 120px);
  background-color: #f5f7fa;
  position: relative;
}

/* 顶部导航栏 */
.workbench-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: -10px -6px 0 -6px;
}

.header-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  font-size: 24px;
  color: #165dff;
}

.header-title {
  font-size: 18px;
  font-weight: 600;
  color: #1d2129;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notification-badge {
  position: relative;
}

.notification-btn {
  color: #f56c6c;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.username {
  font-weight: 500;
  color: #1d2129;
}

/* 快捷操作栏 */
.quick-actions {
  position: sticky;
  top: 64px;
  z-index: 99;
  background: white;
  border-bottom: 1px solid #e5e6eb;
  padding: 12px 0;
  margin: 0 -6px 24px -6px;
  display: flex;
  align-items: center;
  gap: 12px;
  padding-left: 16px;
  padding-right: 16px;
}

.urgent-alerts {
  margin-left: auto;
}

/* 主体内容 */
.main-content {
  padding: 0 16px 32px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  flex-wrap: wrap;
  gap: 16px;
}

.page-title-section h1 {
  font-size: clamp(1.5rem, 3vw, 2.5rem);
  font-weight: bold;
  color: #1d2129;
  margin: 0;
}

.page-subtitle {
  color: #4e5969;
  margin-top: 4px;
}

.time-filter-group {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

/* 预警提示 */
.alert-section {
  margin-bottom: 24px;
}

.urgent-alert {
  border-left: 4px solid #f56c6c;
}

.alert-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.alert-icon {
  font-size: 20px;
  color: #f56c6c;
}

/* 核心指标卡片 */
.metrics-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1d2129;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

/* 新的指标卡片样式 */
.metric-card-new {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
  border-left: 4px solid #4a90e2;
  position: relative;
  min-height: 110px;
}

.metric-card-new:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 应用原来的边框颜色 */
.metric-card-new.primary-border {
  border-left-color: #165dff;
}

.metric-card-new.warning-border {
  border-left-color: #faad14;
}

.metric-card-new.success-border {
  border-left-color: #52c41a;
}

.metric-card-new.info-border {
  border-left-color: #40a9ff;
}

.metric-card-new.danger-border {
  border-left-color: #f56c6c;
}

.metric-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.metric-title-section {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.metric-title {
  font-size: 13px;
  color: #4e5969;
  font-weight: 600;
  margin-bottom: 0;
}

.metric-summary {
  font-size: 11px;
  color: #86909c;
  font-weight: 500;
  background: rgba(134, 144, 156, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
  width: fit-content;
}

.metric-icon-new {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.metric-icon-new.primary {
  background: rgba(22, 93, 255, 0.1);
  color: #165dff;
}

.metric-icon-new.warning {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.metric-icon-new.success {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.metric-icon-new.info {
  background: rgba(64, 169, 255, 0.1);
  color: #40a9ff;
}

.metric-icon-new.danger {
  background: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
}

.metric-value-new {
  font-size: 24px;
  font-weight: 700;
  color: #1d2129;
  margin-bottom: 6px;
  line-height: 1.2;
}

.metric-growth-new {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.metric-growth-new.positive {
  color: #52c41a;
}

.metric-growth-new.negative {
  color: #f56c6c;
}

.metric-growth-new.warning {
  color: #faad14;
}

.metric-growth-new .el-icon {
  font-size: 12px;
}

/* 保留旧样式以防其他地方使用 */
.metric-card {
  background: white;
  border-radius: 12px;
  padding: 18px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border-left: 4px solid transparent;
  min-height: 130px;
}

.metric-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
}

.primary-border {
  border-left-color: #165dff;
}

.warning-border {
  border-left-color: #faad14;
}

.success-border {
  border-left-color: #52c41a;
}

.info-border {
  border-left-color: #40a9ff;
}

.danger-border {
  border-left-color: #f56c6c;
}

.metric-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  height: 100%;
}

.metric-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.metric-label {
  color: #4e5969;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.metric-value {
  font-size: 32px;
  font-weight: bold;
  color: #1d2129;
  margin: 8px 0;
}

.metric-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  margin: 0;
}

.metric-trend.success {
  color: #52c41a;
}

.metric-trend.danger {
  color: #f56c6c;
}

.trend-label {
  color: #4e5969;
}

.overdue-tag {
  margin-left: 8px;
}

.metric-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 22px;
}

.metric-icon.primary {
  background: rgba(22, 93, 255, 0.1);
  color: #165dff;
}

.metric-icon.warning {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.metric-icon.success {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.metric-icon.info {
  background: rgba(64, 169, 255, 0.1);
  color: #40a9ff;
}

.metric-icon.danger {
  background: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
}

/* 时间维度数据样式 */
.metric-periods {
  margin-top: 0;
  display: flex;
  gap: 12px;
  justify-content: space-between;
}

/* 两列布局样式 */
.metric-periods-two {
  gap: 16px;
}

.metric-periods-two .period-item {
  flex: 1;
  padding: 10px 8px;
}

.period-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-width: 0;
  padding: 8px 6px;
  background: #fafbfc;
  border-radius: 6px;
  border: 1px solid #e5e6eb;
}

.period-label {
  font-size: 11px;
  color: #86909c;
  margin-bottom: 4px;
  font-weight: 500;
}

.period-value {
  font-size: 14px;
  font-weight: 700;
  color: #1d2129;
  margin-bottom: 3px;
  line-height: 1;
}

.period-growth {
  font-size: 10px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 2px;
}

.period-growth.positive {
  color: #52c41a;
}

.period-growth.negative {
  color: #f56c6c;
}

.period-growth .el-icon {
  font-size: 10px;
}

/* 单值显示样式 */
.metric-single-value {
  margin-top: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.single-value-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e5e6eb;
}

.single-value-label {
  font-size: 12px;
  color: #86909c;
  margin-bottom: 6px;
  font-weight: 500;
}

.single-value-amount {
  font-size: 20px;
  font-weight: 700;
  color: #1d2129;
  margin-bottom: 8px;
  line-height: 1;
}

.receivable-breakdown {
  display: flex;
  flex-direction: column;
  gap: 4px;
  width: 100%;
}

.breakdown-item {
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  padding: 3px 6px;
  border-radius: 4px;
}

.breakdown-item.overdue {
  color: #f56c6c;
  background: rgba(245, 108, 108, 0.1);
}

.breakdown-item.pending {
  color: #faad14;
  background: rgba(250, 173, 20, 0.1);
}

/* 业务卡片网格布局 */
.business-cards-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  margin-bottom: 32px;
}

/* 合同与收款卡片占两个卡片宽度 */
.contract-payment-card {
  grid-column: span 2;
  min-height: 300px;
}

@media (max-width: 1200px) {
  .business-cards-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .contract-payment-card {
    grid-column: span 2;
  }
}

@media (max-width: 768px) {
  .business-cards-grid {
    grid-template-columns: 1fr;
  }

  .contract-payment-card {
    grid-column: span 1;
  }
}

.contract-payment-content {
  display: flex;
  gap: 16px;
  height: 100%;
}

/* 左侧图表区域 - 70% */
.chart-area {
  flex: 0 0 65%;
  display: flex;
  flex-direction: column;
}

/* 右侧合同信息区域 - 30% */
.contract-info-area {
  flex: 0 0 35%;
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-left: 16px;
  box-sizing: border-box;
  border-left: 1px solid #e5e6eb;
}

.section-subtitle {
  font-size: 13px;
  font-weight: 600;
  color: #1d2129;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.section-subtitle::before {
  content: '';
  width: 3px;
  height: 12px;
  background: #165dff;
  border-radius: 2px;
}

/* 合同预警样式 */
.contract-alerts {
  flex-shrink: 0;
}

.contract-alerts .alert-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 0;
}

.contract-alerts .alert-item {
  padding: 8px;
  border-radius: 6px;
  border: 1px solid rgba(245, 108, 108, 0.2);
  background: rgba(245, 108, 108, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 收款预警状态样式 */
.contract-alerts .alert-item.payment-success {
  border: 1px solid rgba(22, 93, 255, 0.2);
  background: rgba(22, 93, 255, 0.05);
}

.contract-alerts .alert-item.payment-success .amount,
.contract-alerts .alert-item.payment-success .overdue-days {
  color: #165dff;
}

.contract-alerts .alert-item.payment-warning {
  border: 1px solid rgba(255, 125, 0, 0.2);
  background: rgba(255, 125, 0, 0.05);
}

.contract-alerts .alert-item.payment-warning .amount,
.contract-alerts .alert-item.payment-warning .overdue-days {
  color: #ff7d00;
}

.contract-alerts .alert-item.payment-danger {
  border: 1px solid rgba(245, 108, 108, 0.2);
  background: rgba(245, 108, 108, 0.05);
}

.contract-alerts .alert-item.payment-danger .amount,
.contract-alerts .alert-item.payment-danger .overdue-days {
  color: #f56c6c;
}

.alert-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.contract-number {
  font-weight: 500;
  font-size: 12px;
  color: #1d2129;
  margin: 0;
}

.customer-name {
  font-size: 11px;
  color: #4e5969;
  margin: 0;
}

.alert-amount {
  text-align: right;
}

.amount {
  font-weight: 500;
  font-size: 12px;
  color: #f56c6c;
  margin: 0;
}

.overdue-days {
  font-size: 10px;
  color: #f56c6c;
  margin: 0;
}

/* 合同状态分布 */
.contract-status-distribution {
  flex: 1;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 6px;
}

.status-item {
  padding: 8px;
  border-radius: 6px;
  border: 1px solid;
  text-align: center;
}

.status-item.primary {
  background: rgba(22, 93, 255, 0.05);
  border-color: rgba(22, 93, 255, 0.2);
}

.status-item.warning {
  background: rgba(250, 173, 20, 0.05);
  border-color: rgba(250, 173, 20, 0.2);
}

.status-item.info {
  background: rgba(64, 169, 255, 0.05);
  border-color: rgba(64, 169, 255, 0.2);
}

.status-item.success {
  background: rgba(82, 196, 26, 0.05);
  border-color: rgba(82, 196, 26, 0.2);
}
.status-item.danger {
  background: #f56c6c;
  border-color: #f56c6c;
}

.status-label {
  font-size: 11px;
  color: #4e5969;
  margin: 0 0 3px 0;
}

.status-value {
  font-size: 16px;
  font-weight: bold;
  margin: 0;
}

.status-item.primary .status-value {
  color: #165dff;
}

.status-item.warning .status-value {
  color: #faad14;
}

.status-item.info .status-value {
  color: #40a9ff;
}

.status-item.success .status-value {
  color: #52c41a;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .contract-payment-content {
    flex-direction: column;
    gap: 12px;
  }

  .chart-area {
    flex: none;
  }

  .contract-info-area {
    flex: none;
    padding-left: 0;
    border-left: none;
    border-top: 1px solid #e5e6eb;
    padding-top: 12px;
  }

  .status-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .contract-payment-content {
    gap: 8px;
  }

  .status-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* 业务卡片样式 */
.business-card {
  height: auto;
  min-height: 380px;
  max-height: 470px;
  overflow: hidden;
}

.business-card .el-card__body {
  padding: 12px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 卡片通用样式 */
.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #1d2129;
}

.header-icon {
  color: #165dff;
}

/* 滚动列表通用样式 */
.scrollable-list {
  overflow: hidden;
  position: relative;
  max-height: 280px; /* 最大高度，显示3个项目 */
  min-height: 80px; /* 最小高度，至少显示1个项目 */
  cursor: grab; /* 添加抓手光标，提示用户可以交互 */
}

.scrollable-list:active {
  cursor: grabbing; /* 交互时显示抓取状态 */
}

/* 为滚动列表添加悬停提示 */
.scrollable-list:hover::after {
  content: '使用滚轮滚动';
  position: absolute;
  top: 5px;
  right: 5px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  pointer-events: none;
  z-index: 10;
  opacity: 0.8;
}

.scrollable-list .list-container {
  display: flex;
  flex-direction: column;
  gap: 6px;
  will-change: transform;
}

.scrollable-list .list-container.smooth-scroll {
  transition: transform 0.8s ease-in-out;
}

.scrollable-list .opportunity-item,
.scrollable-list .renewal-item,
.scrollable-list .report-item {
  flex-shrink: 0;
  min-height: 74px; /* 最小高度，保持布局一致 */
  opacity: 1;
  transform: translateY(0);
}

/* 当数据少于等于3条时，使用自动高度 */
.scrollable-list.no-scroll {
  height: auto;
  max-height: none;
}

.scrollable-list.no-scroll .opportunity-item,
.scrollable-list.no-scroll .renewal-item,
.scrollable-list.no-scroll .report-item {
  height: auto;
  min-height: auto;
}

/* 超期商机卡片 */
.opportunity-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.opportunity-item {
  padding: 8px;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  background: #fafbfc;
  transition: all 0.3s ease;
}

.opportunity-item.severe-overdue {
  border-color: rgba(245, 108, 108, 0.3);
  background: rgba(245, 108, 108, 0.05);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0.4);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(245, 108, 108, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 108, 108, 0);
  }
}

.opportunity-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6px;
}

.opportunity-title {
  font-weight: 500;
  font-size: 14px;
  color: #1d2129;
  margin: 0;
  line-height: 1.3;
}

.opportunity-status {
  font-size: 12px;
  color: #f56c6c;
  margin: 2px 0 0 0;
}

.opportunity-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.last-follow {
  font-size: 12px;
  color: #4e5969;
}

/* 商机阶段分布 */
.stage-distribution {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e5e6eb;
  flex-shrink: 0;
}

.distribution-title {
  font-size: 14px;
  font-weight: 500;
  color: #4e5969;
  margin-bottom: 5px;
}

.stage-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.stage-grid-compact {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 6px;
}

.stage-grid-compact .stage-item {
  padding: 8px;
}

.stage-grid-compact .stage-label {
  font-size: 11px;
}

.stage-grid-compact .stage-value {
  font-size: 16px;
  margin-top: 2px;
}

.stage-item {
  padding: 12px;
  border-radius: 8px;
  border: 1px solid;
}

.stage-item.primary {
  background: rgba(22, 93, 255, 0.05);
  border-color: rgba(22, 93, 255, 0.2);
}

.stage-item.info {
  background: rgba(64, 169, 255, 0.05);
  border-color: rgba(64, 169, 255, 0.2);
}

.stage-item.warning {
  background: rgba(250, 173, 20, 0.05);
  border-color: rgba(250, 173, 20, 0.2);
}

.stage-item.success {
  background: rgba(82, 196, 26, 0.05);
  border-color: rgba(82, 196, 26, 0.2);
}
.stage-item.danger {
  background: rgba(245, 108, 108, 0.05);
  border-color: rgba(245, 108, 108, 0.2);
}

.stage-label {
  font-size: 12px;
  color: #4e5969;
  margin: 0;
}

.stage-value {
  font-size: 20px;
  font-weight: bold;
  margin: 4px 0 0 0;
}

.stage-item.primary .stage-value {
  color: #165dff;
}

.stage-item.info .stage-value {
  color: #40a9ff;
}

.stage-item.warning .stage-value {
  color: #faad14;
}

.stage-item.success .stage-value {
  color: #52c41a;
}

/* 客户分析卡片 */
.chart-section {
  margin-bottom: 12px;
  flex-shrink: 0;
}

.chart-title {
  font-size: 13px;
  font-weight: 500;
  color: #4e5969;
  margin-bottom: 8px;
}

.chart-container {
  height: 260px;
  width: 100%;
}

.chart-container-compact {
  height: 200px;
  width: 100%;
}

.chart-container.large {
  height: 320px;
}

/* 客户排行 */
.customer-ranking {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.customer-ranking-compact {
  margin-top: 12px;
  flex-shrink: 0;
}

@media (max-width: 768px) {
  .customer-ranking {
    grid-template-columns: 1fr;
  }
}

.ranking-title {
  font-size: 13px;
  font-weight: 500;
  color: #4e5969;
  margin-bottom: 8px;
}

.ranking-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ranking-item {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.ranking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.customer-name {
  font-weight: 500;
  color: #1d2129;
}

.customer-amount {
  color: #4e5969;
}

/* 续费商机卡片 */
.renewal-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.renewal-item {
  padding: 8px;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.renewal-item.high {
  border-color: rgba(250, 173, 20, 0.3);
  background: rgba(250, 173, 20, 0.05);
}

.renewal-item.medium {
  border-color: rgba(64, 169, 255, 0.3);
  background: rgba(64, 169, 255, 0.05);
}

.renewal-item.low {
  border-color: rgba(22, 93, 255, 0.3);
  background: rgba(22, 93, 255, 0.05);
}

.renewal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6px;
}

.renewal-title {
  font-weight: 500;
  font-size: 14px;
  color: #1d2129;
  margin: 0;
  line-height: 1.3;
}

.renewal-status {
  font-size: 12px;
  margin: 2px 0 0 0;
  color: #f56c6c;
}

.renewal-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-fee {
  font-size: 12px;
  color: #4e5969;
}

/* 续费价值分析 */
.renewal-value-analysis {
  margin-top: 12px;
  padding-top: 6px;
  border-top: 1px solid #e5e6eb;
  flex-shrink: 0;
}

.analysis-title {
  font-size: 14px;
  font-weight: 500;
  color: #4e5969;
  margin-bottom: 5px;
}

.value-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.value-item {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.value-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #4e5969;
}

/* 报备商机卡片样式 */
.report-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
  margin-bottom: 12px;
}

.report-item {
  padding: 8px;
  border: 1px solid #e5e6eb;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.report-item.high {
  border-color: rgba(245, 108, 108, 0.3);
  background: rgba(245, 108, 108, 0.05);
}

.report-item.medium {
  border-color: rgba(250, 173, 20, 0.3);
  background: rgba(250, 173, 20, 0.05);
}

.report-item.low {
  border-color: rgba(64, 169, 255, 0.3);
  background: rgba(64, 169, 255, 0.05);
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 6px;
}

.report-title {
  font-weight: 500;
  font-size: 14px;
  color: #1d2129;
  margin: 0;
  line-height: 1.3;
}

.report-status {
  font-size: 12px;
  margin: 2px 0 0 0;
  color: #f56c6c;
}

.report-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.project-value {
  font-size: 12px;
  color: #4e5969;
}

/* 报备统计分析 */
.report-statistics {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #e5e6eb;
  flex-shrink: 0;
}

.statistics-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.statistics-item {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.statistics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #4e5969;
}

/* 分析区域 */
.analysis-section {
  margin-bottom: 32px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-title {
    display: none;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .customer-ranking {
    grid-template-columns: 1fr;
  }

  .stage-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .main-content {
    padding: 88px 8px 32px;
  }

  .quick-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .urgent-alerts {
    margin-left: 0;
  }
}
</style>

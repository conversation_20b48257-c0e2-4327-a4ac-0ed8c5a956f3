<template>
  <div class="avue-top">
    <div class="top-bar__left">
      <div
        class="avue-breadcrumb"
        :class="[{ 'avue-breadcrumb--active': isCollapse }]"
        v-if="setting.collapse && !isHorizontal"
      >
        <i class="icon-navicon" @click="setCollapse"></i>
      </div>
    </div>
    <div class="top-bar__title">
      <top-menu ref="topMenu" v-if="setting.menu"></top-menu>
      <top-search class="top-bar__item" v-if="setting.search"></top-search>
    </div>
    <div class="top-bar__right">
      <div class="top-bar__item">
        <topWx_mini v-if="show_portal_app == 1" type="1"></topWx_mini>
      </div>
      <div class="top-bar__item">
        <topWx_mini v-if="show_portal_app == 1" type="0"></topWx_mini>
      </div>
      <div v-if="setting.lock" class="top-bar__item">
        <top-lock></top-lock>
      </div>
      <div v-if="setting.theme" class="top-bar__item">
        <top-theme></top-theme>
      </div>
      <!-- <div class="top-bar__item">
        <top-lang></top-lang>
      </div> -->
      <div class="top-bar__item">
        <top-notice></top-notice>
      </div>
      <div class="top-bar__item" v-if="setting.fullscren">
        <top-full></top-full>
      </div>
      <div class="top-bar__item" v-if="setting.debug">
        <top-logs></top-logs>
      </div>
      <div class="top-user">
        <img class="top-bar__img" :src="userInfo.avatar" />
        <el-dropdown>
          <span class="el-dropdown-link">
            {{ userInfo.userName }}
            <el-icon class="el-icon--right">
              <arrow-down />
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item>
                <router-link to="/">{{ $t('navbar.dashboard') }}</router-link>
              </el-dropdown-item>
              <el-dropdown-item>
                <router-link to="/info/index">{{ $t('navbar.userinfo') }}</router-link>
              </el-dropdown-item>
              <el-dropdown-item>
                <router-link
                  to="/info/companyIndex"
                  v-if="
                    userInfo.role_name.indexOf('admin') > -1 ||
                    userInfo.role_name.indexOf('manager') > -1
                  "
                >
                系统设置
                </router-link>
              </el-dropdown-item>
              <el-dropdown-item
                v-if="
                  userInfo.role_name.indexOf('admin') > -1 ||
                  userInfo.role_name.indexOf('manager') > -1
                "
                @click="swithUser"
                >{{ '切换用户' }}
              </el-dropdown-item>
              <el-dropdown-item @click="logout">{{ $t('navbar.logOut') }} </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <!-- <top-setting></top-setting> -->
      </div>
    </div>
    <!-- 人员选择弹窗 -->
    <nf-user-select
      ref="user-select"
      :check-type="'radio'"
      @onConfirm="handleUserSelectConfirm"
    ></nf-user-select>
  </div>
</template>
<script>
import { mapGetters } from 'vuex';
import topLock from './top-lock.vue';
import topMenu from './top-menu.vue';
import topSearch from './top-search.vue';
import topTheme from './top-theme.vue';
import topLogs from './top-logs.vue';
import topLang from './top-lang.vue';
import topFull from './top-full.vue';
import topWx_mini from './top-wx_mini.vue';
import topSetting from '../setting.vue';
import topNotice from './top-notice.vue';
import { ElNotification } from 'element-plus';
import nfUserSelect from '@/views/plugin/workflow/components/nf-user-select/index.vue';
import { getUser } from '@/api/system/user';
import { getList } from '@/api/system/param';
export default {
  components: {
    topLock,
    topMenu,
    topSearch,
    topTheme,
    topLogs,
    topLang,
    topFull,
    topSetting,
    topNotice,
    nfUserSelect,
    topWx_mini,
  },
  name: 'top',
  data() {
    return {
      show_portal_app: '',
    };
  },
  filters: {},
  created() {
    getList(1, 10, {
      paramKey: 'show_portal_app',
    }).then(res => {
      this.show_portal_app = res.data.data.records[0].paramValue;
    });
  },
  computed: {
    ...mapGetters([
      'setting',
      'userInfo',
      'tagWel',
      'tagList',
      'isCollapse',
      'tag',
      'logsLen',
      'logsFlag',
      'isHorizontal',
    ]),
  },
  methods: {
    setCollapse() {
      this.$store.commit('SET_COLLAPSE');
    },
    logout() {
      this.$confirm(this.$t('logoutTip'), this.$t('tip'), {
        confirmButtonText: this.$t('submitText'),
        cancelButtonText: this.$t('cancelText'),
        type: 'warning',
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          this.$router.push({ path: '/login' });
          setTimeout(() => {
            ElNotification.closeAll();
          }, 0);
        });
      });
    },
    handleUserSelectConfirm(user) {
      getUser(user).then(res => {
        const loading = this.$loading({
          lock: true,
          text: '登录中,请稍后',
          background: 'rgba(0, 0, 0, 0.7)',
        });
        this.$store
          .dispatch('SwitchUser', res.data.data)
          .then(res => {
            this.$router.push({ path: `/redirect/home` });
            this.$store.commit('DEL_TAG', { fullPath: '/redirect/home' });
            this.$store.dispatch('GetMenu', '').then(data => {
              if (data.length !== 0) {
                this.$router.$avueRouter.formatRoutes(data, true);

                setTimeout(() => {
                  loading.close();
                  this.$store.dispatch('getMessageList', true);
                }, 1000);
              }
            });
          })
          .catch(err => {
            loading.close();
          });
      });
    },
    swithUser() {
      this.$refs['user-select'].visible = true;
    },
  },
};
</script>

<style lang="scss" scoped></style>

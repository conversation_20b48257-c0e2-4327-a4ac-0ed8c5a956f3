# 编辑面板字段同步修复说明

## 问题描述
右边编辑面板中的名称、品牌、型号、描述、单位字段无法同步到表格数据中，而其他数字字段（如数量、单价等）可以正常同步。

## 问题原因
1. **编辑面板缺少事件处理**：文本输入框只有 `v-model` 绑定，没有 `@input` 或 `@change` 事件来触发数据同步
2. **ProductRow 组件中产品描述字段缺少正确的事件处理**：textarea 没有正确的 blur 事件处理

## 修复内容

### 1. 修改 edit.vue 文件

#### 1.1 为编辑面板的文本字段添加事件处理
在以下字段的 `el-input` 组件中添加了 `@input` 事件：

- **产品名称** (`customProductName`)
- **品牌** (`productBrand`) 
- **规格型号** (`customProductSpecification`)
- **产品描述** (`customProductDescription`)
- **单位** (`customUnit`)
- **备注** (`remark`)

```vue
<!-- 修改前 -->
<el-input
  v-model="currentProduct.customProductName"
  placeholder="请输入产品名称"
  :disabled="$route.query.type == 'detail' || props.dialogView"
  data-field="customProductName"
></el-input>

<!-- 修改后 -->
<el-input
  v-model="currentProduct.customProductName"
  placeholder="请输入产品名称"
  :disabled="$route.query.type == 'detail' || props.dialogView"
  data-field="customProductName"
  @input="(value) => handlePanelFieldChange('customProductName', value)"
></el-input>
```

#### 1.2 优化 handleFieldChange 函数
修改了字段变更处理函数，确保所有字段变更都能触发表格数据更新：

```javascript
// 修改前
function handleFieldChange({ field, value, product }) {
  // 只对数字字段触发更新
  if (['number', 'sealPrice', ...].includes(field)) {
    // 触发更新
  }
}

// 修改后  
function handleFieldChange({ field, value, product }) {
  // 对所有字段变更都强制触发表格数据更新
  nextTick(() => {
    moduleDTOList.value = [...moduleDTOList.value]
    setTableData()
  })
}
```

### 2. 修改 ProductRow.vue 文件

#### 2.1 修复产品描述字段的事件处理
为产品描述的 textarea 添加了正确的 blur 事件处理：

```vue
<!-- 修改前 -->
<el-input
  @blur="$emit('blur', $event)"
  v-if="isActive"
  v-model="product.customProductDescription"
  type="textarea"
></el-input>
<textarea
  @focus="$emit('field-focus', 'customProductDescription')"
  v-model="product.customProductDescription"
  v-else
></textarea>

<!-- 修改后 -->
<el-input
  @blur="handleTextareaBlur('customProductDescription', $event)"
  v-if="isActive"
  v-model="product.customProductDescription"
  type="textarea"
></el-input>
<textarea
  @focus="$emit('field-focus', 'customProductDescription')"
  @blur="handleTextareaBlur('customProductDescription', $event)"
  v-model="product.customProductDescription"
  v-else
></textarea>
```

#### 2.2 添加 handleTextareaBlur 函数
新增了专门处理 textarea 字段失焦的函数：

```javascript
// 处理textarea字段失焦
const handleTextareaBlur = (field, event) => {
  const value = event.target.value
  if (value !== undefined) {
    props.product[field] = value
    // 触发字段变更事件，通知父组件更新汇总数据
    emit('field-change', { field, value: props.product[field], product: props.product })
  }
}
```

## 修复效果
修复后，编辑面板中的以下字段现在可以正确同步到表格数据：
- ✅ 产品名称
- ✅ 品牌  
- ✅ 规格型号
- ✅ 产品描述
- ✅ 单位
- ✅ 备注

## 测试建议
1. 打开编辑面板，修改产品名称、品牌等文本字段
2. 检查表格中对应的数据是否实时更新
3. 切换到其他产品再切换回来，确认数据持久化
4. 测试在表格中直接编辑这些字段是否也能正常工作

<template>
  <basic-container :shadow="orderId?'never':'always'">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      :table-loading="loading"
      ref="crud"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @keyup.enter="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #inStorageCode="{ row }">
        <el-link type="primary" @click="toDetail(row)">{{ row.inStorageCode }}</el-link>
      </template>
      <template #menu-left>
        <el-button icon="plus" @click="handleAdd" type="primary">新增</el-button>
      </template>
      <template #menu="{ row }">
        <el-button type="primary" text icon="view" @click="toDetail(row)">详情</el-button>
      </template>
      <template #orderNo="{ row }">
        <el-link type="primary" @click="toOrderDetail(row)">{{ row.orderNo }}</el-link>
      </template>
      <template #productNames="{row}">
        <el-tag effect='plain' style="margin-right:5px" type="primary" v-for="(item,index) in row.productNames?.split(',')" :key="index">{{item}}</el-tag>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
let route = useRoute();
const props = defineProps(['orderId'])
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 200,
  border: true,
  column: [
    {
      label: '入库单号',
      prop: 'inStorageCode',
      width:180,
      overHidden: true,
      // search: !route.query.id,
    },
    {
      label: '入库产品',
      prop: 'productNames',
      search:true,
      align:'left',
      // width:300,
      overHidden: true,
    },
    {
      label: '入库数量',
      prop: 'inStorageNumber',
      width:100,
    },
    // {
    //   label: '关联供应商',
    //   prop: 'supllierName',
    //   search:true,
    //   searchLabelWidth:120,
    // },
    {
      label: '关联订单',
      prop: 'orderNo',
      width:180,
      overHidden: true,
      // search: !route.query.id,
    },
    {
      label: '关联客户',
      prop: 'customerName',
      overHidden: true,
      width:180,
    },
    {
      label: '入库类型',
      prop: 'inStorageType',
      type: 'radio',
      width:100,
      dicData: [
        {
          value: 0,
          label: '订单入库',
        },
      
        {
          value: 2,
          label: '客户退货',
        },
        {
          value: 3,
          label: '期初入库',
        },
        {
          value: 4,
          label: '其他',
        },
       
      ],
      rules: [{ required: true, message: '请选择入库类型' }],
      
    },
    {
      label: '入库时间',
      prop: 'inStorageDate',
      type:'date',
      component: 'wf-daterange-search',
        search:true,
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      searchSpan: 5,
      search: true,
      width:150,
    },
    {
      label:'序列号',
      prop:'serialNumber',
      search:true,
      hide:true,

    },
    
    {
      label: '入库人',
      prop: 'createName',
      width:100,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/purchaseInStorage/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();

onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        orderId:props.orderId,
        size,
        current,
        ...params.value,
        startDate: params.value.inStorageDate && params.value.inStorageDate[0],
        endDate: params.value.inStorageDate && params.value.inStorageDate[1],
        inStorageDate: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function handleAdd() {
  router.push({
    path: '/SRM/warehouse/compoents/addInhouse',
    query: {
      orderId: route.query.id || null,
    },
  });
}
function toDetail(row) {
  router.push({
    path: '/SRM/warehouse/compoents/InhouseDetail',
    query: {
      id: row.id,
    },
  });
}
function toOrderDetail(row,activeName = null) {
  router.push({
    path: '/SRM/procure/compoents/orderDetail',
    query: {
      id: row.orderId,
      activeName
    },
  });
}
</script>

<style lang="scss" scoped>
</style>

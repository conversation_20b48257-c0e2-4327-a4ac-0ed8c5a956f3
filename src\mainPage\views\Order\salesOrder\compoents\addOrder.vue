<template>
  <basic-container>
    <Title style="margin-bottom: 10px">
      新增合同
      <template #foot
        ><el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >
    <avue-form :option="option" @submit="handleSubmit" v-model="form">
      <template #customerInvoiceId>
        <wfInvoiceDrop v-model="form.customerInvoiceId" :id="form.customerId"></wfInvoiceDrop>
      </template>
      <template #deliveryAddress>
        <el-autocomplete
          v-model="form.deliveryAddress"
          style="width: 100%"
          :fetch-suggestions="querySearchAsync"
          value-key="deliveryAddress"
        />
      </template>
      <template #cycleNumber>
        <div style="width: 100%; display: flex; align-items: center">
          <el-input
            v-model.number="form.cycleNumber"
            placeholder="请输入"
            style="width: 20%; margin-right: 5px"
          />
          <el-radio v-model="form.cycleType" :label="0">天</el-radio>
          <el-radio v-model="form.cycleType" :label="1">月</el-radio>
          <el-radio v-model="form.cycleType" :label="2">年</el-radio>
        </div>
      </template>
      <template #renewInfo-header>
        <h1 class="avue-group__title">续费信息</h1>
      </template>
      <template #decompose>
        <el-row :gutter="20" style="height: 100%">
          <el-col :span="10">
            <el-card shadow="never" style="height: 100%">
              <el-alert type="success" :closable="false">请将需要拆分的产品勾选上</el-alert>
              <avue-crud
                :option="needDeposeOption"
                :data="needDeposeData"
                @row-click="handleRowClick"
              >
                <template #menu="{ row }">
                  <el-switch v-model="row.isDepose" :active-value="1" :inactive-value="0">
                  </el-switch>
                </template>
              </avue-crud>
            </el-card>
          </el-col>
          <el-col style="height: 100%" :span="14">
            <el-row style="margin-bottom: 10px">
              <el-col :span="24">
                <el-card shadow="never">
                  <!-- <avue-form :value="detailForm" :option="decomposeOption"></avue-form> -->
                  <el-descriptions border :title="detailForm.customProductName" :column="3">
                    <el-descriptions-item label="规格型号">{{
                      detailForm.customProductSpecification
                    }}</el-descriptions-item>
                    <el-descriptions-item label="品牌">{{
                      detailForm.productBrand
                    }}</el-descriptions-item>
                    <el-descriptions-item label="单位">{{
                      detailForm.unitName
                    }}</el-descriptions-item>
                    <el-descriptions-item label="描述">
                      {{ detailForm.customProductDescription }}
                    </el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-card shadow="never">
                  <template #header>
                    <!-- <el-alert type="info" :closable="false"
                    >拆解会删除被拆解的产品，如需保留请再拆解列表里面添加</el-alert
                  > -->
                    <div style="display: flex; justify-content: space-between">
                      <div>拆解产品</div>
                      <div>
                        <el-form >
                          <el-form-item label="保留原产品">
                            <el-switch
                              v-model="detailForm.isPre"
                              :disabled="!detailForm.productId"
                              @change="handleIsPreChange"
                            ></el-switch>
                          </el-form-item>
                        </el-form>
                      </div>
                    </div>
                  </template>
                  <avue-crud
                    @row-del="rowDelProduct"
                    :data="detailForm.detailDTOList"
                    :option="decomposeEditFormOption"
                  >
                    <template #menu-left>
                      <div v-if="detailForm.isDepose">
                        <el-button
                          icon="plus"
                          type="primary"
                          size="small"
                          @click="$refs.productSelectRef.visible = true"
                        ></el-button>
                        <productSelectDrop
                          v-if="$route.query.type != 'detail'"
                          @select="handleProductSelectConfirm"
                          style="margin-left: 5px"
                        ></productSelectDrop>
                      </div>
                    </template>
                    <template #number="{ row }">
                      <el-input-number
                        size="small"
                        style="width: 80%"
                        controls-position="right"
                        v-model="row.number"
                      ></el-input-number>
                    </template>
                  </avue-crud>
                </el-card>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </template>
    </avue-form>
    <el-dialog title="添加计划收款" v-model="planVisible" class="avue-dialog avue-dialog--top">
      <avue-form
        v-if="planVisible"
        ref="planForm"
        :option="addPlanOption"
        @submit="addPlanSubmit"
        v-model="addPlanForm"
      >
        <template #planCollectionPrice>
          <div style="display: flex; align-items: center">
            <el-input-number
              style="width: calc(100% - 390px)"
              v-model="addPlanForm.planCollectionPrice"
              controls-position="right"
            ></el-input-number>
            <el-tag effect="plain" size="large" style="width: 150px; margin: 0 20px" type="primary"
              >合同额：{{ form.contractTotalPrice }}</el-tag
            >
            <el-input
              v-model="addPlanForm.rate"
              placeholder="填写比例以计算"
              style="width: 200px"
              @change="
                value => {
                  addPlanForm.planCollectionPrice =
                    form.contractTotalPrice * (addPlanForm.rate / 100);
                }
              "
            >
              <template #append>%</template>
            </el-input>
          </div>
        </template>
      </avue-form>
      <!-- <slot ></slot> -->
      <div class="avue-dialog__footer">
        <el-button @click="planVisible = false">取 消</el-button>
        <el-button @click="$refs.planForm.submit()" type="primary">确 定</el-button>
      </div>
    </el-dialog>
    <wfProductSelect
      @onConfirm="handleProductSelectConfirm"
      ref="productSelectRef"
    ></wfProductSelect>
  </basic-container>
</template>

<script setup>
let baseUrl = '/api/blade-system/region/lazy-tree';

import axios from 'axios';
import { useStore } from 'vuex';
import { computed, watch } from 'vue';
import { ref, getCurrentInstance } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import wfInvoiceDrop from './wf-invoice-drop.vue';
import { planNameData } from '@/const/const';
import wfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import moment from 'moment';
import { dateFormat } from '@/utils/date';
const form = ref({});
const router = useRouter();
const route = useRoute();
const store = useStore();
const { proxy } = getCurrentInstance();

function validateName(rule, value, callback) {
  axios.get('/api/vt-admin/customer/existCustomerName?customerName=' + value).then(res => {
    if (res.data.data == 1) {
      callback(new Error('名字不能重复'));
    } else {
      callback();
    }
  });
}
watchEffect(() => {
  if (route.query.id) {
    getDetail();
  }
});
watchEffect(() => {
  if (route.query.projectId) {
    getProjectDetail();
  }
});
let userInfo = computed(() => store.getters.userInfo);
onMounted(() => {
  if (route.query.offerId) {
    getquotationDetail(route.query.offerId);
  }
});
watch(
  () => route.query.offerId,
  () => {
  
    if (!route.query.offerId) return;
    form.value.offerId = route.query.offerId;
    getquotationDetail(route.query.offerId);
    detailForm.value.detailDTOList = []
    needDeposeData.value = [];
  }
);
let isPaymentPeriodData = ref([]);
const type = route.query.type;
console.log(type);
const option = ref({
  labelWidth: 150,
  emptyBtn: false,
  group: [
    {
      label: '基本信息',
      prop: 'baseInfo',
      arrow: true,
      collapse: true,
      display: true,
      column: [
        {
          type: 'input',
          label: '关联报价',
          display: true,
          component: 'wf-quotation-select',
          prop: 'offerId',
          value: route.query.offerId || null,
          params: {
            Url:
              type == 'assist'
                ? '/api/vt-admin/offer/pageForAddSealContract?selectType=3'
                : '/api/vt-admin/offer/pageForAddSealContract?selectType=0',
          },
          rules: [
            {
              required: true,
              message: '请选择关联报价',
              trigger: 'change',
            },
          ],
          change: val => {
            if (val.value) {
              getquotationDetail(val.value);
            }
          },
        },
        {
          type: 'input',
          label: '关联商机',
          display: true,
          disabled: true,
          placeholder: '请先选择报价',
          component: 'wf-business-select',
          prop: 'businessOpportunityId',
          change: val => {
            if (val.value) {
              getBusinessDetail(val.value);
            }
          },
        },
        {
          label: '客户名称',
          span: 12,
          display: true,
          readonly: true,
          prop: 'customerName',
        },
        {
          type: 'input',
          label: '客户地址',
          span: 12,
          display: true,
          prop: 'customerAddress',
        },
        {
          label: '关联联系人',
          type: 'input',
          prop: 'customerContact',
          component: 'wf-contact-select',
          placeholder: '请先选择报价',
          // disabled: true,
          params: {},
          change: ({ value }) => {
            setBaseInfo1(value);
          },
        },
        {
          type: 'input',
          label: '电话',
          span: 12,
          display: true,
          prop: 'customerPhone',
        },
        {
          label:'最终客户',
          prop:'finalCustomer',
          span:12
        }
      ],
    },

    {
      label: '送货信息',
      prop: 'distributionInfo',
      arrow: true,
      collapse: true,
      display: true,
      column: [
        {
          label: '送货方式',
          type: 'radio',
          prop: 'distributionMethod',
          span: 12,
          value: '1',
          dicUrl: '/blade-system/dict/dictionary?code=delivery_method',
          props: {
            value: 'dictKey',
            label: 'dictValue',
          },
        },
        {
          type: 'date',
          label: '客户要求交付日期',
          span: 12,
          display: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          prop: 'deliveryDate',
          required: true,
          // rules: [
          //   {
          //     required: true,
          //     message: '交付日期必须填写',
          //   },
          // ],
          change: val => {
            form.value.contractDeliveryDate = val.value;
          },
        },
        {
          type: 'select',
          label: '交付地址',
          span: 24,
          display: true,
          prop: 'deliveryAddress',
          dicData: [],
          props: {
            value: 'deliveryAddress',
            label: 'deliveryAddress',
          },
        },
        {
          label: '关联联系人',
          type: 'input',
          prop: 'contact',
          component: 'wf-contact-select',
          placeholder: '请先选择报价',
          // disabled: true,
          params: {},
          change: ({ value }) => {
            setBaseInfo2(value);
          },
        },
        {
          type: 'input',
          label: '联系电话',
          span: 12,
          display: true,
          prop: 'contactPhone',
        },
        {
          type: 'input',
          label: '开票信息',
          span: 24,
          display: true,
          prop: 'customerInvoiceId',
        },
        {
          type: 'textarea',
          label: '备注',
          span: 24,
          display: true,
          prop: 'remark',
        },
      ],
    },
    {
      label: '合同信息',
      prop: 'contractInfo',
      arrow: true,
      collapse: true,
      display: true,
      column: [
        // {
        //   type: 'input',
        //   label: '编号',
        //   span: 12,
        //   display: true,
        //   readonly: true,
        //   placeholder: '自动生成',
        //   prop: 'contractCode',
        // },
        {
          type: 'input',
          label: '合同名称',
          span: 12,
          display: true,
          prop: 'contractName',
          required: true,
          rules: [
            {
              required: true,
              message: '项目名称必须填写',
            },
          ],
        },
        {
          type: 'input',
          label: '对方订单编号',
          span: 12,
          display: true,
          prop: 'customerOrderNumber',
        },
        {
          type: 'input',
          label: '合同金额',
          span: 12,
          display: true,
          disabled: true,
          prop: 'contractTotalPrice',
          required: true,
          rules: [
            {
              required: true,
              message: '订单金额必须填写',
            },
          ],
        },
        {
          label: '付款期限',
          type: 'radio',
          span: 12,
          prop: 'paymentDeadline',
          dicFormatter: res => {
            isPaymentPeriodData.value = res.data;
            return res.data;
          },
          control: (val, form, b, c) => {
            return {
              fixedBillingDate: {
                display:
                  isPaymentPeriodData.value.find(item => item.id == val)?.dictValue == '固定账期',
              },
            };
          },
          dicUrl: '/blade-system/dict/dictionary?code=isPaymentPeriod',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          type: 'number',
          label: '固定账期时间',
          span: 12,
          display: true,
          min: 1,
          max: 31,
          tip: '输入1到31之间的数字,账期则为每月这个时间',
          prop: 'fixedBillingDate',
          rules: [
            {
              required: true,
              message: '请输入固定账期时间',
              trigger: 'blur',
            },
          ],
        },
        {
          label: '是否开票',
          type: 'switch',
          prop: 'isNeedInvoice',
          value: 1,
          dicData: [
            {
              value: 0,
              label: '否',
            },
            {
              value: 1,
              label: '是',
            },
          ],
        },
        {
          label: '是否预订单',
          type: 'switch',
          prop: 'isPreOrder',
          value: 0,
         
          dicData: [
            {
              value: 0,
              label: '否',
            },
            {
              value: 1,
              label: '是',
            },
          ],
        },
        {
          label: '结算方式',
          type: 'radio',
          prop: 'settlementMethod',
          span: 24,
          dicUrl: '/blade-system/dict/dictionary?code=payment_methods',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          type: 'select',
          label: '发票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          span: 12,
          search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          type: 'date',
          label: '签订日期',
          span: 12,
          display: true,
          prop: 'signDate',
          required: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          value: dateFormat(new Date()),
          rules: [
            {
              required: true,
              message: '签订日期必须填写',
            },
          ],
        },
        {
          label: '确认附件',
          prop: 'contractFiles',
          type: 'upload',
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          type: 'date',
          label: '合同交付日期',
          span: 12,
          display: true,
          prop: 'contractDeliveryDate',
          required: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
      ],
    },
    {
      label: '计划收款',
      column: [
        {
          label: '计划收款',
          type: 'dynamic',
          span: 24,
          labelWidth: 0,
          prop: 'sealContractPlanCollectionDTOS',
          children: {
            align: 'center',
            headerAlign: 'center',
            rowAdd: done => {
              planVisible.value = true;
            },
            rowDel: (row, done) => {
              done();
            },
            column: [
              // {
              //   type: 'input',
              //   label: '客户名称',
              //   span: 12,
              //   hide: true,
              //   display: true,
              //   prop: 'a170080949446133484',
              // },
              // {
              //   type: 'input',
              //   label: '项目名称',
              //   span: 12,
              //   display: true,
              //   hide: true,
              //   prop: 'a170080951774565537',
              // },
              {
                label: '计划名称',
                prop: 'planName',

                type: 'radio',
                dicData: planNameData,
                span: 24,
                rules: [
                  {
                    required: true,
                    message: '计划名称必须填写',
                  },
                ],
              },
              {
                type: 'date',
                label: '计划收款时间',
                span: 12,
                display: true,
                format: 'YYYY-MM-DD',
                valueFormat: 'YYYY-MM-DD',
                prop: 'planCollectionDate',
                disabled: false,
                readonly: false,
                width: 200,
                required: true,
                rules: [
                  {
                    required: true,
                    message: '计划收款时间必须填写',
                  },
                ],
              },
              {
                type: 'number',
                label: '计划收款金额',
                controls: true,
                width: 200,
                span: 18,
                display: true,
                prop: 'planCollectionPrice',
              },
            
              {
                type: 'textarea',
                label: '备注',
                span: 24,
                display: true,
                prop: 'remark',
                showWordLimit: true,
              },
            ],
          },
        },
      ],
    },
    {
      label: '采购拆解',
      prop: 'decomposeTab',
      arrow: true,
      collapse: true,
      display: true,
      column: [
        {
          label: '',
          labelWidth: 0,
          prop: 'decompose',
          span: 24,
        },
        {
          label: '采购注意事项',
          prop: 'purchaseRemark',
          type: 'textarea',
          labelWidth: 120,
          span: 24,
        },
      ],
    },
    // {
    //   label: '续费信息',
    //   prop: 'renewInfo',
    //   display:false,
    //   column: [
    //     {
    //       label:'是否续费',
    //       prop:'isHasRenew',
    //       span:12,
    //       type:'switch',
    //       dicData:[
    //      {
    //         value:0,
    //         label:'否'
    //       },{
    //         value:1,
    //         label:'是'
    //       }
    //       ]
    //     },
    //     {
    //       type: 'date',
    //       label: '开始使用日期',
    //       span: 12,
    //       width: 100,
    //       rules: [
    //         {
    //           required: true,
    //           message: '请输入开始使用日期',
    //         },
    //       ],
    //       format: 'YYYY-MM-DD',
    //       valueFormat: 'YYYY-MM-DD',
    //       prop: 'useDate',
    //     },

    //     {
    //       type: 'input',
    //       label: '使用周期',
    //       width: 110,
    //       span: 24,
    //       display: true,
    //       rules: [
    //         {
    //           required: true,
    //           message: '请输入使用周期',
    //         },
    //       ],
    //       prop: 'cycleNumber',
    //     },

    //     {
    //       label: '是否提醒',
    //       prop: 'isRemind',
    //       type: 'switch',
    //       value: 1,
    //       dicData: [
    //         { label: '否', value: 0 },
    //         { label: '是', value: 1 },
    //       ],
    //       control: val => {
    //         return {
    //           beforeDays: {
    //             display: !!val,
    //           },
    //         };
    //       },
    //     },
    //     {
    //       type: 'select',
    //       label: '提醒时间',
    //       width: 110,
    //       span: 12,
    //       display: true,
    //       props: {
    //         value: 'value',
    //         label: 'label',
    //       },
    //       dicData: (() => {
    //         let arr = [];
    //         for (let i = 30; i >= 1; i -= 5) {
    //           arr.push({ label: '到期' + i + '天前', value: i });
    //         }
    //         return arr;
    //       })(),
    //       prop: 'beforeDays',
    //     },
    //     {
    //       label: '备注',
    //       prop: 'remark',
    //       span: 24,
    //       type: 'textarea',
    //     },
    //     {
    //       label: '产品信息',
    //       type: 'dynamic',
    //       span: 24,
    //       // labelWidth: 0,
    //       prop: 'productVOList',
    //       children: {
    //         align: 'center',
    //         headerAlign: 'center',
    //         rowAdd: done => {
    //           proxy.$message.warning('无法添加');
    //           // planVisible.value = true;
    //         },
    //         rowDel: (row, done) => {
    //           // proxy.$message.warning('无法删除');
    //           done();
    //         },
    //         column: [
    //           {
    //             label: '产品',
    //             prop: 'customProductName',

    //             cell: false,
    //             overHidden: true,
    //           },
    //           {
    //             label: '规格型号',
    //             prop: 'customProductSpecification',

    //             overHidden: true,
    //             // search: true,
    //             span: 24,
    //             cell: false,
    //             type: 'input',
    //           },

    //           {
    //             label: '数量',
    //             prop: 'number',
    //             type: 'number',
    //             span: 12,
    //             width: 110,
    //             cell: true,
    //           },
    //           {
    //             label: '单位',
    //             prop: 'unitName',
    //             formatter: row => {
    //               return row.customUnit || row.product.unitName;
    //             },
    //             span: 12,
    //             cell: false,
    //           },
    //           {
    //             label: '单价',
    //             prop: 'sealPrice',
    //             type: 'number',
    //             span: 12,
    //             cell: true,
    //           },
    //           {
    //             label: '金额',
    //             prop: 'totalPrice',
    //             type: 'number',
    //             span: 12,
    //             editDisplay: false,
    //             cell: false,
    //           },
    //         ],
    //       },
    //     },
    //   ],
    // },
  ],
});
function handleSubmit(form, done, loading) {
  if (!form.id) {
    const data = {
      ...form,
      contractFiles: form.contractFiles.map(item => item.value).join(','),
      signDate:form.signDate.split(' ')[0]
    };
    decomposeSubmit().then(res => {
      axios
        .post(`/api/vt-admin/sealContract/${form.id ? 'update' : 'save'}`, data)
        .then(res => {
          if (res.data.code == 200) {
            proxy.$message.success(res.data.msg);
            router.$avueRouter.closeTag();
            router.go(-1);
            done();
          }
        })
        .catch(() => {
          done();
        });
    });
  } else {
    axios
      .post(`/api/vt-admin/sealContract/${form.id ? 'update' : 'save'}`, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          router.$avueRouter.closeTag();
          router.go(-1);
          done();
        }
      })
      .catch(() => {
        done();
      });
  }
}
function getBusinessDetail(id) {
  axios
    .get('/api/vt-admin/businessOpportunity/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const {
        productVOList,
        // id: businessOpportunityId,
        customerId,
        id: businessOpportunityId,
        customerName,
        contactPersonName,
        contactPhone,
        address,
        businessSourceType,
        beforeDays,
        useDate,
        cycleType,
        cycleNumber,
        isRemind,

        overDate,
      } = res.data.data;

      form.value.businessOpportunityId = businessOpportunityId;
      // form.value.customerId = customerId;
      form.value.customerName = customerName;
      form.value.contactPerson = contactPersonName;
      form.value.contactPhone = contactPhone;
      form.value.address = address;
      form.value.businessSourceType = businessSourceType;

      form.value.beforeDays = beforeDays;
      form.value.useDate = overDate ? moment(overDate).add(1, 'days').format('YYYY-MM-DD') : '';
      form.value.cycleType = cycleType;
      form.value.isRemind = isRemind;
      form.value.cycleNumber = cycleNumber;
      form.value.isHasRenew = form.value.businessSourceType == 1 ? 1 : 0;
      if (form.value.businessSourceType != 1) {
        const renewObj = proxy.findObject(option.value.group, 'renewInfo');
        renewObj.display = false;
      } else {
        const renewObj = proxy.findObject(option.value.group, 'renewInfo');
        renewObj.display = true;
      }
    });
}
function getquotationDetail(id) {
  axios
    .get('/api/vt-admin/offer/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const {
        productVOList,
        // id: businessOpportunityId,
        customerId,
        businessOpportunityId,
        customerName,
        customerContact,
        customerPhone,
        contactPerson,
        customerAddress,
        isHasOption,
        offerPrice,
        offerName,
        businessOpportunityName,
        fixedBillingDate,
        isPaymentPeriod,
        detailVOList,
      } = res.data.data;
      currentRow.value = res.data.data;
      getNeedDeposeData(currentRow.value);
      form.value.businessOpportunityId = businessOpportunityId;
      // form.value.customerId = customerId;
      form.value.customerName = customerName;
      form.value.customerId = customerId;
      form.value.customerContact = contactPerson;
      form.value.contact = contactPerson;
      form.value.customerPhone = customerPhone;
      form.value.contactPhone = customerPhone;
      form.value.customerAddress = customerAddress;
      form.value.deliveryAddress = customerAddress;
      form.value.contractTotalPrice = offerPrice;
      form.value.contractName = offerName;
      form.value.fixedBillingDate = fixedBillingDate;
      form.value.paymentDeadline = isPaymentPeriod;
      form.value.productVOList = detailVOList;

      setUrl(form.value.customerId);
      getBusinessDetail(businessOpportunityId);
    });
}
// function setUrl(id) {
//   const contactPerson = proxy.findObject(option.value.group[0].column, 'customerContact');
//   console.log(contactPerson);
//   contactPerson.params.Url = '/vt-admin/customerContact/page?customerId=' + id;
// }
function getDetail() {
  axios
    .get('/api/vt-admin/sealContract/detail', {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
      // form.value = formatData(res.data.data)
      form.value = {
        ...res.data.data,
        distributionMethod: '' + res.data.data.distributionMethod,
        contractFiles: res.data.data.attachList.map(item => {
          return {
            value: item.id,
            label: item.originalName,
          };
        }),
      };
      console.log(form.value);
      const decomposeTab = proxy.findObject(option.value.group, 'decomposeTab');
      decomposeTab.display = !form.value.id;
    });
}
function querySearchAsync(val, cb) {
  if (!form.value.customerId) return;
  axios
    .get('/api/vt-admin/sealContract/getAddressPage', {
      params: {
        customerId: form.value.customerId,
        size: 50,
      },
    })
    .then(res => {
      cb(res.data.data.records);
    });
}
function setUrl(id) {
  const customerContact = proxy.findObject(option.value.group[0].column, 'customerContact');
  customerContact.params.Url = '/vt-admin/customerContact/page?customerId=' + id;
  const contact = proxy.findObject(option.value.group[1].column, 'contact');
  contact.params.Url = '/vt-admin/customerContact/page?customerId=' + id;
}
function setBaseInfo2(id) {
  axios
    .get('/api/vt-admin/customerContact/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      form.value.contactPhone = res.data.data.phone;
    });
}
function setBaseInfo1(id) {
  axios
    .get('/api/vt-admin/customerContact/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      form.value.customerPhone = res.data.data.phone;
    });
}
function getProjectDetail() {
  axios
    .get('/api/vt-admin/project/detail', {
      params: {
        id: route.query.projectId,
      },
    })
    .then(res => {
      const { offerId, settlementMethod, invoiceType, deliveryDate } = res.data.data;
      form.value.offerId = offerId;
      form.value.settlementMethod = settlementMethod;
      form.value.invoiceType = invoiceType;
      form.value.contractDeliveryDate = deliveryDate;
      getquotationDetail(offerId);
    });
}
// 添加计划收款
let planVisible = ref(false);
let addPlanForm = ref({});
let addPlanOption = ref({
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  column: [
    {
      label: '计划名称',
      prop: 'planName',

      type: 'radio',
      dicData: planNameData,
      span: 24,
      change: val => {
        addPlanForm.value.rate = '';
        if (val.value == '全款') {
          console.log(val, form.value.contractTotalPrice);
          addPlanForm.value.planCollectionPrice = form.value.contractTotalPrice;
        } else {
          addPlanForm.value.planCollectionPrice = '';
        }
      },
      rules: [
        {
          required: true,
          message: '计划名称必须填写',
        },
      ],
    },

    {
      type: 'date',
      label: '计划收款时间',
      span: 24,
      display: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'planCollectionDate',
      disabled: false,
      readonly: false,
      required: true,
      rules: [
        {
          required: true,
          message: '计划收款时间必须填写',
        },
      ],
    },
    {
      type: 'number',
      label: '计划收款金额',
      controls: true,
      span: 18,
      display: true,
      prop: 'planCollectionPrice',
    },
    // {
    //   type: 'number',
    //   label: '收款比例',
    //   controls: true,
    //   span: 24,
    //   prop: 'collectionRate',

    // },

    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
      showWordLimit: true,
    },
  ],
});
function addPlanSubmit(row, done) {
  form.value.sealContractPlanCollectionDTOS.push(row);
  done();
  proxy.$refs.planForm.resetFields();
  planVisible.value = false;
}

// 拆解
let drawer = ref(false);
let currentRow = ref(null);
// function customerConfirmAndDecompose(row) {
//   drawer.value = true;
//   currentRow.value = row;
//   getNeedDeposeData(row);
// }

let decomposeEditFormOption = ref({
  submitBtn: false,
  emptyBtn: false,
  editBtn: false,
  // header:false,
  addBtn: false,
  column: [
    {
      label: '产品名称',
      prop: 'productName',
      overHidden: true,
      cell: false,
    },
    {
      label: '规格型号',
      prop: 'productSpecification',
      overHidden: true,
      cell: false,
      span: 24,
      type: 'input',
    },
    {
      label: '产品分类',
      prop: 'categoryId',

      hide: true,
      filterable: true,
      type: 'tree',
      cell: false,
      rules: [
        {
          required: true,
          message: '请选择产品分类',
          trigger: 'change',
        },
      ],
      children: 'hasChildren',
      parent: false,
      addDisplay: false,
      cell: false,
      dicUrl: '/api/vt-admin/productCategory/tree',
      props: {
        label: 'categoryName',
        value: 'id',
      },
    },
    {
      label: '品牌',
      cell: false,
      prop: 'productBrand',
      overHidden: true,
    },
    {
      label: '单位',
      type: 'select',
      cell: false,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],
      value: '1693833617402880001',
      prop: 'unit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      width: 120,
      rules: [
        {
          required: true,
          message: '请输入数量',
          trigger: 'blur',
        },
      ],
    },
  ],
});
let needDeposeData = ref([]);
let needDeposeOption = ref({
  header: false,
  addBtn: false,
  border: true,
  editBtn: false,
  delBtn: false,
  menuWidth: 80,
  column: [
    {
      label: '产品名称',
      prop: 'customProductName',
      overHidden: true,
      cell: false,
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',
      overHidden: true,
      cell: false,
      span: 24,
      type: 'input',
    },
    {
      label: '产品分类',
      prop: 'categoryId',

      hide: true,
      filterable: true,
      type: 'tree',
      cell: false,
      rules: [
        {
          required: true,
          message: '请选择产品分类',
          trigger: 'change',
        },
      ],
      children: 'hasChildren',
      parent: false,
      addDisplay: false,
      cell: false,
      dicUrl: '/api/vt-admin/productCategory/tree',
      props: {
        label: 'categoryName',
        value: 'id',
      },
    },
    {
      label: '品牌',
      cell: false,
      prop: 'productBrand',
      overHidden: true,
    },
    {
      label: '单位',
      type: 'select',
      cell: false,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],
      value: '1693833617402880001',
      prop: 'unit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      rules: [
        {
          required: true,
          message: '请输入数量',
          trigger: 'blur',
        },
      ],
    },
  ],
});
function getNeedDeposeData(row) {
  axios
    .get('/api/vt-admin/offer/getOfferProducts', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      needDeposeData.value = res.data.data;
    });
}
let detailForm = ref({});
function handleRowClick(row) {
  detailForm.value = row;
  if (!detailForm.value.detailDTOList) {
    detailForm.value.detailDTOList = [];
  }
}
async function handleProductSelectConfirm(id) {
  console.log(detailForm.value == needDeposeData.value[0]);
  const res = await axios.get('/api/vt-admin/product/detail?id=' + id);
  detailForm.value.detailDTOList.push({
    productId: res.data.data.id,
    productName: res.data.data.productName,
    productSpecification: res.data.data.productSpecification,
    productBrand: res.data.data.productBrand,
    unit: res.data.data.unitName,
    number: detailForm.value.number,
  });
}
function rowDelProduct(form) {
  proxy
    .$confirm('此操作将删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      detailForm.value.detailDTOList = detailForm.value.detailDTOList.filter(
        item => item.productId !== form.productId
      );
    })
    .catch(() => {});
}
function handleIsPreChange(val) {
  console.log(val);
  if (val) {
    handleProductSelectConfirm(detailForm.value.productId);
  } else {
    rowDelProduct({
      productId: detailForm.value.productId,
    });
  }
}
function decomposeSubmit() {
  const data = {
    id: currentRow.value.id,
    offerStatus: 2,
    purchaseRemark:form.value.purchaseRemark,
    dismantleList: needDeposeData.value
      .filter(item => item.isDepose == 1)
      .map(item => {
        return {
          id: item.id,
          productList: item.detailDTOList.map(item => {
            return {
              productId: item.productId,
              number: item.number,
            };
          }),
        };
      }),
  };
  console.log(data);
  return axios.post('/api/vt-admin/offer/customerConfirm', data);
}
</script>

<style lang="scss" scoped></style>

<template>
  <div class="demo-date-picker" style="width: 100%;">
    <div class="block" style="display: flex; align-items: center" v-if="timeType == 'date'">
      <el-date-picker
        v-model="dateRange[0]"
        @change="handleChage($event, 0)"
        :format="format"
      
        :valueFormat="valueFormat"
        :type="type"
        placeholder="开始日期"
      />
      -
      <el-date-picker
        v-model="dateRange[1]"
        :type="type"
        :format="format"
        :valueFormat="valueFormat"
        @change="handleChage($event, 1)"
        placeholder="结束日期"
      />
    </div>
    <div class="block" style="display: flex; align-items: center" v-else>
      <el-date-picker
        v-model="dateRange[0]"
        @change="handleDateChange"
        format="YYYY-MM-DD"
        valueFormat="YYYY-MM-DD"

        type="date"
        placeholder="开始日期"
      />
      <el-time-select
        v-model="timeRange[0]"
        @change="handleChage($event, 0)"
        start="08:30"
        step="00:10"
        end="24:00:00"
       
        format="HH:mm:ss"
        placeholder="开始时间"
      />
      -
      <el-date-picker
        v-model="dateRange[1]"
        @change="handleChage($event, 1)"
        format="YYYY-MM-DD"
        valueFormat="YYYY-MM-DD"
        type="date"
        placeholder="结束日期"
      />
      <el-time-select
        v-model="timeRange[1]"
        @change="handleChage($event, 1)"
        start="08:30:00"
        step="00:10:00"
        end="24:00:00"
        :min-time="timeRange[0]"
       
        format="HH:mm:ss"
        placeholder="结束时间"
      />
    </div>
  </div>
</template>
<script>
import moment from 'moment';
export default {
  name: 'daterange-search',
  components: {},
  emits: ['update:modelValue'],

  props: {
    modelValue: Array,
    timeType: {
      type: String,
      default: 'date',
    },
    type: {
      type: String,
      default: 'date',
    },
    format: {
      type: String,
      default: 'YYYY-MM-DD',
    },
    valueFormat: {
      type: String,
      default: 'YYYY-MM-DD',
    },
  },
  data() {
    return {
      dateRange: [null, null],
      timeRange: [null, null],
    };
  },
  computed: {},
  watch: {
    modelValue: {
      handler(newVal, oldVal) {
       
        this.dateRange = newVal ;
        if (newVal.length == 0 && this.timeType == 'date') {
          this.dateRange = [null, null];
        }
      },
      immediate: true,
      deep:true
    },
  },
  mounted() {},
  methods: {
    handleChage(val, index,type) {
     
      if(this.timeType == 'date'){
        this.$emit('update:modelValue', this.dateRange);
      }else{
        let data
        console.log(this.timeRange);
        if(!this.timeRange[0] || !this.timeRange[1] || !this.dateRange[0] || !this.dateRange[1] ){
          data = []
        }else{
          data = this.dateRange.map((item,index) => {
          return item.split(' ')[0] + ' ' + this.timeRange[index]
        })
        }
        
        
        
        this.$emit('update:modelValue', data);
      }
     
    },
    handleDateChange(val){
      this.dateRange[1] = val
     
    }
  },
};
</script>
<style lang="" scoped></style>

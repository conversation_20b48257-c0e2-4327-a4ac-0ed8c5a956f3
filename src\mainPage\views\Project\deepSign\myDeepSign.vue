<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      :table-loading="loading"
      ref="crud"
      @search-reset="searchReset"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @keyup.enter="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #stage="{ row, a, b, c }">
        <el-tag effect='plain'>{{ (a, b, c) }}{{ row.$stage }}</el-tag>
      </template>
      <template #menu="{ row, index }">
        <el-button
          type="primary"
          text
          @click="handleDeep(row)"
          v-if="
            (row.deepenAuditStatus == 0 || !row.deepenAuditStatus || row.deepenAuditStatus == 2) &&
            $store.getters.userInfo.user_id == row.deepenPerson
          "
          icon="Edit"
          >{{row.deepenStatus != 3?'深化设计':'修改'}}</el-button
        >
        <!-- <el-button type="primary" text @click="handleAdd(row)" v-else icon="plus"
            >新增方案</el-button
          > -->
        <!-- <el-button type="primary" text @click="submit(row)" icon="check" v-if="row.optionStatus != 2 && $store.getters.userInfo.user_id == row.technicalPersonnel">提交</el-button> -->
        <el-button type="primary" v-if="row.deepenStatus == 2 || row.deepenStatus == 3" text @click="handleView(row)" icon="View">详情</el-button>
      </template>
      <template #deepenAuditStatus="{ row }">
        <span v-if="!row.deepenAuditStatus">---</span>
        <el-tag effect='plain' v-if="row.deepenAuditStatus == 1" size="small" type="info">待主管审核</el-tag>
        <el-tag effect='plain' v-if="row.deepenAuditStatus == 2" size="small" type="success">审核成功</el-tag>
        <el-tooltip
          class="box-item"
          effect="dark"
          :content="row.auditReason"
          placement="top-start"
          v-if="row.deepenAuditStatus == 3"
        >
          <el-tag effect='plain' size="small" type="danger">审核失败</el-tag>
        </el-tooltip>
      </template>
   
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <!-- <el-dialog title="历史记录" v-model="dialogVisible">
      <History ref="history" :id="currentId"></History>
    </el-dialog> -->
    <el-drawer title="深化设计" v-loading="loadingSubmut" element-loading-text="保存数据中" size="90%" :with-header="false" v-model="deepDrawer">
      <deepSign ref="deepSignRef" :height="850" :detail="detail" :id="currentId"></deepSign>
      <template #footer v-if="!detail">
        <el-button @click="deepDrawer = false">取 消</el-button>
        <el-button type="primary" @click="deepSubmit" v-if="currentRow.deepenStatus != 3" plain
          >保存草稿</el-button
        >
        <!-- <el-button type="primary" @click="draft" v-if="isEdit && !props.businessPerson"
          >保存草稿并生成报价单</el-button
        > -->
        <el-button type="primary" @click="deepSubmit('confirm')">保存并提交</el-button>
      </template>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { deepProgrammeStatus, businessOpportunityData, auditStatus } from '@/const/const.js';
import deepSign from '../compoents/deepSign.vue';

let store = useStore();
let permission = computed(() => store.getters.permission);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  index: true,
  searchMenuSpan: 4,
  searchSpan: 4,
  searchLabelWidth: 110,
  menuWidth: 200,
  menu: true,
  border: true,
  column: [
    {
      label: '商机名称',
      prop: 'name',
      width: 250,
      overHidden: true,
      search: true,
    },

    // {
    //   type: 'select',
    //   label: '业务板块',
    //   span: 12,
    //   // search: true,
    //   overHidden: true,
    //   rules: [
    //     {
    //       required: true,
    //       message: '请选择商机关联',
    //     },
    //   ],
    //   display: true,
    //   prop: 'type',
    //   dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
    //   props: {
    //     label: 'dictValue',
    //     value: 'id',
    //   },
    // },
    {
      label: '商机描述',
      prop: 'description',
      overHidden: true,
    },

    // {
    //   label: '商机阶段',
    //   prop: 'stage',
    //   slot: true,
    //   type: 'select',
    //   search: true,
    //   dicData: businessOpportunityData,
    // },
    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true,
    },
    {
      label: '关联联系人',
      prop: 'contactPersonName',
    },
    {
      label: '业务员',
      prop: 'businessPersonName',
    },
    // {
    //   label: '预计销售金额',
    //   prop: 'preSealPrice',
    //   type: 'number',
    //   width: 110,
    // },
    // {
    //   label: '预计签单日期',
    //   prop: 'preSignDate',
    //   format: 'YYYY-MM-DD',
    //   valueFormat: 'YYYY-MM-DD',
    //   type: 'date',
    //   width: 110,
    //   rules: [
    //     {
    //       required: true,
    //       message: '请选择预计签单日期',
    //     },
    //   ],
    // },
    {
      label: '深化状态',
      type: 'select',
      dicData: deepProgrammeStatus,
      prop: 'deepenStatus',
      search: true,
    },
    // {
    //   label: '审核状态',
    //   type: 'select',
    //   width: 110,
    //   dicData: auditStatus,
    //   prop: 'deepenAuditStatus',
    //   search: true,
    // },
    // {
    //   label: '深化人',
    //   prop: 'deepenPersonName',
    // },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const delUrl = '/api/vt-admin/businessOpportunity/remove?ids=';
const tableUrl = '/api/vt-admin/businessOpportunity/needDeepenPage';
let route = useRoute();
let params = ref({
  ids:route.query.ids
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();

onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function searchChange(params, done) {
  onLoad();
  done();
}

// function handleView(row) {
//   if (row.isHasDataJson == 1) {
//     router.push({
//       path: '/CRM/programme/compoents/update',
//       query: {
//         id: row.id,
//         type: 'detail',
//         deep: '1',
//       },
//     });
//   } else if (row.isHasDataJson == 0) {
//     router.push({
//       path: '/CRM/programme/compoents/detail',
//       query: {
//         id: row.id,
//         type: 'detail',
//         deep: '1',
//       },
//     });
//   } else {
//     proxy.$message.error('未查询到方案');
//   }
// }
function handleEdit(row) {
  if (row.isHasDataJson == 1) {
    router.push({
      path: '/CRM/programme/compoents/update',
      query: {
        id: row.id,
        type: 'edit',
        name: row.name,
        businessOpportunityId: row.id,
        deep: '1',
      },
    });
  } else {
    router.push({
      path: '/CRM/programme/compoents/addOld',
      query: {
        id: row.id,
        type: 'edit',
        name: row.name,
        businessOpportunityId: row.id,
        deep: '1',
      },
    });
  }
}
function handleAdd(row) {
  router.push({
    path: '/CRM/programme/compoents/update',
    query: {
      id: row.id,
      type: 'add',
    },
  });
}
function searchReset() {
  params.value.ids = null
  onLoad();
}
function submit(row) {
  proxy
    .$confirm('提交之后将会不可修改，你可以在历史记录查看?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios
        .post('/api/vt-admin/businessOpportunityOption/confirm', {
          id: row.optionId,
        })
        .then(res => {
          proxy.$message.success(res.data.msg);
        });
    });
}
let deepDrawer = ref(false);
let currentId = ref(null);
let currentRow = ref({});
function handleDeep(row) {
  deepDrawer.value = true;
  detail.value = false;
  currentId.value = row.id;
  currentRow.value = row;
}
let loadingSubmut =ref(false)
function deepSubmit(type) {
  let data = proxy.$refs.deepSignRef.getData();
  loadingSubmut.value = true
  if (data.id) {
    
    let url = '/api/vt-admin/businessOpportunityOptionHistory/editDeepenDesign';
    if (currentRow.value.deepenStatus == 3) {
      url = '/api/vt-admin/businessOpportunityOptionHistory/leaderUpdateDeepenDesign';
    }
    axios
      .post(url, {
        ...data,
        optionStatus: type == 'confirm' ? 1 : 0,
      })
      .then(res => {
        proxy.$message.success(res.data.msg);
        proxy.$store.dispatch('getMessageList');
        onLoad();
        proxy.$refs.deepSignRef.getDetail();
        if (type == 'confirm') {
          deepDrawer.value = false;
        }
        loadingSubmut.value = false
        // isEdit.value = false;
        // option.value.detail = true;
      });
  } else {
    data = {
      ...data,
      optionStatus: type == 'confirm' ? 1 : 0,
    };
    axios
      .post('/api/vt-admin/businessOpportunityOptionHistory/saveDeepenDesign', data)
      .then(res => {
        proxy.$message.success(res.data.msg);
        onLoad();
        proxy.$refs.deepSignRef.getDetail();
        if (type == 'confirm') {
          deepDrawer.value = false;
        }
        loadingSubmut.value = false
        // isEdit.value = false;
        // option.value.detail = true;
      });
  }
}
let detail = ref(false);
function handleView(row) {
  // if (row.deepenStatus != 3) {
  //   return proxy.$message.error('只有方案状态为已完成的方案才能查看');
  // }
  currentId.value = row.id;
  detail.value = true;
  deepDrawer.value = true;
}
</script>

<style lang="scss" scoped>
:deep(.el-drawer__body) {
  padding: 10px !important;
}
:deep(.el-drawer__footer) {
  padding-top: 0px;
}
</style>

<template>
  <basic-container shadow="never">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @selection-change="handleChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #isInvoice="{ row }">
        <el-tag
          effect="plain"
          :type="
            row.isInvoice == 0
              ? 'danger'
              : row.isInvoice == 1 || row.isInvoice == 2
              ? 'success'
              : 'info'
          "
          >{{ row.$isInvoice }}</el-tag
        >
      </template>
      <template #contractName="{ row }">
        <el-link type="primary" @click="handleView(row)">{{ row.contractName }}</el-link>
      </template>
      <template #menu-left="{}">
        <div style="display: flex; gap: 20px">
          <el-button type="primary" @click="invoiceApply" icon="plus">申请开票</el-button>
          <el-button
            type="warning"
            plain
            style="margin-left: -5px"
            @click="exportData"
            icon="download"
            >导出</el-button
          >
          <div style="display: flex; align-items: center">
            <span style="font-weight: bolder">总金额：</span>
            <el-text type="primary" size="large"
              >￥{{ (totalPrice * 1).toLocaleString() || 0 }}</el-text
            >
            <span style="font-weight: bolder; margin-left: 10px">数量：</span>
            <el-text type="primary" size="large"
              >￥{{ (number * 1).toLocaleString() || 0 }}</el-text
            >
          </div>
          <div style="display: flex; gap: 20px">
            <el-alert
              type="warning"
              title="选中签订日期和客户以查询对账单"
              :closable="false"
            ></el-alert>
            <el-text size="large" style="white-space: noWrap">当前:</el-text>
            <el-text
              size="large"
              :type="params.customerName ? 'success' : 'warning'"
              style="white-space: noWrap"
              >{{ params.customerName || '未选中客户' }}</el-text
            >
            -
            <el-text
              size="large"
              :type="params.signDate[0] ? 'success' : 'warning'"
              style="white-space: noWrap"
              >{{ params.signDate[0] || '未选中时间' }}</el-text
            >
            ~
            <el-text
              size="large"
              :type="params.signDate[1] ? 'success' : 'warning'"
              style="white-space: noWrap"
              >{{ params.signDate[1] || '未选中结束时间' }}</el-text
            >
          </div>
        </div>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer v-model="drawer" size="90%" title="开票申请">
      <el-row style="height: 100%" :gutter="8">
        <el-col style="height: 100%" :span="10">
          <el-card shadow="never" class="box-card" style="height: 100%">
            <avue-form :option="formOption" ref="addFormRef" @submit="submit" v-model="form">
              <template #customerInvoiceInfoId>
                <wfInvoiceDrop
                  v-model="form.customerInvoiceInfoId"
                  :id="form.customerId"
                ></wfInvoiceDrop>
              </template>
            </avue-form>
          </el-card>
        </el-col>

        <el-col style="height: 100%" :span="14">
          <el-card class="box-card" shadow="never" style="height: 100%">
            <avue-crud
              :option="selectProductOption"
              @row-del="productRowDel"
              :summary-method="getSummaries"
              :data="selectProductList"
            >
              <template #menu-left>
                <!-- <el-button type="primary" icon="plus" @click="addProduct">添加产品</el-button> -->
              </template>
              <template #number="{ row }">
                <el-input-number
                  @change="setTotalAmount"
                  :min="0"
                  size="small"
                  style="width: 80%"
                  v-model="row.number"
                ></el-input-number>
              </template>
            </avue-crud>
          </el-card>
        </el-col>
      </el-row>
      <template #footer>
        <div style="flex: auto">
          <el-button type="primary" @click="$refs.addFormRef.submit()">确认 申请</el-button>
        </div>
      </template>
    </el-drawer>
    <!-- 导出选项对话框 -->
    <el-dialog
      v-model="exportOptionsDialog"
      width="400"
      top="30vh"
      title="导出选项"
      style="border-radius: 10px"
      :show-close="false"
      align-center
    >
      <div style="padding: 20px 0">
        <el-form label-width="120px">
          <el-form-item label="是否体现税金:">
            <el-radio-group v-model="exportOptions.isExportTax">
              <el-radio :label="1">体现税金</el-radio>
              <el-radio :label="0">不体现税金</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <el-alert
          title="当前导出皆按13%税率计算"
          type="info"
          :closable="false"
          show-icon
          style="margin-top: 10px"
        />
      </div>
      <template #footer>
        <div style="text-align: right">
          <el-button @click="exportOptionsDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmExport">确认导出</el-button>
        </div>
      </template>
    </el-dialog>

  
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import { planNameData } from '@/const/const';
import wfInvoiceDrop from '@/views/Contract/customer/compoents/wf-invoice-drop.vue';
import { dateFormat } from '@/utils/date';
import { downloadXls,download } from '@/utils/download';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  selection: true,
  searchMenuSpan: 6,
  searchSpan: 4,
  menuWidth: 270,
  reserveSelection: true,
  searchIcon: true,
  searchIndex: 4,
  menu: false,
  border: true,
  selectable: row => {
    return row.isInvoice != 1 && row.isInvoice != 3;
  },
  column: [
    {
      label: '产品',
      prop: 'customProductName',

      overHidden: true,
      // search: true,
    },

    {
      label: '品牌',
      prop: 'productBrand',

      overHidden: true,
      // search: true,
      span: 24,
      type: 'input',
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',

      overHidden: true,
      search: true,
      component: 'wf-product-drop',
      span: 24,

      type: 'input',
    },
    // {
    //   label: '描述',
    //   prop: 'customProductDescription',

    //   overHidden: true,
    //   // search: true,
    //   span: 24,

    //   type: 'input',
    // },
    {
      label: '单位',
      prop: 'unitName',
      bind: 'product.unitName',
      overHidden: true,
      // search: true,
      width: 70,
      span: 24,
      type: 'input',
    },
    {
      label: '数量',
      prop: 'number',

      width: 70,
      overHidden: true,
      // search: true,
      span: 24,
      type: 'number',
    },
    {
      label: '已开票数量',
      prop: 'invoiceNumber',
    },
    {
      label: '已退货数量',
      prop: 'returnNumber',
      formatter: row => row.returnNumber && parseFloat(row.returnNumber),
    },
    {
      label: '单价',
      prop: 'sealPrice',
      type: 'number',
      width: 100,
      span: 12,
      cell: false,
    },
    {
      label: '金额',
      prop: 'totalPrice',
      type: 'number',
      span: 12,
      width: 120,
      cell: false,
    },
    {
      label: '是否开票',
      prop: 'isInvoice',
      width: 100,
      type: 'select',
      search: true,
      dicData: [
        {
          value: 0,
          label: '未开票',
        },
        {
          value: 1,
          label: '已开票',
        },
        {
          value: 2,
          label: '部分开票',
        },
        {
          value: 3,
          label: '无需开票',
        },
      ],
    },
    {
      label: '关联订单',
      prop: 'contractName',
      search: true,
      overHidden: true,
      width: 250,
    },
    {
      label: '关联客户',
      prop: 'customerId',
      search: true,
      overHidden: true,
      component: 'wf-customer-select',
      params: {
        Url: '/api/vt-admin/customer/page?type=6',
      },
      hide: !!props.customerId,
      width: 250,
      searchOrder: 2,
      formatter: row => {
        return row.customerName;
      },
      change: val => {
        params.value.customerName = '';
        if (!val.value) return;
        getCustomerName(val.value);
      },
    },
    {
      label: '签订日期',
      prop: 'signDate',
      search: true,
      component: 'wf-daterange-search',
      searchSpan: 6,
      width: 150,
      searchOrder: 1,
      overHidden: true,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const props = defineProps(['customerId']);
const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/businessOpportunity/getTransactionProducts';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let number = ref(0);
let totalPrice = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        selectType: 0,
        startTime: params.value.signDate ? params.value.signDate[0] : null,
        endTime: params.value.signDate ? params.value.signDate[1] : null,
        customerId: props.customerId || params.value.customerId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  axios
    .get('/api/vt-admin/businessOpportunity/getTransactionProductsStatistics', {
      params: {
        size,
        current,
        ...params.value,
        selectType: 0,
        startTime: params.value.signDate ? params.value.signDate[0] : null,
        endTime: params.value.signDate ? params.value.signDate[1] : null,
        customerId: props.customerId || params.value.customerId,
      },
    })
    .then(res => {
      number.value = res.data.data.number;
      totalPrice.value = res.data.data.totalPrice;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function handleView(row) {
  router.push({
    path: '/Contract/customer/compoents/detail',
    query: {
      id: row.sealContractId,
    },
  });
}

//   开票申请
let drawer = ref(false);
let innerDrawer = ref(false);
let applyQuery = ref({});
let pageOption = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
let selectProductList = ref([]);
let formOption = ref({
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      label: '客户名称',
      prop: 'customerId',
      overHidden: true,
      span: 24,
      component: 'wf-customer-select',
    },
    {
      type: 'input',
      label: '开票信息',
      span: 24,
      display: true,
      hide: true,
      prop: 'customerInvoiceInfoId',
    },
    {
      label: '计划名称',
      prop: 'planName',
      span: 24,
      type: 'radio',
      dicData: planNameData,

      rules: [
        {
          required: true,
          message: '计划名称必须填写',
        },
      ],
    },
    {
      type: 'date',
      label: '开票日期',
      span: 24,
      width: 100,
      display: false,

      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'invoiceDate',
    },

    {
      type: 'input',
      label: '开票金额',
      width: 110,
      span: 24,
      display: true,
      prop: 'invoicePrice',
    },
    {
      type: 'select',
      label: '开票类型',
      dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
      cascader: [],
      span: 24,
      width: 130,
      // search: true,
      value: '',
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'invoiceType',
      rules: [{ required: true, message: '请选择开票类型', trigger: 'blur' }],
    },
    {
      label: '税率',
      type: 'select',
      span: 24,
      width: 80,
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },
      // change:({value}) => {
      //   totalAmount(value);
      // },
      cell: false,
      prop: 'taxRate',
      dicUrl: '/blade-system/dict/dictionary?code=tax',
    },
    {
      label: '开票公司',
      type: 'select',
      prop: 'billingCompany',

      props: {
        label: 'companyName',
        value: 'id',
      },
      dicFormatter: res => {
        return res.data.records;
      },

      overHidden: true,
      cell: false,
      dicUrl: '/api/vt-admin/company/page?size=100',
    },
    {
      label: '申请人',
      type: 'input',
      value: proxy.$store.getters.userInfo.nick_name,
      prop: 'createName',
      width: 80,
      span: 24,
      readonly: true,
    },
    {
      type: 'date',
      label: '申请时间',
      span: 24,
      display: true,
      format: 'YYYY-MM-DD',
      readonly: true,
      width: 100,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
      valueFormat: 'YYYY-MM-DD',
      prop: 'createTime',
    },
    {
      label: '备注',
      prop: 'remark',
      span: 24,
      type: 'textarea',
      cell: false,
      overHidden: true,
    }
  ],
});

let selectProductOption = ref({
  header: true,
  menu: true,
  editBtn: false,
  viewBtn: false,
  addBtn: false,

  showSummary: true,

  menuWidth: 100,
  border: true,
  selection: false,
  column: [
    {
      label: '产品',
      prop: 'customProductName',

      cell: false,
      overHidden: true,
    },
    {
      label: '品牌',
      prop: 'productBrand',

      cell: false,
      overHidden: true,
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',

      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      width: 150,
      span: 12,
      cell: true,
    },
    {
      label: '单位',
      prop: 'unitName',
      bind: 'product.unitName',
      span: 12,
      width: 80,
    },
    {
      label: '单价',
      prop: 'sealPrice',
      type: 'number',
      span: 12,
      cell: false,
      width: 100,
    },
    {
      label: '金额',
      prop: 'totalPrice',
      type: 'number',
      span: 12,
      width: 100,
      cell: false,
      formatter: row => {
        return row.number * row.sealPrice;
      },
    },
  ],
});
let contractList = ref([]);
let currentContractId = ref('');

function invoiceApply() {
  if (selectList.value.length == 0) {
    return proxy.$message.warning('请选中产品');
  }

  const bol = [...new Set(selectList.value.map(item => item.customerId))].length > 1;

  if (bol) {
    proxy.$message.warning('请选择相同客户的产品');
    return;
  }
  drawer.value = true;
  proxy.$nextTick(() => {
    clearAll();
    selectProductList.value.push(
      ...selectList.value.map(i => {
        return {
          ...i,
          number: i.number * 1 - i.invoiceNumber * 1,
          product: {
            ...i.product,
          },
        };
      })
    );
    setTotalAmount();
    form.value.customerId = selectProductList.value[0].customerId;
  });
}

let productList = ref([]);
let selectLoading = ref(false);

let selectList = ref([]);
function handleChange(list) {
  selectList.value = list.map(i => i);
}
let sealContractId = ref([]);
function confirmAddProduct() {}
function productRowDel(row) {
  proxy
    .$confirm('确认删除此产品吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      selectProductList.value = selectProductList.value.filter(item => item.id !== row.id);
      setTotalAmount();
    })
    .catch(() => {});
}
function setTotalAmount() {
  const totalPrice = selectProductList.value.reduce(
    (total, item) => (total += item.number * 1 * (item.sealPrice * 1)),
    0
  );
  form.value.invoicePrice = totalPrice.toFixed(2);
}
function submit(form, done, loading) {
  if (selectProductList.value.length == 0) {
    proxy.$message.error('请选择产品');
    done();
    return;
  }
  form.detailDTOList = selectProductList.value.map(item => {
    return {
      detailId: item.id,
      ...item,
      id: null,
    };
  });
  form.sealContractId = selectProductList.value
    .reduce((pre, cur) => {
      if (pre.includes(cur.sealContractId)) return pre;
      return [...pre, cur.sealContractId];
    }, [])
    .join(',');
  form.createTime = null;
  axios.post('/api/vt-admin/sealContractInvoice/save', form).then(res => {
    if (res.data.code == 200) {
      proxy.$message.success(res.data.msg);
      done();
      onLoad();
      drawer.value = false;
      clearAll();
      proxy.$refs.crud.toggleSelection();
    }
  });
}
function clearAll() {
  proxy.$refs.addFormRef.resetForm();
  contractList.value = [];
  selectProductList.value = [];
  form.value.createName = proxy.$store.getters.userInfo.nick_name;
  form.value.createTime = dateFormat(new Date(), 'yyyy-MM-dd');
  form.value.customerId = props.customerId;
  pageOption.value.currentPage = 1;
  pageContract.value.current = 1;
}
let pageContract = ref({
  current: 1,
  size: 20,
});
function getCustomerName(id) {
  axios
    .get('/api/vt-admin/customer/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      params.value.customerName = res.data.data.customerName;
      ;
    });
}
function reset() {
  params.value.customerName = '';
  onLoad();
}

let exportOptionsDialog = ref(false);
let exportOptions = ref({
  isExportTax: 1 // 默认体现税金
});

let headerDialog = ref(false);
let headerTableData = ref([
  {
    id: 'customerOrderNumber',
    label: '客户订单号',
  },
  {
    id: 'customProductName',
    label: '产品名称',
  },
  {
    id: 'customProductSpecification',
    label: '规格型号',
  },
  {
    id: 'productBrand',
    label: '品牌',
  },
  {
    id: 'customUnit',
    label: '单位',
  },
  {
    id: 'number',
    label: '数量',
  },
  {
    id: 'zhhsdj',
    label: '单价',
  },
  {
    id: 'zhhsze',
    label: '金额',
  },
  {
    id: 'isInvoice',
    label: '开票状态',
  },
  {
    id: 'customerName',
    label: '客户名称',
  },

  {
    id: 'signDate',
    label: '签订日期',
  },
  {
    id: 'outDate',
    label: '出货日期',
  },
]);
function exportData() {
  // 验证是否选择了客户
  if (!params.value.customerName && !params.value.customerId && !props.customerId) {
    proxy.$message.warning('请先选择客户');
    return;
  }

  // 验证是否选择了签订时间
  if (!params.value.signDate || !params.value.signDate[0] || !params.value.signDate[1]) {
    proxy.$message.warning('请先选择签订时间');
    return;
  }

  // 打开导出选项对话框
  exportOptionsDialog.value = true;
}

function confirmExport() {
  // 关闭选项对话框
  exportOptionsDialog.value = false;

  // 根据选择的税金选项调用相应的导出API
  const apiParams = {
    ...params.value,
    selectType: 0,
    startTime: params.value.signDate ? params.value.signDate[0] : null,
    endTime: params.value.signDate ? params.value.signDate[1] : null,
    isExportTax: exportOptions.value.isExportTax
  };

  download('/api/vt-admin/businessOpportunity/downStatementAccount', apiParams);
}


const getSummaries = param => {
  const { columns, data } = param;
  const sums = [];
  
  columns.forEach(item => {
    if (item.property == 'totalPrice') {
      const value = data.reduce((pre, cur) => {
        pre += cur.sealPrice * cur.number;
        return pre;
      }, 0);
      sums.push(value.toLocaleString());
    } else {
      sums.push('');
    }
  });
  return sums;
};

</script>

<style lang="scss" scoped></style>

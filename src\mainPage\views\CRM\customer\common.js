import { followData } from '@/const/const';
// import axios from '@/axios';
let baseUrl = '/api/blade-system/region/lazy-tree';
export default [
  {
    label: '客户编号',
    prop: 'customerCode',
    overHidden: true,
    align: 'center',
    width: 150,
  },
  {
    label: '客户名称',
    prop: 'customerName',
    search: true,
    searchSpan: 5,
    component: 'wf-customer-drop',
    width: 300,
    searchOrder: 9,
  },
  {
    label: '联系人',
    prop: 'contactPerson',
    search: true,
    searchLabelWidth: 80,
    searchOrder: 8,
    searchSpan: 5,
  },
  {
    label: '联系方式',
    prop: 'contactPhone',
    search: true,
    width: 110,
    searchOrder: 7,
    searchSpan: 5,
  },
  {
    label: '未跟进时长',
    prop: 'time',
    searchLabelWidth: 110,
    searchOrder: 6,
    search: false,
    searchSpan: 5,
    hide: true,
    showColumn: true,
  },
  // {
  //   label: '是否设置标签',
  //   type: 'select',
  //   search: true,
  //   hide: true,
  //   searchOrder: 5,
  //   searchLabelWidth: 110,
  //   searchSpan: 4,
  //   dicData: [
  //     {
  //       label: '是',
  //       value: 1,
  //     },
  //     {
  //       label: '否',
  //       value: 0,
  //     },
  //   ],
  // },

  {
    label: '所在区域',
    prop: 'province_city_area',
    search: true,
    hide: true,
    searchSpan: 5,
    type: 'cascader',
    props: {
      label: 'title',
      value: 'id',
    },
    lazy: true,
    lazyLoad(node, resolve) {
      let stop_level = 2;
      let level = node.level;
      let data = node.data || {};
      let id = data.id;
      let list = [];
      let callback = () => {
        resolve(
          (list || []).map(ele => {
            return Object.assign(ele, {
              leaf: level >= stop_level || !ele.hasChildren,
            });
          })
        );
      };
      axios.get(`${baseUrl}?parentCode=${id || '00'}`).then(res => {
        list = res.data.data;
        callback();
      });
    },
  },
  // {
  //   label: '商机/项目',
  //   type: 'select',
  //   search: true,
  //   searchSpan: 5,
  //   // searchLabelWidth: 110,
  //   hide: true,
  //   dicData: [
  //     {
  //       label: '商机',
  //       value: 0,
  //     },
  //     {
  //       label: '项目',
  //       value: 1,
  //     },
  //   ],
  // },
  // {
  //   label: '跟进记录',
  //   search: true,
  //   hide: true,
  //   searchSpan: 5,
  //   searchLabelWidth: 110,
  // },

  {
    label: '客户行业',

    props: {
      label: 'dictValue',
      value: 'id',
      desc: 'desc',
    },
    overHidden: true,
    rules: [
      {
        required: true,
        message: '请选择所属行业',
      },
    ],
    prop: 'industry',
    dicUrl: '/blade-system/dict/dictionary?code=industry',
    remote: false,
    type:'select',
    search: true,
  },
  {
    type: 'select',
    label: '客户级别',
    cascader: [],
    overHidden: true,
    rules: [
      {
        required: true,
        message: '请选择客户级别',
      },
    ],
    searchSpan: 5,
    span: 12,
    display: true,
    props: {
      label: 'dictValue',
      value: 'id',
      desc: 'desc',
    },
    prop: 'customerLevel',
    dicUrl: '/blade-system/dict/dictionary?code=customerLevel',
    remote: false,
    search: true,
  },
  {
    type: 'select',
    label: '跟进状态',
    cascader: [],
    span: 12,
    display: true,
    props: {
      label: 'label',
      value: 'value',
    },
    // slot: true,
    prop: 'followStatus',
    dicData: followData,
    remote: false,
    search: true,
    searchSpan: 4,
    searchOrder: 4,
  },
  {
    type: 'select',
    label: '客户来源',
    cascader: [],
    span: 12,
    display: true,
    overHidden: true,
    props: {
      label: 'dictValue',
      value: 'id',
      desc: 'desc',
    },

    prop: 'customerSource',
    dicUrl: '/blade-system/dict/dictionary?code=customer_source',
    remote: false,
  },
  {
    label: '业务员',
    prop: 'businessPersonName',
    component: 'wf-user-drop',
    search: true,
    // type:'select',
    // filterable: true,
    searchSpan: 5,
  },
  // {
  //   label: '所属部门',
  //   overHidden: true,
  //   prop: 'deptName',
  // },
  {
    label: '推荐人',
    prop: 'referrer',
  },
  {
    type: 'radio',
    label: '是否账期',
    cascader: [],
    rules: [
      {
        required: true,
        message: '请选择账期',
      },
    ],
    span: 12,
    display: true,
    props: {
      label: 'dictValue',
      value: 'id',
      desc: 'desc',
    },
    prop: 'isPaymentPeriod',
    dicUrl: '/blade-system/dict/dictionary?code=isPaymentPeriod',
    remote: false,
  },
];

{"name": "saber", "version": "3.1.0", "scripts": {"dev": "node --max_old_space_size=8192 node_modules/vite/bin/vite.js --host", "build:test": "node --max_old_space_size=8192 node_modules/vite/bin/vite.js build --mode test", "build:prod": "node --max_old_space_size=8192 node_modules/vite/bin/vite.js build --mode production", "serve": "vite preview --host", "postinstall": "patch-package"}, "dependencies": {"@element-plus/icons-vue": "^2.0.9", "@nutflow/nf-design-elp": "^1.0.5", "@nutflow/nf-form-design-elp": "1.0.2", "@nutflow/nf-form-elp": "^1.0.4", "@saber/nf-design-base-elp": "^1.0.0", "@smallwei/avue": "~3.2.21", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "animate.css": "^4.1.1", "avue-plugin-ueditor": "^1.0.3", "axios": "^0.21.1", "crypto-js": "^4.1.1", "dayjs": "^1.10.6", "echarts": "^5.4.2", "element-plus": "2.3.1", "exceljs": "^4.4.0", "express": "^4.21.0", "file-saver": "^2.0.5", "FileSaver": "^0.10.0", "js-base64": "^3.7.4", "js-cookie": "^3.0.0", "js-md5": "^0.7.3", "mockjs": "^1.1.0", "moment": "^2.29.4", "nprogress": "^0.2.0", "qrcode": "^1.5.4", "stompjs": "^2.3.3", "vant": "^4.9.7", "vite-plugin-mock": "^2.9.4", "vue": "~3.2.40", "vue-cropper": "^0.6.5", "vue-draggable-next": "^2.2.1", "vue-i18n": "^9.1.9", "vue-router": "^4.1.5", "vue-weui-next": "^1.0.20", "vue3-print-nb": "^0.1.4", "vuedraggable": "^4.1.0", "vuex": "^4.0.2", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@vitejs/plugin-vue": "^1.3.0", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/compiler-sfc": "^3.0.5", "patch-package": "^8.0.0", "prettier": "^2.8.7", "sass": "^1.37.5", "sortable": "^2.0.0", "unplugin-auto-import": "^0.11.2", "vite": "4.3.9", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-setup-extend": "^0.4.0"}}
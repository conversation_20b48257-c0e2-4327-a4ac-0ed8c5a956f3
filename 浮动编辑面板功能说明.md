# 浮动编辑面板功能说明

## 功能概述

在CRM系统的项目报价编辑页面中，新增了一个可拖动的浮动编辑面板，用于快速编辑当前选中产品行的详细信息。

## 主要特性

### 1. 触发方式
- 点击产品表格中的任意产品行即可打开浮动编辑面板
- 面板会自动显示在屏幕右侧

### 2. 可拖动设计
- 面板标题栏支持鼠标拖拽
- 可以将面板拖动到屏幕的任意位置
- 拖拽时鼠标样式会变为抓取状态
- 面板位置会自动限制在屏幕可视区域内

### 3. 折叠功能
- 点击标题栏的折叠按钮可以收起/展开面板
- 折叠时面板只显示标题栏，节省屏幕空间
- 折叠状态有平滑的动画过渡效果
- 折叠时调整大小控制点会自动隐藏

### 4. 调整大小功能
- 右边缘：水平调整宽度
- 底边缘：垂直调整高度
- 右下角：同时调整宽度和高度
- 最小尺寸限制：宽度300px，高度400px
- 最大尺寸限制：宽度800px，高度为屏幕高度-100px
- 调整大小时面板位置会自动调整以保持在屏幕内

### 5. 键盘导航功能
- **左箭头键 (←)**：切换到上一个产品
- **右箭头键 (→)**：切换到下一个产品
- 支持循环导航，自动跳过分类行
- 切换时会自动滚动到对应产品位置
- 面板标题显示当前产品位置（如：2/10）

### 6. 视觉提示功能
- **当前编辑产品高亮**：正在编辑的产品行有蓝色背景和左边框
- **导航按钮**：面板标题栏显示上一个/下一个按钮
- **键盘提示**：面板内显示键盘快捷键使用提示
- **产品计数**：显示当前产品在总产品中的位置

### 7. 产品插入功能
- **插入空产品**：点击标题栏的加号按钮在当前产品下方插入空行
- **自动切换**：插入后自动切换到新插入的产品进行编辑
- **智能定位**：新产品会插入到当前产品的同一分类中
- **权限控制**：只在编辑模式下可用，详情模式下按钮禁用

### 8. 编辑功能
面板中包含以下可编辑字段：
- **基本信息**
  - 产品名称
  - 品牌
  - 规格型号
  - 产品描述
  - 单位
  - 数量

- **价格信息**
  - 设备单价
  - 人工单价
  - 延保单价
  - 其他单价

- **成本信息**
  - 设备成本单价
  - 人工成本单价
  - 延保成本单价
  - 其他成本单价

- **其他**
  - 备注

### 9. 界面设计
- 现代化的卡片式设计
- 清晰的标题栏和关闭按钮
- 响应式表单布局
- 自定义滚动条样式
- 阴影效果增强视觉层次

### 10. 数据同步
- 面板中的数据修改会实时同步到表格
- 支持数字类型的精确输入（保留2位小数）
- 表单验证确保数据有效性

## 技术实现

### 核心组件
- Vue 3 Composition API
- Element Plus UI组件库
- 响应式数据绑定

### 关键功能
1. **拖拽实现**
   ```javascript
   function startDrag(event) {
     // 记录拖拽起始位置
     // 绑定鼠标移动和释放事件
   }
   
   function onDrag(event) {
     // 计算新位置并限制在屏幕范围内
   }
   ```

2. **面板显示控制**
   ```javascript
   function setReferPrice(item) {
     currentProduct.value = item;
     showEditPanel.value = true;
   }
   ```

3. **折叠功能**
   ```javascript
   function toggleCollapse() {
     isCollapsed.value = !isCollapsed.value;
   }
   ```

4. **调整大小实现**
   ```javascript
   function startResize(type, event) {
     isResizing.value = true;
     resizeType.value = type; // 'right', 'bottom', 'corner'
     // 记录起始位置和尺寸
   }

   function onResize(event) {
     // 根据鼠标移动距离计算新尺寸
     // 限制最小最大值
   }
   ```

5. **键盘导航实现**
   ```javascript
   function handleKeyDown(event) {
     if (event.key === 'ArrowLeft') {
       switchToPreviousProduct();
     } else if (event.key === 'ArrowRight') {
       switchToNextProduct();
     }
   }

   function updateProductList() {
     // 收集当前模块的所有产品
     allProducts.value = [];
     tableData.value.forEach(category => {
       allProducts.value.push(...category.productList);
     });
   }
   ```

6. **产品高亮实现**
   ```javascript
   function isCurrentEditingProduct(product) {
     return showEditPanel.value &&
            currentProduct.value &&
            currentProduct.value.uuid === product.uuid;
   }
   ```

7. **插入空产品实现**
   ```javascript
   function insertEmptyProduct() {
     const newProduct = {
       detailType: 0,
       source: 3,
       classify: currentProduct.value.classify,
       uuid: randomLenNum(10),
       // ... 其他默认字段
     };

     // 在当前产品后插入
     const currentIndex = currentData.detailDTOList.findIndex(
       item => item.uuid === currentProduct.value.uuid
     );
     currentData.detailDTOList.splice(currentIndex + 1, 0, newProduct);

     // 更新界面并切换到新产品
     setTableData();
     updateProductList();
   }
   ```

8. **样式计算**
   ```javascript
   const panelStyle = computed(() => ({
     position: 'fixed',
     left: panelPosition.value.x + 'px',
     top: panelPosition.value.y + 'px',
     width: panelSize.value.width + 'px',
     height: isCollapsed.value ? '50px' : panelSize.value.height + 'px',
     zIndex: 9999,
     transition: isCollapsed.value ? 'height 0.3s ease' : 'none'
   }));
   ```

## 使用说明

1. **打开面板**：点击产品表格中的任意产品行
2. **编辑数据**：在面板中修改相应字段
3. **拖动面板**：按住标题栏拖动到合适位置
4. **折叠面板**：点击标题栏的折叠按钮收起/展开
5. **调整大小**：
   - 拖拽右边缘调整宽度
   - 拖拽底边缘调整高度
   - 拖拽右下角同时调整宽度和高度
6. **键盘导航**：
   - 按左箭头键切换到上一个产品
   - 按右箭头键切换到下一个产品
   - 或点击标题栏的导航按钮
7. **插入产品**：点击标题栏的加号按钮在当前产品下方插入空行
8. **关闭面板**：点击右上角的关闭按钮

## 注意事项

- 面板只在非详情模式和非对话框模式下可编辑
- 数字输入框支持小数点后2位精度
- 面板位置和大小会自动保存在当前会话中
- 关闭面板时会重置产品行的展开状态和折叠状态
- 调整大小时会自动限制最小和最大尺寸
- 拖拽和调整大小操作不会相互干扰
- 键盘导航只在面板打开时生效
- 当前编辑的产品会在表格中高亮显示
- 切换产品时会自动滚动到对应位置
- 插入产品功能只在编辑模式下可用
- 面板默认大小调整为460x720px，提供更好的编辑体验

## 兼容性

- 支持现代浏览器（Chrome、Firefox、Safari、Edge）
- 响应式设计，适配不同屏幕尺寸
- 触摸设备友好的交互设计

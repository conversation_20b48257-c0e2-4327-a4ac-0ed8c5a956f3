<template>
  <basic-container style="height: 100%">
    <div class="header">
      <div>
        <h3 v-if="form.type == 0" style="margin-bottom: 10px" class="title">
          {{ form.projectName }}
        </h3>
        <div v-else style="display: flex; justify-content: space-between; align-items: center">
          <h3 style="margin-bottom: 10px; font-size: 18px" class="title">{{ form.projectName }}</h3>
          <div>
            <el-button type="primary" @click="openDetail" plain>查看方案</el-button>
          <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        >
          </div>
        </div>

        <div class="right"></div>
        <el-form inline label-position="top">
          <el-form-item label="项目总额">
            <el-tag effect="plain" size="large">{{ form.contractTotalPrice || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="原项目总额" v-if="form.changePrice > 0">
            <el-tag effect="plain" size="large">{{ form.oldContractTotalPrice || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="签证总额" v-if="form.changePrice > 0">
            <el-tag effect="plain" size="large">{{ form.changePrice || '---' }}</el-tag>
          </el-form-item>

          <el-form-item label="项目进度">
            <el-tag effect="plain" size="large">{{ form.projectSchedule || `---` }}</el-tag>
          </el-form-item>
          <el-form-item label="项目经理">
            <el-tag effect="plain" size="large">{{ form.technologyUserName || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="业务">
            <el-tag effect="plain" size="large">{{ form.businessUserName || '---' }}</el-tag>
            <!-- <el-icon style="cursor: pointer; margin-left: 5px" @click="edit">
              <Edit></Edit>
            </el-icon> -->
          </el-form-item>
          <el-form-item label="售前技术">
            <el-tag effect="plain" size="large">{{ form.preSaleTechnology || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="协助人员">
            <el-tag effect="plain" size="large">{{
              form.technologyAssistUserName || '---'
            }}</el-tag>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="baseInfo">
        <div>
          <BaseInfo
            :form="form"
            v-loading="loading"
            :isEdit="isEdit"
            @getDetail="getDetail"
          ></BaseInfo>
        </div>
      </el-tab-pane>
      <el-tab-pane :label="item.label" :name="item.name" v-for="item in tabArray" :key="item.name">
      </el-tab-pane>
    </el-tabs>
    <component
      :is="tabArray.find(item => item.name == activeName).component"
      v-if="conpletArr.includes(activeName) && activeName != 'baseInfo'"
      :id="form.businessOpportunityId"
      :detail="true"
      :humanInfo="{
        humanIds: form.humanIds,
        humanResourceEntities: form.humanResourceEntities,
        technologyUserInfo: { id: form.projectLeader, name: form.technologyUserName },
        interiorUsers: form.interiorUsers,
        interiorUserList: form.interiorUserList,
      }"
      @success="getDetail"
      :projectPrice="form.projectPrice"
      :isView="true"
      :deepenStatus="form.deepenStatus"
      :projectId="form.id"
      :projectName="form.projectName"
      :selectType="route.query.type == 0 ? 0 : 1"
      :customerId="form.customerId"
      :sealContractId="form.sealContractId"
      :isCompletePlan="form.isCompletePlan"
      :startDate="form.startDate"
      :isNeedPurchase="form.isNeedPurchase"
      :height="500"
    ></component>
    <el-empty v-if="!conpletArr.includes(activeName) && activeName != 'baseInfo'"></el-empty>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer
      title="方案详情"
      width="90%"
      size="90%"
      overflow
      top="0vh"
      append-to-body
      v-model="dialogVisible"
      style="height: 100vh"
    >
      <div style="background-color: var(--el-color-info-light-9)">
        <programmeDetail
          :businessOpportunityId="form.businessOpportunityId"
          :dialogView="true"
        ></programmeDetail>
      </div>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, shallowRef, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import programmeDetail from '@/views/CRM/programme/compoents/updateVersion3.vue';
import { useStore } from 'vuex';

import BaseInfo from './baseInfo.vue';
// 干系人
import Contact from './contact.vue';
// 项目计划
import ProjectPlan from './projectPlan.vue';
// 项目费用
import Cost from './cost.vue';
// 项目文档
import ProjectFile from './projectFile.vue';
// 项目工时
import ProjectHours from './projectHours.vue';
// 材料清单表
import ProjectProduct from './projectProduct.vue';
// 项目请购
import projectPurchase from './projectPurchase.vue';
// 项目签证
import projectChange from './projectChange.vue';
// 项目报警
import ProjectWarn from './projectWarn.vue';
// 深化设计
import ProjectDeep from '../compoents/deepSign.vue';
let route = useRoute();
let router = useRouter();
let form = ref({});
let isEdit = ref(false);
let loading = ref(false);

watchEffect(() => {
  if (route.query.id && route.fullPath.indexOf('/Project/detail/detail') > -1) {
    getDetail();
  }
});
let { proxy } = getCurrentInstance();
const roleArr = [
  'project:contact',
  'project:deep',
  'project:plan',
  'project:hours',
  'project:product',
  'project:change',
  'project:purchase',
  'project:cost',
  'project:file',
];
const conpletArr = [
  'Contact',
  'ProjectDeep',
  'ProjectPlan',
  'Cost',
  'ProjectFile',
  'ProjectHours',
  'ProjectProduct',
  'projectChange',
  'projectPurchase',

  'ProjectWarn',
];
console.log(conpletArr, 'conpletArr');
const tabArray = [
  {
    label: '干系人',
    name: 'Contact',
    component: Contact,
  },
  {
    label: '深化设计',
    name: 'ProjectDeep',
    component: ProjectDeep,
  },
  {
    label: '项目计划',
    name: 'ProjectPlan',
    component: ProjectPlan,
  },
  {
    label: '项目工时',
    name: 'ProjectHours',
    component: ProjectHours,
  },
  {
    label: '材料清单表',
    name: 'ProjectProduct',
    component: ProjectProduct,
  },
  {
    label: '项目签证',
    name: 'projectChange',
    component: projectChange,
  },
  {
    label: '请购单',
    name: 'projectPurchase',
    component: projectPurchase,
  },

  // {
  //   label: '人力资源库',
  //   name: 'follow',
  //   component: BaseInfo,
  // },
  {
    label: '项目费用',
    name: 'Cost',
    component: Cost,
  },
  // {
  //   label: '项目分析',
  //   name: 'Process',
  //   component: BaseInfo,
  // },
  // {
  //   label: '项目总结',
  //   name: 'Process',
  //   component: BaseInfo,
  // },
  {
    label: '项目文档',
    name: 'ProjectFile',
    component: ProjectFile,
  },
  {
    label: '项目报警',
    name: 'ProjectWarn',
    component: ProjectWarn,
  },
].filter((item, index) => {
  console.log(proxy.$store.getters.permission);

  if (['Contact', 'projectChange', 'ProjectFile'].includes(item.name)) {
    return proxy.$store.getters.permission[roleArr[index]];
  } else {
    return (
      proxy.$store.getters.permission[roleArr[index]] &&
      (route.query.type != 0 || route.query.isMy != 1)
    );
  }
});
console.log(tabArray, 'tabArray');
onMounted(() => {
  getDetail();
});
function getDetail() {
  isEdit.value = false;
  loading.value = true;
  axios
    .get('/api/vt-admin/project/detail', {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
      loading.value = false;
      const { provinceCode, cityCode, areaCode } = res.data.data;
      form.value = {
        ...res.data.data,
      };
      console.log(form.value, 'form.value');
      
    });
}

let currentIndex = ref(0);

function handleClick(value) {
  currentIndex.value = value;
  // currentCompoent.value = compoentArr[value];
}

const store = useStore();
const tag = computed(() => store.getters.tag);
const activeName = ref('baseInfo');

function openDetail() {
  console.log(form.value);
  dialogVisible.value = true;
  // router.push({
  //   path: '/CRM/programme/compoents/updateVersion3',
  //   query: {
  //     id: form.value.businessOpportunityId,
  //     type: 'detail',
  //     name: form.value.businessOpportunityName,
  //     // businessOpportunityId: row.businessOpportunityId,
  //   },
  // });
}
let dialogVisible = ref(false);
</script>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/common/var.scss' as *;
.header {
  // display: flex;
  margin-left: 20px;
  justify-content: space-between;
  align-items: flex-start;
}

.title {
  margin: 0;
  margin-right: 20px;
}
.left_content {
  .main_box {
    margin-right: 10px;
    .item {
      width: 100px;
      cursor: pointer;
      background-color: #fff;
      box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.1);
      text-align: center;
      line-height: 50px;
      font-weight: bolder;
      height: 50px;
      font-size: 12px;
      margin-bottom: 10px;
      transition: all 0.2s;
      transition: all 0.2s;
      position: relative;
    }
    .item.active {
      box-shadow: inset 5px 5px 10px rgba(0, 0, 0, 0.1);
      color: $color-primary;
      .arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -12px;
        border: 6px solid transparent;
        border-left-color: $color-primary;
        height: 0;
        width: 0;
      }
    }
  }
}
</style>

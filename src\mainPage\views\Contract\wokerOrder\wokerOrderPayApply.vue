<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      :permission="permission"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #payStatus="{ row }">
        <el-tag
          :type="row.payStatus == 0 ? priamry : row.payStatus == 3 ? 'danger' : 'success'"
          effect="plain"
          >{{ row.$payStatus }}</el-tag
        >
      </template>
      <template #objectName="{ row }">
        <el-link type="primary" @click="openDetailDrawer(row)">{{ row.objectName }}</el-link>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <wokerOrderSelect
      ref="wokerOrderSelectRef"
      :sealContractId="form.sealContractId"
      @onConfirm="handlewokerOrderSelectConfirm"
    ></wokerOrderSelect>
  </basic-container>
  <el-drawer v-model="drawerVisible" title="工单详情" size="60%">
    <avue-form :option="detailOption" v-model="detailData">
      <template #filesDetail>
        <File :fileList="detailData.fileList"></File>
      </template>
    </avue-form>
  </el-drawer>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import wokerOrderSelect from './component/wokerOrderSelect.vue';
let wokerOrderSelectRef = ref(null);
const detailOption = {
  labelWidth: 100,
  menuBtn: false,
  detail: true,
  labelWidth: 120,
  column: [
    {
      label: '工单名称',
      prop: 'objectName',
      placeholder: '请输入工单名称',
      span: 12,
      type: 'input',
    },
    {
      type: 'datetime',
      label: '服务时间',
      span: 12,

      width: 140,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      prop: 'planTime',
      rules: [
        {
          required: true,
          message: '请选择服务时间',
          trigger: 'blur',
        },
      ],
    },
    // {
    //   label: '工单需求',
    //   type: 'dynamic',
    //   span: 24,

    //   children: {
    //     label: '工单需求',
    //     prop: 'serviceDemandList',

    //     placeholder: '请输入工单需求',
    //     type: 'form',
    //     index: false,
    //     column: [

    //     ],
    //   },
    // },
    {
      label: '工单类型',
      prop: 'lables',
      placeholder: '请输入工单类型',
      type: 'tree',
      props: {
        value: 'id',
        label: 'dictValue',
      },
      dicUrl: '/blade-system/dict-biz/dictionary?code=wokerOrderType',
    },
    // {
    //   label: '工单数量',
    //   prop: 'serviceDemandNum',
    //   placeholder: '请输入工单数量',
    //   type: 'input',
    //   span: 12,
    // },
    {
      label: '工单价格',
      prop: 'orderPrice',
      placeholder: '请输入工单价格',
      type: 'number',
      span: 12,
    },
    {
      label: '工单描述',
      prop: 'objectRemark',
      placeholder: '请输入工单描述',
      type: 'textarea',
      span: 24,
    },

    {
      label: '工单附件',
      prop: 'filesDetail',
      type: 'upload',

      value: [],
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 12,
      slot: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      label: '指派工程师',
      prop: 'externalHandleUser',
      component: 'wf-user-select',
      rules: [
        {
          required: true,
          message: '请选择工程师',
          trigger: 'blur',
        },
      ],
    },
    // {
    //   label: '工单价格',
    //   prop: 'orderPrice',
    //   type: 'number',
    //   component: 'el-input',
    //   rules: [
    //     {
    //       required: true,
    //       message: '请填写工单价格',
    //       trigger: 'blur',
    //     },
    //   ],
    // },
    {
      label: '服务客户名称',
      prop: 'finalCustomer',
      span: 24,
    },
    {
      label: '服务联系人',
      type: 'input',
      prop: 'contact',

      placeholder: '请先选择报价',
      // disabled: true,
      params: {},
    },
    {
      type: 'input',
      label: '服务联系电话',
      span: 12,
      display: true,
      prop: 'contactPhone',
    },
    {
      type: 'input',
      label: '服务地址',
      span: 24,
      display: true,
      prop: 'distributionAddress',
      dicData: [],
    },
    // {
    //   label: '工单描述',
    //   prop: 'objectRemark',
    //   type: 'textarea',
    //   span: 24,
    //   rules: [
    //     {
    //       required: true,
    //       message: '请填写工单描述',
    //       trigger: 'blur',
    //     },
    //   ],
    // },
  ],
};

const detailData = ref({});
const drawerVisible = ref(false);

let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  detailBtn: true,

  dialogWidth: '500',
  //   dialogType: 'drawer',
  border: true,
  column: [
    {
      label: '关联合同',
      prop: 'contractName',
      search: true,
      addDisplay:false,
      editDisplay:false,
    },
    {
      label: '关联工单',
      prop: 'objectName',
      type: 'input',
      readonly: true,
     
      span: 24,
      placeholder: '请选择一个工单',
      click: row => {
        wokerOrderSelectRef.value.dialogVisible = true;
      },
    },
    // {
    //   label: '工单处理人',
    //   prop: 'handleUserName',
    //   type: 'input',
    //   span: 24,
    //   hide: true,
    // },
    {
      label: '付款金额',
      prop: 'totalPrice',
      type: 'number',
      span: 24,
    },
    {
      label: '申请备注',
      prop: 'applyContent',
      span: 24,
      type: 'textarea',
    },
    {
      label: '状态',
      prop: 'payStatus',
       type:'select',
      display: false,
      search: true,
      dicData: [
        {
          value: 0,
          label: '待审核',
        },
        {
          value: 1,
          label: '审核通过',
        },
        {
          value: 2,
          label: '已付款',
        },
        {
          value: 3,
          label: '审核失败',
        },
      ],
    },
    {
      label: '审核备注',
      prop: 'auditRemark',
      overHidden: true,
      display:false
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/sealContractObjectPayment/save';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContractObjectPayment/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function openDetailDrawer(row) {
  axios
    .get('/api/vt-admin/sealContractObject/detail', {
      params: {
        id: row.objectId,
      },
    })
    .then(res => {
      detailData.value = res.data.data;

      drawerVisible.value = true;
    });
}

function handleDetailSubmit(form, done) {
  // 此处可添加提交逻辑
  proxy.$message.success('详情已更新');
  done();
  drawerVisible.value = false;
  onLoad(); // 刷新列表
}

function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
         selectType:0,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();
function handlewokerOrderSelectConfirm(data) {
  const { id, handleUserName, orderPrice, objectName } = data[0];
  form.value.objectId = id;
  form.value.objectName = objectName;
  form.value.handleUserName = handleUserName;
  form.value.totalPrice = orderPrice;
}
function permission(key, row) {
  console.log(key, row);

  if (['editBtn', 'delBtn'].includes(key)) {
    if (row.payStatus == 0) {
      return true;
    } else {
      return false;
    }
  } else {
    return true;
  }
}
function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
</script>

<style lang="scss" scoped></style>

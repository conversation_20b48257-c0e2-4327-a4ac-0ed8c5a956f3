<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left="{}">
        <el-button
          type="warning"
          @click="exportData"
          icon="download"
          v-if="$store.getters.permission.invoice_export"
          plain
          >导出</el-button
        >
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">发票总额：</el-text>
        <el-text type="primary" size="large">￥{{ (totalPrice * 1).toLocaleString() }}</el-text>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">不含税总额：</el-text>
        <el-text type="primary" size="large">￥{{ (noTaxPrice * 1).toLocaleString() }}</el-text>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">税额总额：</el-text>
        <el-text type="primary" size="large">￥{{ (taxPrice * 1).toLocaleString() }}</el-text>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">已开票总额：</el-text>
        <el-text type="primary" size="large"
          >￥{{ (hasInvoicePrice * 1).toLocaleString() }}</el-text
        >
      </template>
      <template #menu="{ row, $index }">
        <el-button type="primary" icon="view" text @click="viewDetail(row, $index)">详情</el-button>
        <el-button
          type="primary"
          icon="pointer"
          v-if="row.invoiceStatus == 0"
          text
          @click="invoice(row)"
          >开票</el-button
        >
        <el-button
          type="primary"
          icon="Position"
          text
          @click="send(row)"
          v-if="row.invoiceStatus == 1"
          >邮寄</el-button
        >
        <el-button
          type="primary"
          icon="edit"
          text
          @click="invoice(row, true)"
          v-if="row.invoiceStatus != 0 && row.invoiceStatus != 3"
          >编辑</el-button
        >
        <el-button
          type="primary"
          icon="pointer"
          text
          @click="voidInvoice(row, true)"
          v-if="row.invoiceStatus == 4"
          >作废</el-button
        >
        <!-- <el-button type="primary" icon="delete" text @click="$refs.crud.rowDel(row, $index)"
          >删除</el-button
        > -->
      </template>
      <template #customerInvoiceInfoId-form>
        <wfInvoiceDrop v-model="form.customerInvoiceInfoId" :id="props.customerId"></wfInvoiceDrop>
        <productSelect
          ref="productSelectRef"
          @select="handleConfirm"
          :sealContractId="props.sealContractId"
          :offerId="props.offerId"
        ></productSelect>
      </template>
      <template #invoiceStatus="{ row }">
        <el-tooltip
          :content="row.invoiceStatus == 3 ? row.invalidDescript : row.applyVoidReason"
          :disabled="row.invoiceStatus != 3 && row.invoiceStatus != 4"
        >
          <el-tag
            effect="plain"
            :type="
              row.invoiceStatus == 3
                ? 'danger'
                : row.invoiceStatus == 0
                ? ''
                : row.invoiceStatus == 4
                ? 'info'
                : 'success'
            "
            >{{ row.$invoiceStatus }}</el-tag
          >
        </el-tooltip>
      </template>
      <template #invoicePrice="{ row }">
        <el-link type="primary" @click="viewPriceDetail(row)">{{ row.invoicePrice }}</el-link>
      </template>
      <template #invoiceFiles="{ row }"> <File :fileList="row.attachList || []"></File></template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-dialog
      title="开票详情"
      v-model="dialogVisible"
      width="80%"
      class="avue-dialog avue-dialog--top"
    >
      <avue-form ref="dialogForm" :option="detailOption" v-model="detailForm">
        <template #contractBaseInfo>
          <!-- 公共信息区域 -->
          <div v-if="detailForm.sealContractVOList && detailForm.sealContractVOList.length > 0" class="common-info-section">
            <div class="common-info-content">
              <div class="info-header">
                <el-tag type="warning" effect="plain" size="small">{{
                  !detailForm.invoicePriceType ? '按产品开票' : '按合同额开票'
                }}</el-tag>
              </div>
              <el-row :gutter="16">
                <el-col :span="6">
                  <div class="info-item">
                    <span class="info-label">客户名称：</span>
                    <span class="info-value">{{ getCommonInfo('customerName') }}</span>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="info-item">
                    <span class="info-label">客户地址：</span>
                    <span class="info-value">{{ getCommonInfo('customerAddress') }}</span>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="info-item">
                    <span class="info-label">联系人：</span>
                    <span class="info-value">{{ getCommonInfo('customerContact') }}</span>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="info-item">
                    <span class="info-label">联系电话：</span>
                    <span class="info-value">{{ getCommonInfo('customerPhone') }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>

          <!-- 合同详细信息区域 -->
          <div class="contracts-section" style="padding-bottom: 50px;">
            <div v-for="(item, index) in detailForm.sealContractVOList" :key="item.id" class="contract-item">
              <el-card class="contract-card" shadow="hover">
                <template #header>
                  <div class="card-header">
                    <div class="header-left">
                      <span class="header-title">
                        <template v-if="detailForm.sealContractVOList.length > 1">合同 {{ index + 1 }}：</template>
                        <el-link type="primary" @click="toDetail({ id: item.id })" class="contract-link">
                          {{ item.contractName }}
                        </el-link>
                      </span>
                      <span class="collection-amount">本次开票金额：<span class="amount-value">￥{{ calculateContractCollectionAmount(item).toLocaleString() }}</span></span>
                    </div>
                    <el-button type="primary" size="small" @click="viewProductList(item)">查看产品清单</el-button>
                  </div>
                </template>
                <ContractInfo :detailForm="item" :invoicePriceType="detailForm.invoicePriceType" :key="item.id" :hideCommonInfo="true"></ContractInfo>
              </el-card>
            </div>
          </div>
        </template>
        <template #invoiceFiles> <File :fileList="detailForm.attachList || []"></File></template>
      </avue-form>
      <div class="avue-dialog__footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
    <el-dialog
      title="开票"
      v-model="invoiceDialog"
      width="80%"
      class="avue-dialog avue-dialog--top"
    >
      <avue-form
        ref="invoiceFormRef"
        @submit="invoiceSubmit"
        :option="invoiceOption"
        v-model="invoiceForm"
      >
      </avue-form>
      <!-- <slot ></slot> -->
      <div class="avue-dialog__footer">
        <el-button @click="invoiceDialog = false">关 闭</el-button>
        <el-button @click="$refs.invoiceFormRef.submit()" type="primary">确 认</el-button>
      </div>
    </el-dialog>
    <el-dialog title="金额详情" v-model="detailDrawer" width="80%">
      <avue-crud :option="detailPriceOption" :data="detailPriceForm.detailEntityList"></avue-crud>
    </el-dialog>
    <el-drawer title="产品清单" v-model="productDrawer" direction="rtl" size="60%" >
      <productList :edit="false" :sealContractId="currentContractRow.id" :contractType="currentContractRow.contractType" :offerId="currentContractRow.offerId" ></productList>
    </el-drawer>
    
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import wfInvoiceDrop from '../../Contract/customer/compoents/wf-invoice-drop.vue';
import productSelect from '../../Contract/customer/compoents/productSelect.vue';
import { dateFormat } from '@/utils/date';
import { downloadXls } from '@/utils/download';
import contractBaseInfo from '@/views/Contract/customer/detail/contractBaseInfo.vue';
import { ElForm, ElFormItem, ElMessageBox, ElInput } from 'element-plus';
import ContractInfo from './contractInfo.vue';
import productList from '@/views/Contract/customer/detail/contractProduct.vue'
let { proxy } = getCurrentInstance();

let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  viewBtn: false,
  size: 'default',
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  searchIcon: true,
  searchIndex: 4,
  addTitle: '开票申请',
  addBtnText: '开票申请',
  menuWidth: 150,
  border: true,
  column: [
    {
      label: '关联合同',
      prop: 'contractName',
      overHidden: true,
      width: 200,
      searchOrder: 4,
      search: true,
      //   component: 'wf-contract-select',
    },
    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true,
      width: 200,
      component: 'wf-customer-drop',
      searchOrder: 3,
      search: true,
      //   component: 'wf-contract-select',
    },
    {
      type: 'input',
      label: '开票信息',
      span: 24,
      display: true,
      hide: true,

      prop: 'customerInvoiceInfoId',
    },
    {
      type: 'textarea',
      label: '发票号码',
      span: 24,
      display: true,
      search: true,
      width: 150,
      prop: 'invoiceNumber',
    },
    {
      type: 'date',
      label: '开票日期',
      span: 12,
      width: 150,
      display: false,
      component: 'wf-daterange-search',
      search: true,
      overHidden: true,
      searchSpan: 6,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'invoiceDate',
    },

    {
      type: 'select',
      label: '开票类型',
      dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
      cascader: [],
      span: 12,
      width: 130,
      overHidden: true,
      // search: true,
      display: true,

      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'invoiceType',
    },

    {
       label: '开票公司',
      type: 'select',
      props: {
        label: 'companyName',
        value: 'id',
      },
      width: 150,
      search: true,
      overHidden: true,
      cell: false,
      prop: 'billingCompany',
      dicUrl: '/api/vt-admin/company/page?size=100',
      dicFormatter:(res) => {
        return res.data.records
      },
      formatter: (res) => {
        return res.billingCompanyName
      },
    },
    {
      type: 'input',
      label: '不含税金额',
      span: 24,
      overHidden: true,
      width: 100,
      display: false,
      prop: 'noTaxPrice',
    },
    {
      type: 'input',
      label: '税率',
      span: 24,
      overHidden: true,
      display: false,
      width: 100,
      prop: 'taxRates',
    },
    {
      type: 'input',
      label: '税额',
      span: 24,
      overHidden: true,
      display: false,
      width: 100,
      prop: 'taxPrice',
    },
    {
      type: 'input',
      label: '开票金额',
      span: 24,
      overHidden: true,
      display: true,
      width: 100,
      prop: 'invoicePrice',
    },
    {
      label: '申请人',
      type: 'input',
      value: proxy.$store.getters.userInfo.nick_name,
      prop: 'createName',
      readonly: true,
      width: 110,
    },
    {
      type: 'date',
      label: '申请时间',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      readonly: true,
      width: 120,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
      valueFormat: 'YYYY-MM-DD',
      prop: 'createTime',
      overHidden: true,
    },
    {
      label: '关联产品',
      prop: 'detailDTOList',
      type: 'dynamic',
      hide: true,
      rules: [
        {
          required: true,
          message: '请选择关联产品',
        },
      ],
      span: 24,
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          proxy.$refs.productSelectRef.open();
          // done();
        },
        rowDel: (row, done) => {
          done();
        },

        column: [
          {
            label: '产品',
            prop: 'productId',
            bind: 'product.productName',
            cell: false,
          },
          {
            label: '规格型号',
            prop: 'productSpecification',
            bind: 'product.productSpecification',
            overHidden: true,
            // search: true,
            span: 24,
            cell: false,
            type: 'input',
          },
          {
            label: '单位',
            prop: 'unitName',
            bind: 'product.unitName',
            type: 'input',
            span: 12,
            cell: false,
          },
          {
            label: '数量',
            prop: 'number',
            type: 'number',
            span: 12,
            cell: false,
          },
          {
            label: '单价',
            prop: 'zhhsdj',
            type: 'number',
            span: 12,
            cell: false,
          },
          {
            label: '金额',
            prop: 'totalPrice',
            type: 'number',
            span: 12,
            cell: false,
            overHidden: true,
          },
        ],
      },
    },
    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
    },
    {
      type: 'select',
      label: '状态',
      search: true,
      width: 100,
      searchOrder: 2,
      dicData: [
        {
          label: '待开票',
          value: 0,
        },
        {
          label: '已开票 ',
          value: 1,
        },
        {
          label: '已邮寄',
          value: 2,
        },
        {
          label: '已作废',
          value: 3,
        },
        {
          label: '申请作废',
          value: 4,
        },
      ],
      cascader: [],
      span: 12,
      addDisplay: false,
      editDisplay: false,
      props: {
        label: 'label',
        value: 'value',
        desc: 'desc',
      },
      prop: 'invoiceStatus',
    },
    {
      type: 'input',
      label: '发票附件',
      span: 24,
      display: true,
      prop: 'invoiceFiles',
      width: 150,
    },
    {
      type: 'date',
      label: '计划收款时间',
      span: 12,
      addDisplay: false,
      width: 130,
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      editDisplay: false,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'planCollectionDate',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/sealContractInvoice/save';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContractInvoice/page';
let route = useRoute();
let params = ref({
  ids: route.query.ids,
  invoiceStatus: 0,
});
let tableData = ref([]);

onMounted(() => {
  onLoad();
});
let loading = ref(false);
let totalPrice = ref(0);
let hasInvoicePrice = ref(0);
let taxPrice = ref(0);
let noTaxPrice = ref(0);

function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        invoiceStartDate: params.value.invoiceDate && params.value.invoiceDate[0],
        invoiceEndDate: params.value.invoiceDate && params.value.invoiceDate[1],
        invoiceDate: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/sealContractInvoice/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        invoiceStartDate: params.value.invoiceDate && params.value.invoiceDate[0],
        invoiceEndDate: params.value.invoiceDate && params.value.invoiceDate[1],
        invoiceDate: null,
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
      hasInvoicePrice.value = res.data.data.hasInvoicePrice;
      noTaxPrice.value = res.data.data.noTaxPrice;
      taxPrice.value = res.data.data.taxPrice;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    createUser: null,
    createTime: null,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function handleConfirm(list) {
  console.log(list);
  form.value.detailDTOList = list.map(item => {
    return {
      detailId: item.id,
      ...item,
      id: null,
    };
  });
  form.value.invoicePrice = totalAmount();
}
function reset() {
  params.value.ids = null;
  onLoad();
}
function totalAmount() {
  return form.value.detailDTOList.reduce((total, item) => (total += item.totalPrice * 1), 0);
}

let dialogVisible = ref(false);
let detailForm = ref({});
let detailOption = ref({
  detail: true,
  tabs: true,
  submitBtn: false,
  emptyBtn: false,
  group: [
    {
      label: '申请信息',
      prop: 'applyInfo',
      labelWidth: 140,
      column: [
        {
          type: 'input',
          label: '申请开票金额',
          span: 12,
          display: true,
          prop: 'invoicePrice',
        },
        {
          type: 'select',
          label: '申请开票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          span: 12,
          // search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          label: '开票方式',
          type: 'radio',
          prop: 'invoicePriceType',
          span: 12,
          dicData: [
            {
              label: '按产品开票',
              value: 0,
            },
            {
              label: '按合同额开票',
              value: 1,
            },
          ],
        },
        {
          label: '税率',
          type: 'select',
          props: {
            label: 'dictValue',
            value: 'dictKey',
          },
          cell: false,
          prop: 'taxRate',
          dicUrl: '/blade-system/dict/dictionary?code=tax',
          
        },
        {
          label: '申请开票公司',
          type: 'select',
          props: {
            label: 'dictValue',
            value: 'id',
          },span:24,
          cell: false,
          prop: 'billingCompanyName',
         
        },
        {
          label: '申请人',
          type: 'input',
          prop: 'createUser',
          readonly: true,
          component: 'wf-user-select',
        },
        {
          type: 'date',
          label: '申请时间',
          span: 12,
          display: true,
          format: 'YYYY-MM-DD',
          readonly: true,
          value: dateFormat(new Date(), 'yyyy-MM-dd'),
          valueFormat: 'YYYY-MM-DD',
          prop: 'createTime',
        },
        {
          type: 'textarea',
          label: '备注',
          span: 24,
          display: true,
          prop: 'remark',
        },
      ],
    },
    {
      label: '抬头信息',
      prop: 'titleInfo',
      labelWidth: 160,
      column: [
        {
          label: '客户公司名称',
          prop: 'invoiceCompanyName',
          bind: 'sealContractMakeInvoiceInfoVO.invoiceCompanyName',
          rules: [{ required: true, message: '请输入开票公司名称', trigger: 'blur' }],
        },
        {
          label: '纳税人识别号',
          prop: 'ratepayerIdentifyNumber',
          bind: 'sealContractMakeInvoiceInfoVO.ratepayerIdentifyNumber',
          rules: [{ required: true, message: '请输入纳税人识别号', trigger: 'blur' }],
        },
        {
          label: '开户银行',
          prop: 'bankName',
          bind: 'sealContractMakeInvoiceInfoVO.bankName',
          rules: [{ required: true, message: '请输入开户银行', trigger: 'blur' }],
        },
        {
          label: '银行账号',
          prop: 'bankAccount',
          bind: 'sealContractMakeInvoiceInfoVO.bankAccount',
          rules: [{ required: true, message: '请输入银行账号', trigger: 'blur' }],
        },
        {
          label: '注册地址',
          prop: 'bankAddress',
          bind: 'sealContractMakeInvoiceInfoVO.bankAddress',
        },
        {
          label: '电话',
          prop: 'phone',
          bind: 'sealContractMakeInvoiceInfoVO.phone',
        },
        {
          label: '期初末开票余额(元)',
          prop: 'endTermNoInvoiceAmount',
          type: 'number',
          bind: 'sealContractMakeInvoiceInfoVO.endTermNoInvoiceAmount',
        },
      ],
    },
    {
      label: '发票信息',
      prop: 'invoiceInfo',
      column: [
        {
          type: 'date',
          label: '发票日期',
          span: 12,

          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          prop: 'invoiceDate',
        },

        {
          type: 'input',
          label: '发票金额',
          span: 12,
          display: true,
          prop: 'invoicePrice',
        },
        {
          type: 'select',
          label: '发票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          span: 12,
          // search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          type: 'input',
          label: '发票代码',
          span: 12,
          display: true,
          prop: 'invoiceCode',
        },
        {
          type: 'textarea',
          label: '发票号码',
          span: 24,
          display: true,
          prop: 'invoiceNumber',
        },
        {
          label: '发票附件',
          prop: 'invoiceFiles',
          type: 'upload',
          overHidden: true,
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    // {
    //   label: '关联产品',
    //   prop: 'productInfo',

    //   column: [
    //     {
    //       label: '',
    //       prop: 'detailVOList',
    //       type: 'dynamic',
    //       labelWidth: 0,
    //       rules: [
    //         {
    //           required: true,
    //           message: '请选择关联产品',
    //         },
    //       ],
    //       span: 24,
    //       children: {
    //         align: 'center',
    //         headerAlign: 'center',
    //         rowAdd: done => {
    //           proxy.$refs.productSelectRef.open();
    //           // done();
    //         },
    //         rowDel: (row, done) => {
    //           done();
    //         },

    //         column: [
    //           {
    //             label: '关联合同',
    //             prop: 'contractName',
    //             type: 'input',
    //             span: 12,
    //             cell: false,
    //           },
    //           {
    //             label: '产品',
    //             prop: 'productId',

    //             formatter: row => {
    //               return row.customProductName || row.productVO?.productName;
    //             },
    //             cell: false,
    //           },
    //           {
    //             label: '规格型号',
    //             prop: 'productSpecification',

    //             formatter: row => {
    //               return row.customProductSpecification || row.productVO?.productSpecification;
    //             },
    //             overHidden: true,
    //             // search: true,
    //             span: 24,
    //             cell: false,
    //             type: 'input',
    //           },
    //           {
    //             label: '数量',
    //             prop: 'number',
    //             type: 'number',
    //             span: 12,
    //             cell: false,
    //             width: 60,
    //           },
    //           {
    //             label: '单位',
    //             prop: 'unitName',
    //             bind: 'productVO.unitName',
    //             type: 'input',
    //             span: 12,
    //             cell: false,
    //             width: 60,
    //           },
    //           {
    //             label: '单价',
    //             prop: 'zhhsdj',
    //             type: 'number',
    //             span: 12,
    //             cell: false,
    //             width: 100,
    //           },
    //           {
    //             label: '金额',
    //             prop: 'totalPrice',
    //             type: 'number',
    //             span: 12,
    //             cell: false,
    //             width: 120,
    //           },
    //         ],
    //       },
    //     },
    //   ],
    // },
    {
      label: '快递信息',
      prop: 'courieInfo',
      column: [
        {
          type: 'select',
          label: '快递公司',
          dicUrl: '/blade-system/dict/dictionary?code=courierCompany',
          cascader: [],
          span: 24,
          // search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          rules: [
            {
              required: true,
              message: '请选择快递公司',
              trigger: 'blur',
            },
          ],
          prop: 'courierCompany',
        },
        {
          label: '快递单号',
          prop: 'courierNumber',
          span: 24,
          rules: [
            {
              required: true,
              message: '请输入快递单号',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    {
      label: '合同信息',
      prop: 'contract',
      column: [
        {
          type: 'select',
          label: '',
          span: 24,
          labelWidth: 0,
          prop: 'contractBaseInfo',
        },
      ],
    },
  ],
});
let productOption = ref({
  header: false,
  menu: false,
  border: true,
  column: [
    {
      label: '关联合同',
      prop: 'contractName',
      type: 'input',
      span: 12,
      cell: false,
    },
    {
      label: '产品',
      prop: 'productId',

      formatter: row => {
        return row.customProductName || row.productVO?.productName;
      },
      cell: false,
    },
    {
      label: '规格型号',
      prop: 'productSpecification',

      formatter: row => {
        return row.customProductSpecification || row.productVO?.productSpecification;
      },
      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      span: 12,
      cell: false,
      width: 60,
    },
    {
      label: '单位',
      prop: 'unitName',
      bind: 'productVO.unitName',
      type: 'input',
      span: 12,
      cell: false,
      width: 60,
    },
    {
      label: '单价',
      prop: 'zhhsdj',
      type: 'number',
      span: 12,
      cell: false,
      width: 100,
    },
    {
      label: '金额',
      prop: 'totalPrice',
      type: 'number',
      span: 12,
      cell: false,
      width: 120,
    },
  ],
});
function viewDetail(row) {
  axios.get('/api/vt-admin/sealContractInvoice/detail?id=' + row.id).then(res => {
    dialogVisible.value = true;
    detailForm.value = res.data.data;
    getDetail();
  });
}

// 获取公共信息的方法
function getCommonInfo(field) {
  if (!detailForm.value.sealContractVOList || detailForm.value.sealContractVOList.length === 0) {
    return '';
  }

  // 获取第一个合同的信息作为基准
  const firstContract = detailForm.value.sealContractVOList[0];
  const value = firstContract[field];

  return value;
}

// 计算合同本次开票金额（产品列表金额相加）
function calculateContractCollectionAmount(contract) {
  // 发票明细使用 invoiceDetailVOList
  if (contract.invoiceDetailVOList && contract.invoiceDetailVOList.length > 0) {
    return contract.invoiceDetailVOList.reduce((total, product) => {
      const amount = parseFloat(product.totalPrice) || 0;
      return total + amount;
    }, 0);
  }

  // 合同明细使用 productList 或 detailEntityList
  if (contract.productList && contract.productList.length > 0) {
    return contract.productList.reduce((total, product) => {
      const amount = parseFloat(product.totalPrice) || 0;
      return total + amount;
    }, 0);
  }

  // 如果是合同详情，可能直接使用合同总金额
  if (contract.contractTotalPrice) {
    return parseFloat(contract.contractTotalPrice) || 0;
  }

  return 0;
}

// 跳转到合同详情页面
function toDetail(row) {
  if (row.id.split(',').length > 1) {
    return;
  }

  router.push({
    path: '/Contract/customer/compoents/detail',
    query: {
      id: row.id,
      delBtn: 1,
    },
  });
}

// 开票开始
let invoiceDialog = ref(false);
let invoiceForm = ref({});
let invoiceOption = ref({
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      type: 'input',
      label: '开票总额',
      span: 12,
      overHidden: true,
      display: true,
      disabled: true,
      width: 100,
      prop: 'invoicePrice',
    },
    {
      type: 'input',
      label: '申请开票额',
      span: 12,
      overHidden: true,
      display: true,
      disabled: true,
      width: 100,
      prop: 'applyInvoicePrice',
    },
    {
      type: 'date',
      label: '开票日期',
      span: 12,
      // value: row.invoiceDate || dateFormat(new Date(), 'yyyy-MM-dd'),
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'invoiceDate',
    },
    {
      type: 'input',
      label: '发票代码',
      span: 12,
      display: true,
      // value: row.invoiceCode,
      prop: 'invoiceCode',
      // rules: [
      //   {
      //     required: true,
      //     message: '请输入发票代码',
      //     trigger: 'blur',
      //   },
      // ],
    },
    {
      type: 'input',
      label: '发票号码',
      placeholder: '多个号码以逗号隔开',
      span: 24,
      display: true,
      // value: row.invoiceNumber,
      rules: [
        {
          required: true,
          message: '请输入发票号码',
          trigger: 'blur',
        },
      ],
      prop: 'invoiceNumber',
    },
    {
      label: '上传发票',
      prop: 'invoiceFiles',
      type: 'upload',
      overHidden: true,
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 12,
      slot: true,
      // value:
      //   row.attachList &&
      //   row.attachList.map(item => {
      //     return {
      //       value: item.id,
      //       label: item.originalName,
      //     };
      //   }),
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },

      tip: '先上传发票可以自动填充部分类容',
      action: '/blade-resource/attach/upload',

      uploadPreview: file => {
        return;
      },
      uploadAfter: (res, done) => {
        console.log(res);
        done();
        const { id } = res;
        axios
          .get('/api/vt-admin/purchaseContractInvoiceDetail/analysisInvoice', {
            params: {
              id,
            },
          })
          .then(res => {
            const {
              date: invoiceDate,
              buyerName: companyName,
              totalAmount: invoicePrice,
              number: invoiceNumber,
            } = res.data.data;
            const list = res.data.data.detailList?.map(item => {
              const {
                totalPrice,
                amount: productTotalPrice,
                taxAmount: taxPrice,
                taxRate,
                count: number,
                name: productName,
                model: specification,
                unit: unitName,
                price: price,
              } = item;
              return {
                totalPrice,
                productTotalPrice,
                taxPrice,
                taxRate,
                number,
                productName,
                specification,
                unitName,
                price,
                disabled: true,
              };
            });

            if (invoiceForm.value.detailEntityList) {
              invoiceForm.value.detailEntityList.push(...list);
            } else {
              invoiceForm.value.detailEntityList = list;
            }
            invoiceForm.value.invoiceDate = invoiceDate;
            // form.value.companyName = companyName;
            invoiceForm.value.invoicePrice = invoicePrice;
            invoiceForm.value.invoiceNumber = invoiceNumber;
          });
      },
    },
    {
      type: 'input',
      label: '明细信息',
      span: 24,
      display: true,
      hide: true,
      prop: 'detailEntityList',
      type: 'dynamic',
      rules: [
        {
          required: true,
          message: '请输入明细信息',
        },
      ],
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          done();
        },
        rowDel: (row, done) => {
          done();
        },
        column: [
          {
            label: '货物、应税劳务及服务',
            prop: 'productName',
          },
          {
            label: '规格型号',
            prop: 'specification',
          },
          {
            label: '单位',
            prop: 'unitName',
            width: 80,
          },
          {
            label: '数量',
            prop: 'number',
            width: 80,
            change: (a, b, c) => {
              // const { row } = a;
              // if (row.price) {
              //   row.productTotalPrice = (row.number * 1 * row.price * 1).toFixed(2);
              // }
              const { row } = a;
              if (row.productTotalPrice && !row.disabled) {
                row.price = ((row.productTotalPrice * 1) / row.number) * 1;
              }
            },
          },
          {
            label: '单价',
            prop: 'price',
            width: 100,
            change: (a, b, c) => {
              // const { row } = a;
              // if (row.number) {
              //   row.productTotalPrice = (row.number * 1 * row.price * 1).toFixed(2);
              // }
            },
          },
          {
            label: '金额',
            prop: 'productTotalPrice',
            width: 120,
            change: (a, b, c) => {
              // const { row } = a;
              // if (row.taxRate) {
              //   row.taxPrice = ((row.productTotalPrice * 1 * (row.taxRate * 1)) / 100).toFixed(2);

              //   row.totalPrice = (row.productTotalPrice * 1 + row.taxPrice * 1).toFixed(2);
              // }
              const { row } = a;
              if (row.number && !row.disabled) {
                row.price = ((row.productTotalPrice * 1) / row.number) * 1;
              }
            },
          },
          {
            label: '税率',
            type: 'select',
            cell: true,
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            width: 100,
            prop: 'taxRate',
            dicUrl: '/blade-system/dict/dictionary?code=tax',
            change: (a, b, c) => {
              const { row } = a;
              if (row.totalPrice && !row.disabled) {
                row.productTotalPrice = (row.totalPrice / (1 + (row.taxRate * 1) / 100)).toFixed(2);

                row.taxPrice = (row.totalPrice - row.productTotalPrice).toFixed(2);
              }
            },
          },
          {
            label: '税额',
            prop: 'taxPrice',
            width: 120,
          },
          {
            label: '小计',
            prop: 'totalPrice',
            width: 120,
            change: a => {
              invoiceForm.value.invoicePrice = invoiceForm.value.detailEntityList
                .reduce((pre, cur) => {
                  return (pre += cur.totalPrice * 1);
                }, 0)
                .toFixed(2);

              const { row } = a;
              if (row.taxRate && !row.disabled) {
                row.productTotalPrice = (row.totalPrice / (1 + (row.taxRate * 1) / 100)).toFixed(2);

                row.taxPrice = (row.totalPrice - row.productTotalPrice).toFixed(2);
              }
            },
          },
        ],
      },
    },
  ],
});
let invoiceFormRef = ref(null);
function invoice(row, type) {
  if (invoiceFormRef.value) {
    invoiceFormRef.value.resetForm();
  }
  axios
    .get('/api/vt-admin/sealContractInvoice/detail', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      invoiceForm.value = {
        ...res.data.data,
        applyInvoicePrice: res.data.data.invoicePrice,
        invoicePrice: 0,
        invoiceFiles: res.data.data.attachList.map(item => {
          return {
            value: item.id,
            label: item.originalName,
          };
        }),
        editType: type,
        detailEntityList: res.data.data.detailEntityList.map(item => {
          return {
            ...item,
            taxRate: '' + item.taxRate * 1,
          };
        }),
      };
      invoiceDialog.value = true;
    });
}
function invoiceSubmit(form, done, loading) {
  if (form.applyInvoicePrice != form.invoicePrice) {
    proxy.$message.warning('开票金额必须等于申请开票金额，请联系申请人');
    done();
    return;
  }
  const { editType } = form;
  let url = '';
  if (editType) {
    url = '/api/vt-admin/sealContractInvoice/update';
  } else {
    url = '/api/vt-admin/sealContractInvoice/invoice';
  }
  const data = {
    id: form.id,
    ...form,
    invoiceFiles: form.invoiceFiles.map(item => item.value).join(','),
  };
  axios
    .post(url, data)
    .then(r => {
      proxy.$message.success(r.data.msg);
      proxy.$store.dispatch('getMessageList');
      onLoad();
      done();
      invoiceDialog.value = false;
    })
    .catch(() => {
      done();
    });
}
// 开票结束
// 详情开始
let detailDrawer = ref(false);
let detailPriceForm = ref({});
let detailPriceOption = ref({
  border: true,
  header: false,
  menu: false,
  align: 'center',
  column: [
    {
      label: '货物、应税劳务及服务',
      prop: 'productName',
    },
    {
      label: '规格型号',
      prop: 'specification',
    },
    {
      label: '单位',
      prop: 'unitName',
      width: 80,
    },
    {
      label: '数量',
      prop: 'number',
      width: 80,
      formatter: row => {
        return parseFloat(row.number);
      },
    },
    {
      label: '单价',
      prop: 'price',
      width: 100,
      overHidden: true,
    },
    {
      label: '金额',
      prop: 'productTotalPrice',
      width: 120,
    },
    {
      label: '税率',
      type: 'select',
      cell: true,
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },
      width: 100,
      prop: 'taxRate',
      dicUrl: '/blade-system/dict/dictionary?code=tax',
      formatter: row => {
        return parseFloat(row.taxRate) + '%';
      },
    },
    {
      label: '税额',
      prop: 'taxPrice',
      width: 120,
    },
    {
      label: '小计',
      prop: 'totalPrice',
      width: 120,
    },
  ],
});
function viewPriceDetail(row) {
  axios
    .get('/api/vt-admin/sealContractInvoice/detail', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      detailPriceForm.value = res.data.data;
      detailDrawer.value = true;
    });
}
let productDrawer =  ref(false)
let currentContractRow = ref(null)
function viewProductList(row) {
    currentContractRow.value = row
    productDrawer.value = true
}
// 详情结束
function send(row) {
  proxy.$refs.dialogForm.show({
    title: '邮寄',
    option: {
      column: [
        {
          type: 'select',
          label: '快递公司',
          dicUrl: '/blade-system/dict/dictionary?code=courierCompany',
          cascader: [],
          span: 24,
          // search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          rules: [
            {
              required: true,
              message: '请选择快递公司',
              trigger: 'blur',
            },
          ],
          prop: 'courierCompany',
        },
        {
          label: '快递单号',
          prop: 'courierNumber',
          span: 24,
          rules: [
            {
              required: true,
              message: '请输入快递单号',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        ...res.data,
      };
      axios.post('/api/vt-admin/sealContractInvoice/mail', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
let contractForm = ref({});
// function getDetail() {

//   axios
//     .get('/api/vt-admin/sealContract/detail', {
//       params: {
//         id: detailForm.value.sealContractId,
//       },
//     })
//     .then(res => {

//       // form.value = formatData(res.data.data)
//       contractForm.value = {
//         ...res.data.data,
//         distributionMethod: '' + res.data.data.distributionMethod,
//       };

//     });
// }
function voidInvoice(row) {
  const invalidDescript = ref('');
  ElMessageBox({
    title: '提示',
    cancelButtonText: 'Cancel',
    message: () => [
      h(
        ElForm,
        {
          labelPosition: 'top',
        },

        h(
          ElFormItem,
          { label: '作废原因：' },
          h(ElInput, {
            modelValue: invalidDescript.value,
            type: 'textarea',
            placeholder: '请输入作废原因',
            'onUpdate:modelValue': val => {
              invalidDescript.value = val;
            },
          })
        )
      ),
    ],
  }).then(res => {
    axios
      .post('/api/vt-admin/sealContractInvoice/voidInvoice', {
        id: row.id,
        invalidDescript: invalidDescript.value,
      })
      .then(res => {
        proxy.$message.success('操作成功');

        onLoad();
      });
  });
}
let result = ref([]);
function getDetail() {
  result.value = detailForm.value.sealContractId.split(',');
}
function exportData() {
  const { pageSize: size, currentPage: current } = page.value;
  downloadXls({
    url: '/api/vt-admin/sealContractInvoice/exportList',
    method: 'post',
    params: {
      size,
      current,
      ...params.value,
      invoiceStartDate: params.value.invoiceDate && params.value.invoiceDate[0],
      invoiceEndDate: params.value.invoiceDate && params.value.invoiceDate[1],
      invoiceDate: null,
    },
  });
}
</script>

<style lang="scss" scoped>
// 明细对话框样式
.common-info-section {
  margin-bottom: 16px;
}

.common-info-content {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px 16px;

  .info-header {
    margin-bottom: 8px;
    text-align: right;
  }

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 0;
    min-height: 28px;

    .info-label {
      font-weight: 500;
      color: #495057;
      font-size: 13px;
      padding-left: 10px;
      min-width: 70px;
      flex-shrink: 0;
    }

    .info-value {
      color: #212529;
      flex: 1;
      word-break: break-all;
      font-size: 13px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.contracts-section {
  .contract-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .contract-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-left {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .header-title {
        font-size: 15px;
        font-weight: 600;
        color: #303133;

        .contract-link {
          font-size: 15px;
          font-weight: 600;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .collection-amount {
        font-size: 13px;
        color: #606266;

        .amount-value {
          font-weight: 600;
          color: #e6a23c;
          font-size: 14px;
        }
      }
    }
  }
}

// 对话框内容区域样式优化
:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.avue-dialog__footer) {
  padding: 15px 20px;
  border-top: 1px solid #e4e7ed;
  background-color: #fafafa;
  text-align: right;
}
</style>

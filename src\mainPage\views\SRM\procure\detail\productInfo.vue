<template>
  <div>
    <el-card
      class="box-card"
      shadow="never"
      v-for="item in form.supplier"
      style="margin-bottom: 10px"
    >
      <template #header>
        <div style="display: flex; align-items: center">
          <el-text size="large" type="primary">{{ item.supplierName }}</el-text>
          <el-button
            style="margin-left: 20px"
            type="primary"
            v-if="
              item.supplierName != '暂无供应商' &&
              form.statusMap[item.supplierName] == 0 &&
              form.auditStatus == 1
            "
            @click="handllAdd(item)"
            >签合同</el-button
          >
          <i
            v-if="item.supplierName != '暂无供应商' && form.statusMap[item.supplierName] == 1"
            class="element-icons el-icon-yiqianding"
            style="font-size: 60px; color: var(--el-color-danger)"
          ></i>
        </div>
      </template>
      <el-table
        class="avue-crud"
        @selection-change="handleSelectionChange"
        v-loading="loading"
        :data="item.products"
        :summary-method="getSummaries"
        show-summary
        :row-style="rowStyle"
        border
      >
        <el-table-column
          label="产品名称"
          show-overflow-tooltip
          #default="{ row }"
          prop="productVO.productName"
        >
          <el-popover
            :disabled="!row.productVO"
            placement="top-start"
            title="请购原产品详情"
            :width="400"
            trigger="hover"
          >
          <el-form inline label-position="top" label-width="80px">
            <el-form-item label="产品名称">
              <el-tag effect="plain" size="large">{{ row.customProductName }}</el-tag>
            </el-form-item>
            <el-form-item label="品牌">
              <el-tag effect="plain" size="large">{{ row.productBrand }}</el-tag>
            </el-form-item>
            <el-form-item label="产品型号">
              <el-tag effect="plain" size="large">{{ row.customProductSpecification }}</el-tag>
            </el-form-item>
            <el-form-item label="单位">
              <el-tag effect="plain" size="large">{{ row.customUnit }}</el-tag>
            </el-form-item>
          </el-form>
            <template #reference>
              <el-link type="primary" class="m-2">{{
                row.productVO?.productName || row.productVO.productName
              }}</el-link>
            </template>
          </el-popover>
        </el-table-column>
        <el-table-column
          label="规格型号"
          prop="customProductSpecification"
          show-overflow-tooltip
          #default="{ row }"
        >
          {{ row.productVO?.productSpecification || row.customProductSpecification }}
        </el-table-column>
        <!-- <el-table-column label="产品图片" #default="{ row }">
          <el-image
            style="width: 80px"
            :preview-src-list="[row.coverUrl]"
            :src="row.coverUrl"
          ></el-image>
        </el-table-column> -->
        <el-table-column
          label="产品描述"
          show-overflow-tooltip
          width="200"
          prop="description"
          #default="{ row }"
        >
          {{  row.productVO?.description ||  row.customProductDescription  }}
        </el-table-column>
        <el-table-column
          label="品牌"
          #default="{ row }"
          prop="productVO.productBrand"
          width="100"
          >{{ row.productVO?.productBrand ||  row.productBrand  }}</el-table-column
        >
        <el-table-column label="单位" #default="{ row }" prop="unitName">
          {{ row.productVO?.unitName ||  row.customUnit  }}
        </el-table-column>
        <el-table-column label="数量" width="80" #default="{ row }" prop="number">
        </el-table-column>
        <el-table-column label="入库数量" width="100" #default="{ row }" prop="arriveNumber">
          {{ row.arriveNumber || 0 }}
        </el-table-column>
        <el-table-column label="出库数量" width="100" #default="{ row }" prop="outNumber">
          {{ row.outNumber || 0 }}
        </el-table-column>
        <el-table-column label="使用库存数量" width="120" #default="{ row }" prop="inventoryNumber">
          {{ row.inventoryNumber || 0 }}</el-table-column
        >

        <!-- <el-table-column label="报价(采购价)" #default="{ row }" prop="unitPrice">
          <span>{{ row.unitPrice }}</span>
        </el-table-column> -->
        <el-table-column
          label="单价"
          align="center"
          width="100"
          #default="{ row }"
          prop="unitPrice"
        >
        </el-table-column>
        <el-table-column
          label="金额"
          align="center"
          width="120"
          #default="{ row }"
          prop="totalPrice"
        >
        </el-table-column>
        <!-- <el-table-column label="供应商" #default="{ row }" width="250" prop="supplier">
          <span>{{ row.supplierName }}</span>
        </el-table-column> -->
        <el-table-column label="采购类型" prop="purchaseType" show-overflow-tooltip width="150">
          <template #default="{ row }">
            <el-tag type="primary" v-if="row.purchaseType == 1" effect="plain">库存采购</el-tag>
            <el-tag type="success" v-else effect="plain">报价采购</el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <suppliyerSelect
      ref="suppliyerSelectRef"
      @onConfirm="handleUserSelectConfirm"
    ></suppliyerSelect>
    <dialogForm ref="dialogForm"></dialogForm>
    <!-- <avue-form :option="signOption" v-model="signForm" ref="signFormRef"></avue-form> -->
    <el-drawer title="确认产品信息" size="90%" v-model="visible">

      
      <el-row :gutter="10">
        <el-col :span="8">
          <el-card shadow="never">
            <avue-form
              :option="addOption"
              ref="addFormRef"
              @submit="handleSubmit"
              v-model="addForm"
            ></avue-form>
          </el-card>
        </el-col>
        <el-col :span="16">
          <el-card shadow="never">
            <el-table
              class="avue-crud"
              show-summary
              :summary-method="summaryMethod"
              :data="productList"
              border
              align="center"
            >
              <el-table-column
                label="设备名称"
                show-overflow-tooltip
                prop="productVO.productName"
              ></el-table-column>
              <el-table-column
                label="规格型号"
                show-overflow-tooltip
                prop="productVO.productSpecification"
              ></el-table-column>
              <!-- <el-table-column label="产品图片" #default="{ row }">
        <el-image
            style="width: 80px"
            :preview-src-list="[row.coverUrl]"
            :src="row.coverUrl"
          ></el-image>
      </el-table-column> -->
              <el-table-column
                label="产品描述"
                show-overflow-tooltip
                width="200"
                prop="productVO.description"
              ></el-table-column>
              <el-table-column label="品牌" prop="productVO.productBrand"></el-table-column>
              <el-table-column label="单位" prop="productVO.unitName"></el-table-column>
              <el-table-column
                label="供应商"
                show-overflow-tooltip
                prop="supplierName"
              ></el-table-column>
              <el-table-column label="数量" #default="{ row }" prop="number"> </el-table-column>

              <el-table-column label="单价" #default="{ row, $index }" prop="unitPrice">
                <el-input
                  v-model="productList[$index].unitPrice"
                  @blur="setContractTotalAmount"
                  size="small"
                ></el-input>
              </el-table-column>
              <el-table-column label="金额" #default="{ row }" prop="totalPrice">
                {{ row.unitPrice ? row.number * row.unitPrice : '---' }}
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>

      <template #footer>
        <div style="flex: auto">
          <el-button type="primary" @click="$refs.addFormRef?.submit()">确 定</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup>
import suppliyerSelect from '@/components/Y-UI/wf-supplier-select.vue';
import moment from 'moment';

import { getCurrentInstance, onMounted, nextTick } from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute();
const emit = defineEmits(['update']);
const { proxy } = getCurrentInstance();
let selectList = ref([]);
onMounted(() => {
  getDetail();
});
let form = ref({});
let loading = ref(false);
function getDetail() {
  loading.value = true;
  axios
    .get('/api/vt-admin/purchaseOrder/detail', {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
      loading.value = false;

      form.value = formatData(res.data.data);
    });
}
function formatData(data) {
  const keys = Object.keys(data.detailMap);
  const form = {
    ...data,
  };

  form.supplier = keys.map(item => {
    return {
      supplierName: item,
      products: data.detailMap[item].map(i => {
        console.log(i);
        return {
          ...i.productVO,
          ...i,
        };
      }),
    };
  });
  form.supplier.sort((a, b) => {
    // 如果 a 的 supplierName 是 "暂无供应商"，将其排在前面
    if (a.supplierName === '暂无供应商') {
      return -1;
    }

    // 如果 b 的 supplierName 是 "暂无供应商"，将其排在前面
    if (b.supplierName === '暂无供应商') {
      return 1;
    }

    // 如果都不是 "暂无供应商"，按原始顺序排列
    return 0;
  });
  return form;
}
function addContract(row) {
  proxy.$refs.dialogForm.show({
    title: '签订合同',

    option: {
      labelWidth: 150,
      column: [
        {
          label: '采购日期',
          type: 'date',
          prop: 'purchaseDate',

          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rules: [
            {
              required: true,
              message: '请选择采购日期',
              trigger: 'change',
            },
          ],
        },
        {
          label: '送货地址',
          type: 'input',
          prop: 'deliveryAddress',
          span: 24,
          value: '深圳市龙华区民治街道北站社区鸿荣源北站中心B塔2104-2105',
          hide: true,
        },
        {
          label: '送货方式',
          type: 'radio',
          prop: 'deliveryMethod',
          span: 24,
          dicUrl: '/blade-system/dict/dictionary?code=delivery_method',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          label: '付款期限',
          type: 'radio',
          span: 15,
          prop: 'paymentTerm',
          dicUrl: '/blade-system/dict/dictionary?code=payment_term',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          label: '自定义账期',
          span: 9,
          labelTip: '填1-30之间的数字,即每个月这个日期结账',
          prop: 'fixedBillingDate',
        },
        {
          label: '付款方式',
          type: 'radio',
          prop: 'paymentMethod',
          span: 24,
          dicUrl: '/blade-system/dict/dictionary?code=payment_methods',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          label: '合同附件',
          prop: 'contractFiles',
          type: 'upload',
          dataType: 'object',
          cell: true,
          loadText: '附件上传中，请稍等',
          span: 24,
          width: 150,
          slot: true,
          value: [],
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    callback(res) {
      const data = {
        ...res.data,
        contractFiles:
          res.data.contractFiles && res.data.contractFiles.map(item => item.value).join(','),
        purchaseDate: res.data.purchaseDate,
        orderId: route.query.id,
        supplierId: row.products[0].supplierId,
        detailDTOList: row.products.map(item => {
          return {
            ...item,
            orderDetailId: item.id,
            number: item.number - item.inventoryNumber,
            id: null,
          };
        }),
      };
      axios
        .post('/api/vt-admin/purchaseContract/save', data)
        .then(r => {
          if (r.data.code == 200) {
            proxy.$message.success(r.data.msg);
            getDetail();
            res.close();
          }
        })
        .catch(err => {});
    },
  });
}
function getSummaries({ columns, data }) {
  let arr = [];
  console.log(columns, data);

  columns.forEach(element => {
    if (element.property == 'totalPrice') {
      const value = data.reduce((prev, curr) => {
        return prev + curr.totalPrice * 1;
      }, 0);
      arr.push(`${value.toFixed(2)}`);
    } else if (element.property == 'productName') {
      arr.push('合计：');
    } else if (element.property == 'number') {
      const value = data.reduce((prev, curr) => {
        return prev + curr.number * 1;
      }, 0);
      arr.push(`${value}`);
    } else {
      arr.push('');
    }
  });
  return arr;
}
function rowStyle({ row, index }) {
  if (row.purchaseType == 1) {
    return {
      background: 'var(--el-color-primary-light-9)',
    };
  }
}

let addOption = ref({
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  column: [
    {
      label: '优惠金额',
      prop: 'discountPrice',
      span: 24,
      type: 'number',
      value: 0,
      width: 150,
    },
    {
      label: '合同总额',
      prop: 'contractPrice',
      addDisplay: false,
      readonly: true,
      span: 24,
      type: 'number',
      width: 150,
    },

    {
      label: '签订日期',
      type: 'date',
      prop: 'purchaseDate',
      value: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      span: 24,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      width: 150,
      rules: [
        {
          required: true,
          message: '请选择签订日期',
        },
      ],
      // hide: true,
    },

    {
      label: '送货地址',
      type: 'input',
      prop: 'deliveryAddress',
      span: 24,
      hide: true,
    },
    {
      label: '送货方式',
      type: 'radio',
      prop: 'deliveryMethod',
      span: 24,
      dicUrl: '/blade-system/dict/dictionary?code=delivery_method',
      props: {
        value: 'id',
        label: 'dictValue',
      },
      hide: true,
    },
    {
      label: '付款期限',
      type: 'radio',
      span: 24,
      prop: 'paymentTerm',
      dicUrl: '/blade-system/dict/dictionary?code=payment_term',
      props: {
        value: 'id',
        label: 'dictValue',
      },
      hide: true,
    },
    {
      label: '自定义账期',
      span: 9,
      span: 24,
      labelTip: '填1-30之间的数字,即每个月这个日期结账',
      prop: 'fixedBillingDate',
      hide: true,
    },
    {
      label: '付款方式',
      type: 'radio',
      prop: 'paymentMethod',
      span: 24,
      dicUrl: '/blade-system/dict/dictionary?code=payment_methods',
      props: {
        value: 'id',
        label: 'dictValue',
      },
      hide: true,
    },

    // {
    //   label: '付款状态',
    //   type: 'payStatus',
    //   prop: 'select',
    //   dicData: [
    //     {
    //       label: '未付款',
    //       value: '0',
    //     },
    //     { label: '已付款', value: '1' },
    //   ],
    // },
    // {
    //   label: '创建日期',
    //   type: 'date',
    //   prop: 'createTime',
    //   width: 150,
    //   overHidden: true,
    //   addDisplay: false,
    //   format: 'YYYY-MM-DD HH:mm',
    //   valueFormat: 'YYYY-MM-DD HH:mm',
    // },
  ],
});
let addForm = ref({
  supplierId: '',
});
let visible = ref(false);
function getContractTotalAmount() {
  // 计算产品总额
  const totalAmount = productList.value.reduce((sum, item) => {
    const num = Number(item.number) || 0;
    const price = Number(item.unitPrice) || 0;
    return sum + num * price;
  }, 0);
  return totalAmount;
}
const summaryMethod = ({ columns, data }) => {
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (column.property === 'totalPrice') {
      const values = data.map(item => {
        const num = Number(item.number) || 0;
        const price = Number(item.unitPrice) || 0;
        return num * price;
      });
      sums[index] = values.reduce((a, b) => a + b, 0).toFixed(2);
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
let addFormRef = ref();
let productList = ref([]);
function handllAdd(row) {
  
  // if (addFormRef.value) {
  //   addFormRef.value.resetFields();
  //   addFormRef.value.resetForm();
  // }
  productList.value = row.products.map(item => {
    return {
      ...item,
      orderDetailId: item.id,
      number: item.number - item.inventoryNumber,
      id: null,
    };
  });

  // 清空新增表单数据
  addForm.value = {
    orderId: route.query.id,
    supplierId: productList.value[0].supplierId, // 默认选择第一个供应商
    deliveryAddress: proxy.$store.getters.userInfo.companyInfo.address,
  };

  // 打开drawer
  visible.value = true;
  nextTick(() => {
    const totalAmount = getContractTotalAmount();
    addForm.value.contractPrice = totalAmount;
    addForm.value.deliveryAddress = proxy.$store.getters.userInfo.companyInfo.address;
  });
}
function handleSubmit(form, done, loading) {
  // 新增合同逻辑
  const data = {
    ...form,
    detailDTOList: productList.value.map(item => {
      return {
        ...item,
        // 计算总价
        totalPrice: item.number * item.unitPrice,
      };
    }),
  };
  axios
    .post('/api/vt-admin/purchaseContract/save', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        getDetail();
        visible.value = false;
        done();
      }
    })
    .catch(err => {
      done();
    });
}

// 新增一个方法用于设置合同总额
function setContractTotalAmount() {
  const totalAmount = getContractTotalAmount();
  addForm.value.contractPrice = totalAmount;
}
</script>

<style lang="scss" scoped></style>

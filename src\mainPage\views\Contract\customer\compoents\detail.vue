<template>
  <basic-container style="height: 100%">
    <Title
      >合同详情
      <template #foot>
        <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >
    <div class="header">
      <div>
        <h3 style="margin-bottom: 10px" class="title">{{ form.name }}</h3>
        <div class="right"></div>
        <el-form inline label-position="top">
          <el-form-item label="合同名称">
            <el-tag effect="plain" size="default">{{ form.contractName || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="合同总额">
            <el-tag effect="plain" size="default">{{ form.contractTotalPrice || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="原合同总额" v-if="form.changePrice > 0">
            <el-tag effect="plain" size="default">{{ form.oldContractTotalPrice || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="签证总额" v-if="form.changePrice > 0">
            <el-tag effect="plain" size="default">{{ form.changePrice || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="回款金额">
            <el-tag effect="plain" size="default">{{ form.receivedPrice || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="合同状态">
            <el-tag effect="plain" size="default" v-if="form.contractStatus == 0" type="info"
              >待执行</el-tag
            >
            <el-tag effect="plain" size="default" v-if="form.contractStatus == 1" type="info"
              >执行中</el-tag
            >
            <el-tag effect="plain" size="default" type="info" v-if="form.contractStatus == 2"
              >暂停</el-tag
            >
            <el-tag effect="plain" size="default" type="info" v-if="form.contractStatus == 3"
              >终止</el-tag
            >
            <el-tag effect="plain" size="default" type="success" v-if="form.contractStatus == 4"
              >结束</el-tag
            >
          </el-form-item>
          <el-form-item label="业务">
            <el-tag effect="plain">{{ form.businessUserName || '---' }}</el-tag>
            <!-- <el-icon style="cursor: pointer; margin-left: 5px" @click="edit">
              <Edit></Edit>
            </el-icon> -->
          </el-form-item>
          <el-form-item :label="'商务'">
            <el-tag effect="plain">{{ form.assistUserName || '---' }}</el-tag>
            <!-- <el-icon style="cursor: pointer; margin-left: 5px" @click="edit">
              <Edit></Edit>
            </el-icon> -->
          </el-form-item>
          <el-form-item label="客户名称">
            <el-tag effect="plain">{{ form.customerName || '---' }}</el-tag>
            <!-- <el-icon style="cursor: pointer; margin-left: 5px" @click="edit">
              <Edit></Edit>
            </el-icon> -->
          </el-form-item>
          <el-form-item label="客户地址">
            <el-tag effect="plain">{{ form.customerAddress || '---' }}</el-tag>
            <!-- <el-icon style="cursor: pointer; margin-left: 5px" @click="edit">
              <Edit></Edit>
            </el-icon> -->
          </el-form-item>
          <el-form-item label="客户联系人">
            <el-tag effect="plain">{{ form.customerContactName || '---' }}</el-tag>
            <!-- <el-icon style="cursor: pointer; margin-left: 5px" @click="edit">
              <Edit></Edit>
            </el-icon> -->
          </el-form-item>
          <el-form-item label="联系电话">
            <el-tag effect="plain">{{ form.customerPhone || '---' }}</el-tag>
            <!-- <el-icon style="cursor: pointer; margin-left: 5px" @click="edit">
              <Edit></Edit>
            </el-icon> -->
          </el-form-item>
        </el-form>
      </div>
      <div class="btn_group" style="margin-right: 20px; padding-right: 20px">
        <el-button
          type="primary"
          icon="Edit"
          v-if="!isEdit && form.businessPerson == $store.getters.userInfo.user_id"
          @click="isEdit = true"
          >编辑</el-button
        >
        <el-button type="primary" icon="close" v-else-if="isEdit" @click="isEdit = false"
          >取消</el-button
        >
      </div>
    </div>
    <!-- <div style="display: flex">
          <div class="left_content">
            <div class="main_box">
              <div
                class="item"
                v-for="(item, index) in tabArr"
                :class="{ active: currentIndex == index }"
                @click="handleClick(index)"
              >
                <div class="arrow"></div>
                {{ item }}
              </div>
            </div>
          </div>
          <div style="width: calc(100% - 100px)">
            <component
              :is="currentCompoent"
              :form="form"
              :isEdit="isEdit"
              @getDetail="getDetail"
            ></component>
          </div>
        </div> -->
    <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="baseInfo">
        <div>
          <ContractBaseInfo
            :form="form"
            v-loading="loading"
            :isEdit="isEdit"
            :contractType="form.contractType"
            @getDetail="getDetail"
          ></ContractBaseInfo>
        </div>
      </el-tab-pane>
      <el-tab-pane :label="item.label" :name="item.name" v-for="item in tabArray" :key="item.name">
        <template #label>
          {{ item.label }}
          <el-text
            :type="parseFloat(form.planCollectionRate) < 100 ? 'danger' : 'success'"
            v-if="item.label == '计划收款'"
            >({{ form.planCollectionRate }})</el-text
          >
        </template>
      </el-tab-pane>
    </el-tabs>
    <component
      v-if="conpletArr.includes(activeName) && activeName != 'baseInfo'"
      :userId="form.businessPerson"
      :is="tabArray.find(item => item.name == activeName).component"
      :offerId="form.offerId"
      :sealContractId="form.id"
      :isNeedInvoice="form.isNeedInvoice"
      :customerId="form.customerId"
      :sealContractInvoiceId="form.customerInvoiceId"
      :delBtn="delBtn"
      :contractType="form.contractType"
      @success="getDetail"
      :form="form"
      :selectType="route.query.selectType == 0 ? 0 : 1"
      :contractTotalPrice="form.contractTotalPrice"
    ></component>
    <el-empty v-if="!conpletArr.includes(activeName) && activeName != 'baseInfo'"></el-empty>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, shallowRef, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { computed } from 'vue';
import ContractBaseInfo from '../detail/contractBaseInfo.vue';
// 跟进
import Follow from '@/views/CRM/follow/allFollow.vue';
// 产品信息
import productInfo from '../detail/contractProduct.vue';
// 标的
import mission from '../detail/mission.vue';
// 发票
import invoice from '../detail/invoice.vue';
// 计划收款
import plan from '../detail/plannedCollection.vue';
// 实际收款
import actual from '../detail/actual.vue';
// 任务
import wokerOrder from '../detail/wokerOrder.vue';
// 费用
import cost from '../detail/cost.vue';
// 续费
import productRenew from '../detail/productRenew.vue';
// 维保服务
import sealContractQualityGuarantee from '../detail/sealContractQualityGuarantee.vue';
// 退货
import returnProduct from '../detail/returnProduct.vue';
// // 维修
// import repairProduct from '../detail/repairProduct.vue';
import axios from 'axios';
import { ElMessage } from 'element-plus';

let route = useRoute();
let router = useRouter();
let form = ref({});
let isEdit = ref(false);
let loading = ref(false);
let delBtn = ref(false);
// onMounted(() => {
//   getDetail();
// });
watchEffect(() => {
  if (route.query.id) {
    getDetail();

    console.log(route.query.delBtn);
    delBtn.value = route.query.delBtn == 1;
  }
});
const conpletArr = [
  'follow',
  'product',
  'contract',
  'mission',
  'invoice',
  'plan',
  'actual',
  'wokerOrder',
  'cost',
  'productRenew',
  'sealContractQualityGuarantee',
  'returnProduct',
];
const tabArray = [
  {
    label: '产品信息',
    name: 'product',
    component: productInfo,
  },
  // {
  //   label: '标的',
  //   name: 'mission',
  //   component: mission,
  // },
  {
    label: '开票',
    name: 'invoice',
    component: invoice,
  },
  {
    label: '计划收款',
    name: 'plan',
    component: plan,
  },
  {
    label: '实际收款',
    name: 'actual',
    component: actual,
  },
  {
    label: '发布任务',
    name: 'wokerOrder',
    component: wokerOrder,
  },
  {
    label: '费用',
    name: 'cost',
    component: cost,
  },
  {
    label: '续费产品',
    name: 'productRenew',
    component: productRenew,
  },
  // {
  //   label: '维保服务',
  //   name: 'sealContractQualityGuarantee',
  //   component: sealContractQualityGuarantee,
  // },
  {
    label: '退货',
    name: 'returnProduct',
    component: returnProduct,
  },
  // {
  //   label: '维修',
  //   name: 'repairProduct',
  //   component: repairProduct,
  // },
];

function getDetail() {
  isEdit.value = false;
  loading.value = true;
  axios
    .get('/api/vt-admin/sealContract/detail', {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
        if (!res.data.data) {
        ElMessage.warning('该合同不存在，自动关闭页面');
        proxy.$router.$avueRouter.closeTag();
        proxy.$router.back();
      }
      loading.value = false;
      // form.value = formatData(res.data.data)
      form.value = {
        ...res.data.data,
        distributionMethod: '' + res.data.data.distributionMethod,
      };
    
    });
}
let moreInfoDetail = ref(false);

let currentIndex = ref(0);
// let currentCompoent = shallowRef(BaseInfo);
function handleClick(value) {
  currentIndex.value = value;
  // currentCompoent.value = compoentArr[value];
}
let { proxy } = getCurrentInstance();

function edit() {
  proxy.$refs.dialogForm.show({
    title: '编辑',
    option: {
      column: [
        {
          label: '商务',
          component: 'wf-user-select',
          prop: 'assistant',
          value: form.value.assistUser,
        },
        {
          label: '技术',
          prop: 'technicalPersonnel',
          component: 'wf-user-select',
          value: form.value.technologyUser,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/sealContract/update', {
          ...res.data,
          id: form.value.id,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getDetail();
        });
    },
  });
}
const activeName = ref('baseInfo');
</script>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/common/var.scss' as *;
.header {
  display: flex;
  margin-left: 20px;
  justify-content: space-between;
  align-items: flex-start;
}

.title {
  margin: 0;
  margin-right: 20px;
}
.left_content {
  .main_box {
    margin-right: 10px;
    .item {
      width: 100px;
      cursor: pointer;
      background-color: #fff;
      box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.1);
      text-align: center;
      line-height: 50px;
      font-weight: bolder;
      height: 50px;
      font-size: 12px;
      margin-bottom: 10px;
      transition: all 0.2s;
      position: relative;
    }
    .item.active {
      box-shadow: inset 5px 5px 10px rgba(0, 0, 0, 0.1);
      color: $color-primary;
      .arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -12px;
        border: 6px solid transparent;
        border-left-color: $color-primary;
        height: 0;
        width: 0;
      }
    }
  }
}
</style>

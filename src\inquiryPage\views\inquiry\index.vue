<template>
  <div style="background-color: #eee; height: 100vh; overflow-y: scroll">
    <div class="wrap">
      <div class="header">
        <h4 style="padding: var(--van-contact-card-padding); text-align: center">
          询价单{{ detailForm.inquiryCode }}
          <Tag
            :type="
              detailForm.inquiryStatus == 1
                ? 'warning'
                : detailForm.inquiryStatus == 2
                ? 'success'
                : 'info'
            "
            >{{ ['待提交', '进行中', '已完成', '已过期'][detailForm.inquiryStatus] }}</Tag
          >
        </h4>
        <h5 style="text-align: center">{{}}</h5>
        <!-- <div>
          <div style="margin-bottom: 5px">
            <h6 style="padding: var(--van-contact-card-padding); padding-bottom: 0; padding-top: 0">
              深圳市非常聚成科技有限公司
            </h6>

            <ContactCard
              type="edit"
              style="border-radius: 10px"
              :name="detailForm.contactName"
              :tel="detailForm.contactPhone"
              :editable="false"
            />
          </div>
          <div>
            <h6 style="padding: var(--van-contact-card-padding); padding-bottom: 0; padding-top: 0">
              {{ detailForm.supplierName || '' }}
            </h6>

            <ContactCard
              type="edit"
              style="border-radius: 10px"
              :name="detailForm.supplierContactName || ''"
              :tel="detailForm.supplierContactPhone || ''"
              :editable="false"
            />
          </div>
        </div> -->
      </div>
    </div>
    <div class="wrap_body">
      <!-- <cell v-for="(item, index) in detailForm.detailVOS">
        <div style="display: flex; justify-content: space-between; align-items: center">
          <Card
            @click="handleClick(item, index)"
            :num="item.number"
            :tag="index + 1"
            style="width: 100%"
            :price="!item.isStop ? item.inquiryPrice : ''"
            :desc="item.productSpecification"
            :title="item.productName"
            :thumb="item.coverUrl || '1'"
          >
            <template #tags>
              <Tag
                plain
                :type="item.isStop == 1 ? 'danger' : item.inquiryPrice ? 'success' : 'warning'"
                >{{ item.isStop == 1 ? '停产' : item.inquiryPrice ? '已报价' : '未报价' }}</Tag
              >
            </template>
            <template #footer>
              <span>小计：</span
              ><span style="color: var(--van-red); font-size: 16px">
                ￥{{ !item.isStop ? item.number * item.inquiryPrice || '' : '' }}</span
              >
            </template>
          </Card>
        </div>
      </cell> -->
      <Swipe vertical :loop="false" ref="swipeRef" style="height: 100%;">
        <SwipeItem v-for="item in detailForm.detailVOS">
          <div @touchmove="touchMove" @scroll="scrollChange"   class="form_box">
            <Form  >
              <FormItem label-position="right" label="产品名称">
                {{ item.productName }}
              </FormItem>
              <FormItem label-position="right" label="规格型号">
                {{ item.productSpecification }}
              </FormItem>
              <FormItem label-position="right" label="数量">
                {{ item.number }}
              </FormItem>
              <FormItem label-position="right" label="产品价格">
                <Input
                  placeholder="请输入报价"
                  allowClear
                  :disabled="detailForm.inquiryStatus == 2"
                  v-model="item.inquiryPrice"
                  type="number"
                ></Input>
              </FormItem>
              <FormItem label-position="right" label="质保期(年)">
                <!-- <Input
            placeholder="请输入质保期"
            allowClear
            :disabled="detailForm.inquiryStatus == 2"
            v-model="form.guaranteePeriod"
            type="number"
          ></Input> -->
                <div>
                  <div>
                    <RadioGroup
                      :disabled="detailForm.inquiryStatus == 2"
                      v-model="item.guaranteePeriodType"
                      checked-color="var(--van-tag-success-color)"
                      direction="horizontal"
                    >
                      <Radio name="1">1年</Radio>
                      <Radio name="2">2年</Radio>
                      <Radio name="3">3年</Radio>
                      <Radio name="4">其他</Radio>
                    </RadioGroup>
                  </div>
                  <div>
                    <Input
                      v-if="item.guaranteePeriodType == 4"
                      placeholder="请输入质保期"
                      allowClear
                      :disabled="detailForm.inquiryStatus == 2"
                      v-model="item.guaranteePeriod"
                      type="number"
                    ></Input>
                  </div>
                </div>
              </FormItem>
              <FormItem label-position="right" label="供货周期(天)">
                <!-- <Input
            placeholder="请输入质保期"
            allowClear
            :disabled="detailForm.inquiryStatus == 2"
            v-model="form.guaranteePeriod"
            type="number"
          ></Input> -->
                <div>
                  <div>
                    <RadioGroup
                      :disabled="detailForm.inquiryStatus == 2"
                      v-model="item.deliveryCycle"
                      checked-color="var(--van-tag-success-color)"
                      direction="horizontal"
                    >
                      <Radio :name="0">现货</Radio>
                      <Radio :name="1">自定义</Radio>
                    </RadioGroup>
                  </div>
                  <div>
                    <Input
                      v-if="item.deliveryCycle == 1"
                      placeholder="请输入供货周期(天)"
                      allowClear
                      :disabled="detailForm.inquiryStatus == 2"
                      v-model="item.deliveryCycleDays"
                      type="number"
                    ></Input>
                  </div>
                </div>
              </FormItem>
              <FormItem label-position="right" label="是否含税">
                <Switch v-model="item.isHasTax" :disabled="detailForm.inquiryStatus == 2" />
              </FormItem>
              <FormItem label-position="right" label="税率">
                <Picker
                  :disabled="detailForm.inquiryStatus == 2"
                  v-model="item.taxRate"
                  :options="[
                    { value: 1, label: '1%' },
                    { value: 3, label: '3%' },
                    { value: 6, label: '6%' },
                    { value: 9, label: '9%' },
                    { value: 13, label: '13%' },
                  ]"
                ></Picker>
              </FormItem>

              <FormItem label-position="right" label="是否停产">
                <Switch v-model="item.isStop" :disabled="detailForm.inquiryStatus == 2" />
              </FormItem>
              <FormItem label-position="top" label="备注">
                <Textarea
                  placeholder="请输入备注"
                  allowClear
                  :disabled="detailForm.inquiryStatus == 2"
                  v-model="item.remark"
                  type="textarea"
                ></Textarea>
              </FormItem>
            </Form>
          </div>
        </SwipeItem>
        <template #indicator="{ active, total }">
          <div class="custom-indicator">{{ active + 1 }}/{{ total }}</div>
        </template>
      </Swipe>
      <!-- <Divider content-position="right" :style="{ margin: 0 }"
        >总计：<span style="color: var(--van-red)">{{ totalPrice }}</span></Divider
      > -->
    </div>

    <div
      style="
        position: fixed;
        left: 50%;
        bottom: 0px;
        transform: translateX(-50%);

        background-color: #fff;
        width: 100vw;
        height: 60px;
        display: flex;
        align-items: center;
      "
    >
      <div>
        <span style="color: var(--van-danger-color)">总计：</span
        >{{ (totalPrice * 1).toLocaleString() }}
      </div>
      <Button
        size="large"
        @click="detailForm.dialogVisible = true"
        v-if="detailForm.inquiryStatus == 1"
        type="primary"
        >提交</Button
      >
    </div>
  </div>
  <HalfScreenDialog title="信息填写" v-model="dialogVisible">
    <Form>
      <FormItem label-position="right" label="产品名称">
        {{ form.productName }}
      </FormItem>
      <FormItem label-position="right" label="规格型号">
        {{ form.productSpecification }}
      </FormItem>
      <FormItem label-position="right" label="产品价格">
        <Input
          placeholder="请输入报价"
          allowClear
          :disabled="detailForm.inquiryStatus == 2"
          v-model="form.inquiryPrice"
          type="number"
        ></Input>
      </FormItem>
      <FormItem label-position="right" label="质保期(年)">
        <!-- <Input
            placeholder="请输入质保期"
            allowClear
            :disabled="detailForm.inquiryStatus == 2"
            v-model="form.guaranteePeriod"
            type="number"
          ></Input> -->
        <div>
          <div>
            <RadioGroup
              :disabled="detailForm.inquiryStatus == 2"
              v-model="form.guaranteePeriodType"
              checked-color="var(--van-tag-success-color)"
              direction="horizontal"
            >
              <Radio name="1">1年</Radio>
              <Radio name="2">2年</Radio>
              <Radio name="3">3年</Radio>
              <Radio name="4">其他</Radio>
            </RadioGroup>
          </div>
          <div>
            <Input
              v-if="form.guaranteePeriodType == 4"
              placeholder="请输入质保期"
              allowClear
              :disabled="detailForm.inquiryStatus == 2"
              v-model="form.guaranteePeriod"
              type="number"
            ></Input>
          </div>
        </div>
      </FormItem>
      <FormItem label-position="right" label="供货周期(天)">
        <!-- <Input
            placeholder="请输入质保期"
            allowClear
            :disabled="detailForm.inquiryStatus == 2"
            v-model="form.guaranteePeriod"
            type="number"
          ></Input> -->
        <div>
          <div>
            <RadioGroup
              :disabled="detailForm.inquiryStatus == 2"
              v-model="form.deliveryCycle"
              checked-color="var(--van-tag-success-color)"
              direction="horizontal"
            >
              <Radio :name="0">现货</Radio>
              <Radio :name="1">自定义</Radio>
            </RadioGroup>
          </div>
          <div>
            <Input
              v-if="form.deliveryCycle == 1"
              placeholder="请输入供货周期(天)"
              allowClear
              :disabled="detailForm.inquiryStatus == 2"
              v-model="form.deliveryCycleDays"
              type="number"
            ></Input>
          </div>
        </div>
      </FormItem>
      <FormItem label-position="right" label="是否含税">
        <Switch v-model="form.isHasTax" :disabled="detailForm.inquiryStatus == 2" />
      </FormItem>
      <FormItem label-position="right" label="税率">
        <Picker
          :disabled="detailForm.inquiryStatus == 2"
          v-model="form.taxRate"
          :options="[
            { value: 1, label: '1%' },
            { value: 3, label: '3%' },
            { value: 6, label: '6%' },
            { value: 9, label: '9%' },
            { value: 13, label: '13%' },
          ]"
        ></Picker>
      </FormItem>
      <FormItem label-position="right" label="报价日期">
        <DatePicker disabled v-model="form.offerDate" />
      </FormItem>
      <FormItem label-position="right" label="是否停产">
        <Switch v-model="form.isStop" :disabled="detailForm.inquiryStatus == 2" />
      </FormItem>
      <FormItem label-position="top" label="备注">
        <Textarea
          placeholder="请输入备注"
          allowClear
          :disabled="detailForm.inquiryStatus == 2"
          v-model="form.remark"
          type="textarea"
        ></Textarea>
      </FormItem>
    </Form>
    <template #extra>
      <!-- <Button
        type="info" style="margin-right: 5px;"
        @click="handleStop"
        :loading="form.loading"
        :disabled="form.loading"
        size="mini"
        v-if="detailForm.inquiryStatus == 1"
        >停产</Button
      > -->
      <Button
        type="primary"
        @click="handleSubmit"
        :loading="form.loading"
        :disabled="form.loading"
        size="mini"
        v-if="detailForm.inquiryStatus == 1"
        >保存</Button
      ></template
    >
  </HalfScreenDialog>
  <Dialog
    v-model="detailForm.dialogVisible"
    title="提交询价单"
    @ok="handleConfirm"
    desc="是否确认提交询价单？提交后将不可修改"
  ></Dialog>
</template>

<script setup>
import {
  Button,
  Form,
  FormItem,
  Input,
  Cell,
  Switch,
  DatePicker,
  HalfScreenDialog,
  Picker,
  Toast,
  Dialog,
  Textarea,
} from 'vue-weui-next';
import { Card, ContactCard, Tag, RadioGroup, Radio, Field, Divider, Swipe, SwipeItem } from 'vant';
import { computed, onMounted, onBeforeMount, nextTick } from 'vue';
import axios from 'axios';
import moment from 'moment';
import wx from 'weixin-js-sdk';
let dialogVisible = ref(false);
let loading = ref(true);
let form = ref({
  isHasTax: true,
  taxRate: 13,
  offerDate: moment().format('YYYY-MM-DD'),
});
const getQueryString = name => {
  let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
  let r = window.location.search.substr(1).match(reg);
  if (r != null) return unescape(decodeURI(r[2]));
  return null;
};
let detailForm = ref({
  detailVOS: [],
  dialogVisible: false,
});
onBeforeMount(() => {
  axios
    .get('/api/vt-admin/purchaseInquiry/getWxShareSian/' + getQueryString('id') + '?type=0')
    .then(res => {
      console.log(res);
      const { appId, nonceStr, signature, timestamp } = res.data.data;
    
      wx.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId, // 必填，公众号的唯一标识
        timestamp, // 必填，生成签名的时间戳
        nonceStr, // 必填，生成签名的随机串
        signature, // 必填，签名
        jsApiList: ['updateAppMessageShareData'], // 必填，需要使用的JS接口列表
      });
    });

  wx.ready(() => {
    const title = `询价单(${detailForm.value.detailVOS[0].createTime})`;
    const origin = window.location.origin;
    const imgUrl = `${origin}/img/logo2.png`;
    const link = window.location.href;
  
    wx.updateAppMessageShareData({
      title: title, // 分享标题
      desc: detailForm.value.detailVOS[0].productName,
      link, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
      imgUrl, // 分享图标
      success: function () {},
    });
  });
});
onMounted(() => {
  getDetail();
});
let swipeRef = ref();
function getDetail() {
  const hide = Toast.toast({
    text: '加载中...',
    type: 'loading',
    //   duration: 0,
  });
  axios
    .get(`/api/vt-admin/purchaseInquiry/detail/${getQueryString('id')}`)
    .then(res => {
      if (res.data.code == 200) {
        detailForm.value = {
          ...res.data.data,
          detailVOS: res.data.data.detailVOS.map(item => {
            return {
              ...item,
              isHasTax: true,
              taxRate: item.taxRate ? item.taxRate * 1 : 13,
              offerDate: item.offerDate,
              isStop: item.isStop,
              guaranteePeriodType: item.guaranteePeriod * 1 >= 4 ? '4' : item.guaranteePeriod,
              guaranteePeriod: item.guaranteePeriod,
            };
          }),
        };
        hide();
        nextTick(() => {
          animate();
        });
      } else {
        Toast.toast({
          text: res.data.msg,
          type: 'warn',
        });
      }
    })
    .catch(err => {
      Toast.toast({
        text: err.message,
        type: 'warn',
      });
    });
}
function animate() {
  swipeRef.value.next();
  setTimeout(() => {
    swipeRef.value.prev();
  }, 1000);
}
function handleClick(item, index) {

  form.value = {
    ...item,
    isHasTax: true,
    taxRate: item.taxRate ? item.taxRate * 1 : 13,
    offerDate: item.offerDate,
    isStop: item.isStop,
    guaranteePeriodType: item.guaranteePeriod * 1 >= 4 ? '4' : item.guaranteePeriod,
    guaranteePeriod: item.guaranteePeriod,
    index: index,
    offerDate: moment().format('YYYY-MM-DD'),
  };

  dialogVisible.value = true;
}
function handleSubmit() {
  detailForm.value.detailVOS[form.value.index].offerDate = null;
  detailForm.value.detailVOS[form.value.index].isHasTax = form.value.isHasTax ? 1 : 0;
  detailForm.value.detailVOS[form.value.index].isStop = form.value.isStop ? 1 : 0;
  detailForm.value.detailVOS[form.value.index].inquiryPrice = form.value.inquiryPrice;
  detailForm.value.detailVOS[form.value.index].taxRate = form.value.taxRate;

  detailForm.value.detailVOS[form.value.index].remark = form.value.remark;
  detailForm.value.detailVOS[form.value.index].deliveryCycle = form.value.deliveryCycle;
  detailForm.value.detailVOS[form.value.index].deliveryCycleDays = form.value.deliveryCycleDays;
  detailForm.value.detailVOS[form.value.index].guaranteePeriodType = form.value.guaranteePeriodType;
  detailForm.value.detailVOS[form.value.index].guaranteePeriod =
    form.value.guaranteePeriodType == 4
      ? form.value.guaranteePeriod
      : form.value.guaranteePeriodType;
  dialogVisible.value = false;
  // axios
  //   .post('/api/vt-admin/purchaseInquiry/submit', {
  //     ...detailForm.value,

  //     detailDTOList: [
  //       { ...form.value, offerDate: null, createTime: null, isHasTax: form.value.isHasTax ? 1 : 0,guaranteePeriod: form.value.guaranteePeriodType == 4? form.value.guaranteePeriod : form.value.guaranteePeriodType },
  //     ],
  //   })
  //   .then(res => {
  //     dialogVisible.value = false;
  //     getDetail();
  //     Toast.toast({
  //       text: '操作成功',
  //       type: 'success',
  //       duration: 10,
  //     });
  //   });
}
function name(params) {}
function handleConfirm() {
  axios
    .post('/api/vt-admin/purchaseInquiry/submit', {
      ...detailForm.value,
      inquiryStatus: 2,

      detailDTOList: detailForm.value.detailVOS.map(item => {
        return {
          ...item,
          isHasTax: item.isHasTax ? 1 : 0,
          isStop: item.isStop ? 1 : 0,
          inquiryPrice: item.inquiryPrice,
          taxRate: item.taxRate,
          remark: item.remark,
          deliveryCycle: item.deliveryCycle,
          deliveryCycleDays: item.deliveryCycleDays,
          guaranteePeriodType: item.guaranteePeriodType,
          guaranteePeriod:
            item.guaranteePeriodType == 4 ? item.guaranteePeriod : item.guaranteePeriodType,
        };
      }),
    })
    .then(res => {
      dialogVisible.value = false;
      getDetail();
      Toast.toast({
        text: '操作成功',
        type: 'success',
      });
    });
}
const totalPrice = computed(() => {
  return detailForm.value.detailVOS
    .filter(item => !!item.inquiryPrice)
    .reduce((pre, cur) => {
      pre = (pre + cur.inquiryPrice * 1 * (cur.number * 1) * 1) * 1;
      return pre;
    }, 0)
    .toFixed(2);
});
let isStopPropagation = ref(false);
function touchMove(e) {
  if (!isStopPropagation.value) {
    e.stopPropagation();
  } else {
  }
}
function scrollChange(e) {
  isScrollAtBottom(e.target);
}
function isScrollAtBottom(element) {
  isStopPropagation.value = element.scrollHeight - (element.scrollTop + element.clientHeight) <= 1;
}
</script>

<style lang="scss" scoped>
:deep(.weui-form__control-area) {
  margin-top: 5px;
}
.title {
  font-weight: bolder;
  font-size: 14px;
}
.sub_title {
  color: #111;
  font-size: 12px;
}
.wrap {
  padding: 10px;
  box-sizing: border-box;
}
.header {
  //   height: 100px;
  border-radius: 10px;
  background-color: #fff;
}
.wrap_body {
  // background-color: #fff;
  margin: 0 10px;
  border-radius: 10px;
  height: calc(100% - 150px);
  overflow-y: scroll;
  // margin-bottom: 100px;
}
:deep(.van-card__num) {
  font-size: 16px;
}
:deep(.van-contact-card) {
  padding-top: 5px;
}
:deep(.weui-form__control-area) {
  margin-bottom: 0;
}
.custom-indicator {
  position: absolute;
  right: 5px;
  top: 5px;
  padding: 2px 5px;
  font-size: 12px;
  background: rgba(0, 0, 0, 0.1);
}
.form_box {
  height: calc(100%);
  overflow-y: scroll;
}
</style>

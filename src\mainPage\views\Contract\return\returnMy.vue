<template>
  <basic-container block :shadow="sealContractId ? 'never' : 'always'">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #tip-form>
        <el-alert
          show-icon
          type="warning"
          description="变更将会删除计划收款和未开票的开票申请（已开票的请自行申请作废），请变更后重新申请开票和添加计划收款"
          :closable="false"
        ></el-alert>
      </template>
      <template #menu="{ row }">
        <el-button
          type="primary"
          @click="$refs.crud.rowEdit(row, index)"
          text
          icon="edit"
          v-if="row.returnStatus == 0"
          >编辑</el-button
        >
        <!-- <el-button
          type="primary"
          v-if="row.returnStatus == 0"
          text
          icon="back"
          @click="confirmReturn(row)"
          >确认退货</el-button
        >
        <el-button
          type="primary"
          v-if="row.returnStatus == 1"
          @click="refundPrice(row)"
          text
          icon="back"
          >退款</el-button
        > -->
        <!-- <el-button type="primary" @click="$refs.crud.rowDel(row,index)" text icon="delete">删除</el-button> -->
      </template>
      <template #returnCode="{ row }">
        <el-link type="primary" @click="$refs.crud.rowView(row)">{{ row.returnCode }}</el-link>
      </template>
      <template #returnStatus="{ row }">
        <el-tag
          :type="row.returnStatus == 0 ? 'info' : row.returnStatus == 1 ? 'warning' : 'success'"
          effect="plain"
          >{{ row.$returnStatus }}</el-tag
        >
      </template>
      <template #refundFile-form="{ type }">
        <File :fileList="form.attachList"></File>
      </template>
      <template #sealContractId="{ row }">
        <el-link type="primary" @click="toDetail(row)">{{ row.sealContractName }}</el-link>
      </template>
      <template #operate-form="{ row,type }">
        <el-button type="primary" v-if="type != 'view'" @click="replacePro(row)" text>替换</el-button>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <productSelect
      :offer-id="props.offerId || form.offerId"
      @confirm="handleConfirm"
      ref="productSelectRef"
    ></productSelect>
    <wfProductSelect
      ref="wfProductSelectRef"
      @onConfirm="handleConfirmForReplace"
    ></wfProductSelect>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import productSelect from '@/views/Order/salesOrder/compoents/productSelect.vue';
import wfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import { ElMessage } from 'element-plus';
import { dateFormat } from '@/utils/date';
let productSelectRef = ref();
const props = defineProps(['offerId', 'sealContractId']);
let wfProductSelectRef = ref(null);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 100,
  labelWidth: 120,
  border: true,
  dialogClickModal: true,
  dialogType: 'drawer',
  dialogWidth: '80%',
  column: [
    {
      label: '提示',
      prop: 'tip',
      width: 100,
      
      viewDisplay: false,
      span: 24,
      labelWidth: 0,
      hide: true,
    },
    {
      label: '变更单编号',
      prop: 'returnCode',
      overHidden: true,

      disabled: true,
      width: 180,
      search: true,
      searchLabelWidth: 120,
    },
    {
      label: '合同名称',
      prop: 'sealContractName',
      hide: true,
      display: false,
      search: !props.sealContractId,
    },
    {
      type: 'input',
      label: '关联合同',
      display: true,
      rules: [
        {
          required: true,
          message: '请选择关联订单',
        },
      ],
      display: !props.sealContractId,
      hide: !!props.sealContractId,
      component: 'wf-contract-select',
      overHidden: true,
      prop: 'sealContractId',
      params: {
        Url: '/api/vt-admin/sealContract/pageForObject?selectType=5&contractType=0',
        checkType: 'radio',
      },
      formatter: (row, value) => {
        return row.sealContractName;
      },
      change: val => {
        handleChange(val.value);
      },
    },
    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true,
      disabled: true,
      component: 'wf-customer-drop',
      display: false,
      search: !props.sealContractId,
      hide: !!props.sealContractId,
    },
    {
      label: '变更金额',
      prop: 'refundPrice',
      type: 'number',
      width: 120,
      span: 12,
    },
    {
      label: '退货产品',
      prop: 'detailDTOList',
      type: 'dynamic',
      hide: true,
      rules: [
        {
          required: true,
          message: '请选择关联产品',
        },
      ],
      span: 24,
      children: {
        align: 'center',
        headerAlign: 'center',
        size: 'small',
        rowAdd: done => {
          productSelectRef.value.open();
          // done();
        },
        rowDel: (row, done) => {
          done();
         nextTick(() => {
           setReturnPrice()
         })
        },
        size: 'small',
        column: [
          {
            label: '产品名称',
            prop: 'productName',
            overHidden: true,
            bind: 'productVO.productName',
            cell: false,
          },

          {
            label: '规格型号',
            prop: 'productSpecification',
            overHidden: true,
            bind: 'productVO.productSpecification',
            // search: true,
            cell: false,
            span: 24,
            type: 'input',
          },
          {
            label: '品牌',
            prop: 'productBrand',
            overHidden: true,
            bind: 'productVO.productBrand',
            cell: false,
            width: 100,
          },   {
            label:'单位',
            prop: 'unitName',
              width: 80,
            cell:false,
             bind: 'productVO.unitName',
          },
          {
            label: '数量',
            prop: 'number',
            width: 120,
            type: 'number',
           
            span: 12,
            cell: true,
            blur: setReturnPrice,
          },
          {
            label: '单价',
            prop: 'sealPrice',
            type: 'number',
            span: 12,
            width: 100,
            cell: false,
          },
       
          {
            label: '金额',
            prop: 'totalPrice',
            type: 'number',
            span: 12,
            width: 100,
            cell: false,
            formatter: row => {
              return (row.number * row.sealPrice).toFixed(2);
            },
          },
          {
            label: '退货单价',
            prop: 'returnPrice',
            type: 'number',
            span: 12,
            width: 120,
            cell: true,
            blur: setReturnPrice,
          },
          {
            label: '退货金额',
            prop: 'refundTotal',
            type: 'number',
            span: 12,

            width: 120,
            cell: false,
            formatter: row => {
              return (row.number * row.returnPrice).toFixed(2);
            },
          },
          {
            label: '序列号',
            prop: 'serialNumber',
            span: 12,
            cell: true,

            width: 200,
            type: 'textarea',
          },
          {
            label:'操作',
            prop:'operate',
            cell: true,
            width: 120,
          }
        ],
      },
    },
    {
      label: '换货产品',
      prop: 'replaceDTOList',
      type: 'dynamic',
      hide: true,
      span: 24,
      children: {
        align: 'center',
        headerAlign: 'center',
        size: 'small',
        addBtn: false,
        
        rowDel: (row, done) => {
          done();
            setTimeout(() => {
             setReturnPrice()
          }, 100);
        },
        size: 'small',
        column: [
          {
            label: '产品名称',
            prop: 'productName',
            overHidden: true,
            bind: 'productVO.productName',
            cell: false,
          },

          {
            label: '规格型号',
            prop: 'productSpecification',
            overHidden: true,
            bind: 'productVO.productSpecification',
            // search: true,
            cell: false,
            span: 24,
            type: 'input',
          },
          {
            label: '品牌',
            prop: 'productBrand',
            overHidden: true,
            bind: 'productVO.productBrand',
            cell: false,
            width: 100,
          },
           {
            label:'单位',
            prop: 'unitName',
            cell:false,
            width: 80,
             bind: 'productVO.unitName',
          },
          {
            label: '数量',
            prop: 'number',
            width: 120,
            type: 'number',
            span: 12,
            blur: setReturnPrice,
            cell: true,
            rules: [
              {
                required: true,
                message: '请输入数量',
              },
            ],
          },

          {
            label: '单价',
            prop: 'sealPrice',
            type: 'number',
            span: 12,
            blur: setReturnPrice,
            rules: [
              {
                required: true,
                message: '请输入单价',
              },
            ],
            width: 130,
            cell: true,
          },
          {
            label: '金额',
            prop: 'refundTotal',
            type: 'number',
            span: 12,

            width: 120,
            cell: false,
            formatter: row => {
              return row.number * row.sealPrice;
            },
          },
        ],
      },
    },
    {
      label: '变更备注',
      prop: 'returnReason',
      type: 'textarea',
      span: 24,
      overHidden: true,
    },

    {
      label: '变更状态',
      prop: 'returnStatus',
      display: false,
      type: 'select',
      search: true,
      width:100,
      dicData: [
        {
          value: 0,
          label: '退货中',
        },
        {
          value: 1,
          label: '已退货',
        },
     
      ],
    },
    // {
    //   label: '创建人',
    //   prop: 'businessUserName',
    //   display: false,
    //   width:100,
    // },
    // {
    //   label: '联系电话',
    //   prop: 'followPerson',
    // },
  ],
  group: [
    {
      label: '退货信息',
      prop: 'rereturnInfo',
      addDisplay: false,
      editDisplay: false,
      column: [
        {
          label: '退货时间',
          prop: 'returnDate',
          type: 'date',
          format: 'YYYY-MM-DD',
          span: 24,
          valueFormat: 'YYYY-MM-DD',
        },
        {
          label: '备注',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    // {
    //   label: '退款信息',
    //   prop: 'refundInfo',
    //   addDisplay: false,
    //   editDisplay: false,
    //   column: [
    //     {
    //       label: '退款金额',
    //       prop: 'refundPrice',
    //       rules: [
    //         {
    //           required: true,
    //           message: '请输入退款金额',
    //         },
    //       ],
    //     },
    //     {
    //       label: '退款时间',
    //       prop: 'refundDate',
    //       type: 'date',
    //       format: 'YYYY-MM-DD',
    //       valueFormat: 'YYYY-MM-DD',
    //     },
    //     {
    //       label: '退款附件',
    //       prop: 'refundFile',
    //       type: 'upload',
    //       span: 24,

    //       // rules: [
    //       //   {
    //       //     required: true,
    //       //     validator: validatorPath,
    //       //     trigger: "change",
    //       //   },
    //       // ],
    //       dataType: 'object',
    //       propsHttp: {
    //         res: 'data',
    //         url: 'id',
    //         name: 'originalName',
    //       },
    //       action: '/api/blade-resource/attach/upload',
    //     },
    //   ],
    // },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/sealsReturn/save';
const delUrl = '';
const updateUrl = '/api/vt-admin/sealsReturn/update';
const tableUrl = '/api/vt-admin/sealsReturn/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        sealContractId: props.sealContractId,
        selectType: 0,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    refundFile: null,
    sealContractId: props.sealContractId || form.sealContractId,
    detailDTOList: [
      ...form.detailDTOList.map(item => {
        return {
          ...item,
          detailId: item.id,
          id: null,
        };
      }),
      ...form.replaceDTOList.map((item) => {
        return {
          ...item,
          id: null,
        };
      }),
    ],
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    refundFile: null,
    detailDTOList: [
      ...row.detailDTOList.map(item => {
        return {
          ...item,
          detailId: item.id,
          id: null,
        };
      }),
      ...row.replaceDTOList.map((item) => {
        return {
          ...item,
          id: null,
        };
      }),
    ],
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function handleConfirm(list) {
  console.log(list);

  form.value.detailDTOList = list.map(item => {
    return {
      ...item,
      type: 0,
      productVO: item.product,
      sealPrice: item.zhhsdj,
      returnPrice: item.sealPrice,
    };
  });
  setReturnPrice()
}
function totalAmount(val) {
  //  console.log(val);
  // form.value.invoicePrice =  form.value.detailDTOList.reduce((total, item) => (total += item.totalPrice * 1), 0) * (1 - (val * 1) / 100)
  return form.value.detailDTOList
    .reduce((total, item) => (total += item.number * 1 * (item.returnPrice * 1)), 0)
    .toFixed(2);
}
function handleChange(value) {
  axios
    .get('/api/vt-admin/sealContract/detail', {
      params: {
        id: value,
      },
    })
    .then(res => {
      form.value.offerId = res.data.data.offerId;
    });
}
function beforeOpen(done, type) {
  if (['edit', 'view'].includes(type)) {
    axios.get('/api/vt-admin/sealsReturn/detail', { params: { id: form.value.id } }).then(res => {
      form.value = {
        ...res.data.data,
        detailDTOList: res.data.data.detailVOList.filter(item => item.type == 0).map(item => {
          return {
            ...item,
            number: item.number * 1,
            id: item.detailId,
          };
        }),
        replaceDTOList: res.data.data.detailVOList.filter(item => item.type == 1).map(item => {
          return {
           ...item,
           replaceDetailId :item.replaceDetailId,
           id:null,
          };
        }),
      };
    });
  }

  done();
}
function confirmReturn(row) {
  axios.get('/api/vt-admin/sealsReturn/detail', { params: { id: row.id } }).then(res => {
    const detailDTOList = res.data.data.detailVOList.map(item => {
      return {
        ...item,
        number: item.number * 1,
        id: item.detailId,
      };
    });
    proxy.$refs.dialogForm.show({
      title: '确认退货',
      width: '50%',
      option: {
        column: [
          // {
          //   label: '关联产品',
          //   prop: 'detailDTOList',
          //   type: 'dynamic',
          //   labelWidth: 0,
          //   value: detailDTOList,
          //   hide: true,

          //   span: 24,
          //   children: {
          //     align: 'center',
          //     headerAlign: 'center',
          //     rowAdd: done => {
          //       productSelectRef.value.open();
          //       // done();
          //     },
          //     rowDel: (row, done) => {
          //       done();
          //     },

          //     column: [
          //       {
          //         label: '产品名称',
          //         prop: 'productName',
          //         overHidden: true,
          //         bind: 'productVO.productName',
          //         cell: false,
          //       },

          //       {
          //         label: '规格型号',
          //         prop: 'productSpecification',
          //         overHidden: true,
          //         bind: 'productVO.productSpecification',
          //         // search: true,
          //         cell: false,
          //         span: 24,
          //         type: 'input',
          //       },
          //       {
          //         label: '品牌',
          //         prop: 'productBrand',
          //         overHidden: true,
          //         bind: 'productVO.productBrand',
          //         cell: false,
          //         width: 120,
          //       },
          //       {
          //         label: '数量',
          //         prop: 'number',
          //         type: 'number',
          //         span: 12,
          //         cell: true,
          //         width: 120,
          //       },
          //       {
          //         label: '单价',
          //         prop: 'sealPrice',
          //         type: 'number',
          //         span: 12,
          //         cell: false,
          //         width: 100,
          //       },
          //       {
          //         label: '金额',
          //         prop: 'totalPrice',
          //         type: 'number',
          //         span: 12,
          //         width: 100,
          //         cell: false,
          //         formatter: row => {
          //           return row.number * row.sealPrice;
          //         },
          //       },
          //       {
          //         label: '序列号',
          //         prop: 'serialNumber',
          //         span: 12,
          //         cell: true,
          //         row: 1,
          //         width: 200,
          //         type: 'textarea',
          //       },
          //     ],
          //   },
          // },
          {
            label: '退货时间',
            prop: 'returnDate',
            type: 'date',
            format: 'YYYY-MM-DD',
            span: 24,
            valueFormat: 'YYYY-MM-DD',
            value: dateFormat(new Date(), 'yyyy-MM-dd'),
          },
          {
            label: '备注',
            type: 'textarea',
            span: 24,
            prop: 'remark',
          },
        ],
      },
      callback(res) {
        axios
          .post('/api/vt-admin/sealsReturn/confirmReturn', {
            ...row,
            ...res.data,
          })
          .then(ref => {
            ElMessage.success('操作成功');
            onLoad();
            res.close();
          });
      },
    });
  });
}
function refundPrice(row) {
  proxy.$refs.dialogForm.show({
    title: '退款',
    option: {
      column: [
        {
          label: '退款金额',
          prop: 'refundPrice',
          value: row.refundPrice,
          rules: [
            {
              required: true,
              message: '请输入退款金额',
            },
          ],
        },
        {
          label: '退款时间',
          prop: 'refundDate',
          type: 'date',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          value: dateFormat(new Date(), 'yyyy-MM-dd'),
        },
        {
          label: '退款附件',
          prop: 'refundFile',
          type: 'upload',
          span: 24,

          // rules: [
          //   {
          //     required: true,
          //     validator: validatorPath,
          //     trigger: "change",
          //   },
          // ],
          dataType: 'object',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
          },
          action: '/api/blade-resource/attach/upload',

          viewDisplay: false,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/sealsReturn/refundPrice', {
          id: row.id,
          ...res.data,
          refundFile: res.data.refundFile && res.data.refundFile.map(item => item.value).join(','),
        })
        .then(r => {
          ElMessage.success('操作成功');
          res.close();
          onLoad();
        });
    },
  });
}
function toDetail(row) {
  router.push({
    path: '/Contract/customer/compoents/detail',
    query: {
      id: row.sealContractId,
      delBtn: 1,
    },
  });
}
function handleConfirmForReplace(ids) {
  axios.get('/api/vt-admin/product/detail?id=' + ids).then(res => {
    form.value.replaceDTOList.push({
      replaceDetailId: replaceDetailId.value,
      type: 1,
      productId: ids,
      productVO: res.data.data,
    });
  });
}
function setReturnPrice() {
  // 计算退货产品的总金额
  const returnTotal =
    form.value.detailDTOList?.reduce(
      (total, item) => (total += item.number * item.returnPrice),
      0
    ) || 0;
  // 计算换货产品的总金额
  const replaceTotal =
    form.value.replaceDTOList?.reduce(
      (total, item) => (total += item.number * item.sealPrice),
      0
    ) || 0;
  // 设置form的refundPrice为退货产品的总金额 - 换货产品的总金额
  form.value.refundPrice = (replaceTotal - returnTotal  ).toFixed(2);
}
let replaceDetailId = ref(null);
function replacePro(row) {
  replaceDetailId.value = row.id;
  wfProductSelectRef.value.visible = true;
}
</script>

<style lang="scss" scoped></style>
